# Import all admin classes from the organized modules

# Users admin
from .users import CustomUserAdmin

# Profiles admin
from .profiles import (
    CustomerProfileAdmin,
    ServiceProviderProfileAdmin,
)

# Security admin
from .security import (
    LoginHistoryAdmin,
    LoginAlertAdmin,
)

# Team admin
from .team import TeamMemberAdmin

# Privacy admin
from .privacy import (
    ProfilePrivacySettingsAdmin,
    ProfileVerificationAdmin,
)

# Make all admin classes available at package level
__all__ = [
    'CustomUserAdmin',
    'CustomerProfileAdmin',
    'ServiceProviderProfileAdmin',
    'LoginHistoryAdmin',
    'LoginAlertAdmin',
    'TeamMemberAdmin',
    'ProfilePrivacySettingsAdmin',
    'ProfileVerificationAdmin',
] 