# Django Core Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
LOG_LEVEL=INFO

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/cozywish_db

# Email Configuration (SendGrid)
EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=apikey
EMAIL_HOST_PASSWORD=your-sendgrid-api-key
DEFAULT_FROM_EMAIL=<EMAIL>
SERVER_EMAIL=<EMAIL>
FORCE_EMAIL_BACKEND=False

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-s3-bucket-name
AWS_S3_REGION_NAME=us-east-1
AWS_S3_CUSTOM_DOMAIN=your-custom-domain.com

# Business Logic
PLATFORM_FEE_RATE=0.05
DASHBOARD_CACHE_TIMEOUT=300
NOTIFICATION_CACHE_TIMEOUT=60

# Development Settings
ENABLE_TEST_VIEW=True
