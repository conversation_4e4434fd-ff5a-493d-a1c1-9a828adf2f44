"""
Basic tests for customer views.
"""

import pytest
from django.contrib.auth import get_user_model
from django.test import TestCase, Client
from django.urls import reverse

from accounts_app.constants import UserRoles, UserStatus
from accounts_app.models import CustomerProfile

User = get_user_model()


class CustomerAuthenticationViewTests(TestCase):
    """Test cases for customer authentication views"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.customer_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER,
            status=UserStatus.ACTIVE,
            email_verified=True
        )

    def test_customer_login_view_get(self):
        """Test customer login view GET request"""
        url = reverse('accounts_app:customer_login')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)

    def test_customer_logout_view(self):
        """Test customer logout view"""
        # Login first
        self.client.login(email='<EMAIL>', password='testpass123')
        
        url = reverse('accounts_app:customer_logout')
        response = self.client.post(url)
        
        # Should redirect after logout
        self.assertEqual(response.status_code, 302)

    def test_customer_login_redirect_authenticated(self):
        """Test login view redirects authenticated users"""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        url = reverse('accounts_app:customer_login')
        response = self.client.get(url)
        
        # Should redirect authenticated users
        self.assertEqual(response.status_code, 302)


class CustomerProfileViewTests(TestCase):
    """Test cases for customer profile views"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER,
            status=UserStatus.ACTIVE,
            email_verified=True
        )
        
        self.profile = CustomerProfile.objects.create(
            user=self.user,
            first_name='John',
            last_name='Doe',
            phone_number='+**********'
        )

    def test_customer_profile_view_authenticated(self):
        """Test customer profile view for authenticated user"""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        url = reverse('accounts_app:customer_profile')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)

    def test_customer_profile_view_unauthenticated(self):
        """Test customer profile view for unauthenticated user"""
        url = reverse('accounts_app:customer_profile')
        response = self.client.get(url)
        
        # Should redirect to login
        self.assertEqual(response.status_code, 302)

    def test_customer_profile_edit_view_get(self):
        """Test customer profile edit view GET request"""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        url = reverse('accounts_app:customer_profile_edit')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)


class CustomerEmailVerificationViewTests(TestCase):
    """Test cases for customer email verification views"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER,
            email_verified=False
        )

    def test_email_verification_view(self):
        """Test email verification view"""
        url = reverse('accounts_app:email_verification', kwargs={'token': 'test_token'})
        response = self.client.get(url)
        
        # Should handle the request (may redirect or return 200)
        self.assertIn(response.status_code, [200, 302])

    def test_resend_verification_email_view(self):
        """Test resend verification email view"""
        url = reverse('accounts_app:resend_verification')
        response = self.client.get(url)
        
        # Should handle the request (may redirect or return 200)
        self.assertIn(response.status_code, [200, 302])

    def test_email_verification_success_view(self):
        """Test email verification success view"""
        url = reverse('accounts_app:email_verification_success')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)

    def test_email_verification_sent_view(self):
        """Test email verification sent view"""
        url = reverse('accounts_app:email_verification_sent')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)


@pytest.mark.django_db
class CustomerViewsPytestTests:
    """Pytest-style tests for customer views"""

    def test_customer_dashboard_view(self):
        """Test customer dashboard view"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER,
            email_verified=True
        )
        
        client = Client()
        client.login(email='<EMAIL>', password='testpass123')
        
        # Test profile view as dashboard
        url = reverse('accounts_app:customer_profile')
        response = client.get(url)
        assert response.status_code == 200

    def test_customer_view_permissions(self):
        """Test customer view permissions"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER,
            email_verified=True
        )
        
        client = Client()
        
        # Test unauthenticated access
        url = reverse('accounts_app:customer_profile')
        response = client.get(url)
        assert response.status_code == 302  # Should redirect to login
        
        # Test authenticated access
        client.login(email='<EMAIL>', password='testpass123')
        response = client.get(url)
        assert response.status_code == 200 