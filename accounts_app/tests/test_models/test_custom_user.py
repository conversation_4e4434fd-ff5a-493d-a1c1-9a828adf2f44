"""
Comprehensive tests for CustomUser model and related managers.
"""

import pytest
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.test import TestCase
from django.utils import timezone
from unittest.mock import patch

from accounts_app.constants import UserRoles, UserStatus
from accounts_app.models import CustomUser, UserProfile, UserSecurity

User = get_user_model()


class CustomUserModelTests(TestCase):
    """Test cases for CustomUser model functionality"""

    def setUp(self):
        self.user_data = {
            'email': '<EMAIL>',
            'password': 'testpass123',
            'role': UserRoles.CUSTOMER,
            'status': UserStatus.ACTIVE
        }

    def test_user_creation_with_email(self):
        """Test creating a user with email"""
        user = User.objects.create_user(**self.user_data)
        
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.role, UserRoles.CUSTOMER)
        self.assertEqual(user.status, UserStatus.ACTIVE)
        self.assertFalse(user.email_verified)
        self.assertTrue(user.check_password('testpass123'))

    def test_user_creation_without_email_raises_error(self):
        """Test that creating user without email raises ValueError"""
        with self.assertRaises(ValueError) as cm:
            User.objects.create_user(email='', password='testpass123')
        
        self.assertIn('The Email field must be set', str(cm.exception))

    def test_superuser_creation(self):
        """Test creating a superuser"""
        user = User.objects.create_superuser(
            email='<EMAIL>',
            password='adminpass123'
        )
        
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.role, UserRoles.ADMIN)
        self.assertEqual(user.status, UserStatus.ACTIVE)
        self.assertTrue(user.is_staff)
        self.assertTrue(user.is_superuser)
        self.assertTrue(user.is_active)
        self.assertTrue(user.email_verified)

    def test_superuser_creation_validates_staff_status(self):
        """Test that superuser creation validates staff status"""
        with self.assertRaises(ValueError) as cm:
            User.objects.create_superuser(
                email='<EMAIL>',
                password='adminpass123',
                is_staff=False
            )
        
        self.assertIn('Superuser must have is_staff=True', str(cm.exception))

    def test_superuser_creation_validates_superuser_status(self):
        """Test that superuser creation validates superuser status"""
        with self.assertRaises(ValueError) as cm:
            User.objects.create_superuser(
                email='<EMAIL>',
                password='adminpass123',
                is_superuser=False
            )
        
        self.assertIn('Superuser must have is_superuser=True', str(cm.exception))

    def test_email_uniqueness(self):
        """Test that email addresses must be unique"""
        User.objects.create_user(**self.user_data)
        
        with self.assertRaises(ValidationError):
            User.objects.create_user(**self.user_data)

    def test_email_case_insensitive(self):
        """Test that email addresses are normalized to lowercase"""
        # Create user with mixed case email
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Django's normalize_email converts domain to lowercase but preserves local part case
        self.assertEqual(user.email, '<EMAIL>')

    def test_string_representation(self):
        """Test string representation of user"""
        user = User.objects.create_user(**self.user_data)
        self.assertEqual(str(user), '<EMAIL>')

    def test_user_properties(self):
        """Test user model properties"""
        user = User.objects.create_user(**self.user_data)
        
        # Test role properties
        self.assertTrue(user.is_customer)
        self.assertFalse(user.is_service_provider)
        self.assertFalse(user.is_admin)
        
        # Test verification property
        self.assertFalse(user.is_verified)
        
        # Test active user property
        self.assertTrue(user.is_active_user)

    def test_admin_role_properties(self):
        """Test properties for admin user"""
        admin_user = User.objects.create_superuser(
            email='<EMAIL>',
            password='adminpass123'
        )
        
        self.assertTrue(admin_user.is_admin)
        self.assertFalse(admin_user.is_customer)
        self.assertFalse(admin_user.is_service_provider)
        self.assertTrue(admin_user.is_verified)

    def test_provider_role_properties(self):
        """Test properties for provider user"""
        provider_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.SERVICE_PROVIDER
        )
        
        self.assertTrue(provider_user.is_service_provider)
        self.assertFalse(provider_user.is_customer)
        self.assertFalse(provider_user.is_admin)

    def test_display_name_without_profile(self):
        """Test display name when no profile exists"""
        user = User.objects.create_user(**self.user_data)
        self.assertEqual(user.display_name, '<EMAIL>')

    def test_display_name_with_profile(self):
        """Test display name when profile exists"""
        user = User.objects.create_user(**self.user_data)
        UserProfile.objects.create(
            user=user,
            first_name='John',
            last_name='Doe'
        )
        
        self.assertEqual(user.display_name, 'John Doe')

    def test_get_full_name_without_profile(self):
        """Test get_full_name when no profile exists"""
        user = User.objects.create_user(**self.user_data)
        self.assertEqual(user.get_full_name(), '<EMAIL>')

    def test_get_full_name_with_profile(self):
        """Test get_full_name when profile exists"""
        user = User.objects.create_user(**self.user_data)
        UserProfile.objects.create(
            user=user,
            first_name='John',
            last_name='Doe'
        )
        
        self.assertEqual(user.get_full_name(), 'John Doe')

    def test_get_short_name_without_profile(self):
        """Test get_short_name when no profile exists"""
        user = User.objects.create_user(**self.user_data)
        self.assertEqual(user.get_short_name(), 'test')

    def test_get_short_name_with_profile(self):
        """Test get_short_name when profile exists"""
        user = User.objects.create_user(**self.user_data)
        UserProfile.objects.create(
            user=user,
            first_name='John',
            last_name='Doe'
        )
        
        self.assertEqual(user.get_short_name(), 'John')

    def test_verify_email_method(self):
        """Test verify_email method"""
        user = User.objects.create_user(**self.user_data)
        self.assertFalse(user.email_verified)
        
        user.verify_email()
        user.refresh_from_db()
        
        self.assertTrue(user.email_verified)

    def test_activate_method(self):
        """Test activate method"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            status=UserStatus.INACTIVE,
            is_active=False
        )
        
        user.activate()
        user.refresh_from_db()
        
        self.assertEqual(user.status, UserStatus.ACTIVE)
        self.assertTrue(user.is_active)

    def test_deactivate_method(self):
        """Test deactivate method"""
        user = User.objects.create_user(**self.user_data)
        
        user.deactivate()
        user.refresh_from_db()
        
        self.assertEqual(user.status, UserStatus.INACTIVE)
        self.assertFalse(user.is_active)

    def test_suspend_method(self):
        """Test suspend method"""
        user = User.objects.create_user(**self.user_data)
        
        user.suspend()
        user.refresh_from_db()
        
        self.assertEqual(user.status, UserStatus.SUSPENDED)

    def test_update_last_login_ip(self):
        """Test updating last login IP"""
        user = User.objects.create_user(**self.user_data)
        
        user.last_login_ip = '***********'
        user.save()
        user.refresh_from_db()
        
        self.assertEqual(user.last_login_ip, '***********')

    def test_clean_method_email_validation(self):
        """Test clean method validates email"""
        user = User(
            email='invalid-email',
            password='testpass123'
        )
        
        with self.assertRaises(ValidationError):
            user.clean()

    def test_clean_method_role_status_validation(self):
        """Test clean method validates role and status combination"""
        # This test is removed as the model doesn't have role-status validation
        # The model allows any combination of role and status
        pass

    def test_clean_method_email_lowercase(self):
        """Test clean method converts email to lowercase"""
        user = User(
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Test that full_clean() method normalizes email
        user.full_clean()
        # Django's normalize_email converts domain to lowercase but preserves local part case
        self.assertEqual(user.email, '<EMAIL>')

    def test_model_constraints(self):
        """Test model constraints are enforced"""
        # Test unique email constraint
        User.objects.create_user(**self.user_data)
        
        with self.assertRaises(ValidationError):
            User.objects.create_user(**self.user_data)

    def test_model_indexes(self):
        """Test that model indexes are created"""
        # This test verifies that the model defines the expected indexes
        # The actual index creation is tested at the database level
        user = User.objects.create_user(**self.user_data)
        
        # Query using indexed fields to ensure they work
        users = User.objects.filter(email='<EMAIL>')
        self.assertEqual(users.count(), 1)
        
        users = User.objects.filter(role=UserRoles.CUSTOMER)
        self.assertEqual(users.count(), 1)
        
        users = User.objects.filter(status=UserStatus.ACTIVE)
        self.assertEqual(users.count(), 1)

    def test_user_cascade_deletion(self):
        """Test that related objects are properly handled on user deletion"""
        user = User.objects.create_user(**self.user_data)
        
        # Create related objects
        UserProfile.objects.create(user=user, first_name='John')
        UserSecurity.objects.create(user=user)
        
        user_id = user.id
        user.delete()
        
        # Verify related objects are deleted
        self.assertFalse(UserProfile.objects.filter(user_id=user_id).exists())
        self.assertFalse(UserSecurity.objects.filter(user_id=user_id).exists())


class CustomUserManagerTests(TestCase):
    """Test cases for CustomUserManager"""

    def test_active_users_manager(self):
        """Test ActiveUserManager returns only active users"""
        active_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            status=UserStatus.ACTIVE
        )
        
        inactive_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            status=UserStatus.INACTIVE
        )
        
        suspended_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            status=UserStatus.SUSPENDED
        )
        
        active_users = User.active_users.all()
        self.assertEqual(active_users.count(), 1)
        self.assertEqual(active_users.first(), active_user)

    def test_customers_manager(self):
        """Test CustomerManager returns only customers"""
        customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER
        )
        
        provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.SERVICE_PROVIDER
        )
        
        admin = User.objects.create_superuser(
            email='<EMAIL>',
            password='testpass123'
        )
        
        customers = User.customers.all()
        self.assertEqual(customers.count(), 1)
        self.assertEqual(customers.first(), customer)

    def test_providers_manager(self):
        """Test ProviderManager returns only providers"""
        customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER
        )
        
        provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.SERVICE_PROVIDER
        )
        
        admin = User.objects.create_superuser(
            email='<EMAIL>',
            password='testpass123'
        )
        
        providers = User.providers.all()
        self.assertEqual(providers.count(), 1)
        self.assertEqual(providers.first(), provider)

    def test_admins_manager(self):
        """Test AdminManager returns only admins"""
        customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER
        )
        
        provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.SERVICE_PROVIDER
        )
        
        admin = User.objects.create_superuser(
            email='<EMAIL>',
            password='testpass123'
        )
        
        admins = User.admins.all()
        self.assertEqual(admins.count(), 1)
        self.assertEqual(admins.first(), admin)

    def test_manager_chaining(self):
        """Test that managers can be chained with other queries"""
        # Create test users
        active_customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER,
            status=UserStatus.ACTIVE
        )
        
        inactive_customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER,
            status=UserStatus.INACTIVE
        )
        
        # Test chaining managers with additional filters
        active_customers = User.customers.filter(status=UserStatus.ACTIVE)
        self.assertEqual(active_customers.count(), 1)
        self.assertEqual(active_customers.first(), active_customer)
        
        # Test combining managers
        active_users = User.active_users.filter(role=UserRoles.CUSTOMER)
        self.assertEqual(active_users.count(), 1)
        self.assertEqual(active_users.first(), active_customer)


@pytest.mark.django_db
class CustomUserPytestTests:
    """Pytest-style tests for CustomUser model"""

    def test_user_creation_with_factory(self):
        """Test user creation using factory pattern"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        assert user.email == '<EMAIL>'
        assert user.role == UserRoles.CUSTOMER
        assert not user.email_verified

    def test_user_password_hashing(self):
        """Test that passwords are properly hashed"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='plaintextpassword'
        )
        
        # Password should be hashed, not stored in plain text
        assert user.password != 'plaintextpassword'
        assert user.check_password('plaintextpassword')
        assert not user.check_password('wrongpassword')

    def test_user_timezone_handling(self):
        """Test that date fields handle timezone correctly"""
        from unittest.mock import patch
        from django.utils import timezone
        
        mock_time = timezone.now()
        with patch('django.utils.timezone.now', return_value=mock_time):
            user = User.objects.create_user(
                email='<EMAIL>',
                password='testpass123'
            )
            
            # The actual time might be slightly different due to processing time
            # So we check that they're close (within 1 second)
            time_diff = abs((user.date_joined - mock_time).total_seconds())
            assert time_diff < 1

    def test_user_permissions_inheritance(self):
        """Test that user inherits Django's permission system"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Test basic permission methods exist
        assert hasattr(user, 'has_perm')
        assert hasattr(user, 'has_module_perms')
        assert hasattr(user, 'get_all_permissions')
        
        # Test default permissions
        assert not user.has_perm('accounts_app.add_customuser')
        assert not user.is_staff
        assert not user.is_superuser

    def test_superuser_permissions(self):
        """Test that superuser has all permissions"""
        superuser = User.objects.create_superuser(
            email='<EMAIL>',
            password='testpass123'
        )
        
        assert superuser.has_perm('accounts_app.add_customuser')
        assert superuser.is_staff
        assert superuser.is_superuser
        assert superuser.has_module_perms('accounts_app') 