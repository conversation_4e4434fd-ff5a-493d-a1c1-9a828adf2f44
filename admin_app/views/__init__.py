"""View package for admin_app.

This package organizes the monolithic views module into
feature-specific submodules for better maintainability.
All views are re-exported here for backward compatibility.
"""

# Common utilities
from .common import admin_required, is_admin_user, ITEMS_PER_PAGE, User

# Authentication
from .auth import admin_login_view, admin_logout_view

# Dashboard
from .dashboard import admin_dashboard_view

# User management
from .users import (
    AdminUserListView,
    AdminUserDetailView,
    admin_user_edit_view,
    admin_bulk_user_actions_view,
    admin_provider_approval_view,
    admin_pending_providers_view,
)

# Content management
from .content import (
    AdminStaticPageListView,
    AdminStaticPageCreateView,
    AdminStaticPageDetailView,
    AdminStaticPageUpdateView,
    AdminStaticPageDeleteView,
    AdminHomepageBlockListView,
    AdminHomepageBlockCreateView,
    AdminMediaFileListView,
    AdminMediaFileCreateView,
)

# Blog management
from .blog import (
    AdminBlogCategoryListView,
    AdminBlogCategoryCreateView,
    AdminBlogPostListView,
    AdminBlogPostCreateView,
    AdminBlogPostUpdateView,
    AdminBlogPostDeleteView,
)

# System configuration
from .system import (
    admin_site_configuration_view,
    AdminAnnouncementListView,
    AdminAnnouncementCreateView,
)

# Analytics
from .analytics import (
    admin_analytics_dashboard_view,
    admin_export_analytics_view,
)

# System health and misc utilities
from .health import (
    AdminSystemHealthListView,
    admin_resolve_health_event_view,
    admin_health_check,
    admin_global_search_view,
)

# Media management
from .media import (
    AdminMediaFileListView,
    AdminMediaFileCreateView,
)

__all__ = [
    'admin_required', 'is_admin_user', 'ITEMS_PER_PAGE', 'User',
    'admin_login_view', 'admin_logout_view',
    'admin_dashboard_view',
    'AdminUserListView', 'AdminUserDetailView', 'admin_user_edit_view',
    'admin_bulk_user_actions_view', 'admin_provider_approval_view',
    'admin_pending_providers_view',
    'AdminStaticPageListView', 'AdminStaticPageCreateView', 'AdminStaticPageDetailView',
    'AdminStaticPageUpdateView', 'AdminStaticPageDeleteView',
    'AdminHomepageBlockListView', 'AdminHomepageBlockCreateView',
    'AdminBlogCategoryListView', 'AdminBlogCategoryCreateView',
    'AdminBlogPostListView', 'AdminBlogPostCreateView',
    'AdminBlogPostUpdateView', 'AdminBlogPostDeleteView',
    'admin_site_configuration_view', 'AdminAnnouncementListView',
    'AdminAnnouncementCreateView',
    'admin_analytics_dashboard_view', 'admin_export_analytics_view',
    'AdminSystemHealthListView', 'admin_resolve_health_event_view',
    'admin_health_check', 'admin_global_search_view',
    'AdminMediaFileListView', 'AdminMediaFileCreateView',
]
