"""
Comprehensive tests for custom validators in accounts_app.
"""

import pytest
from django.core.exceptions import ValidationError
from django.test import TestCase
from django.contrib.auth import get_user_model
from unittest.mock import patch, MagicMock

from accounts_app.utils.validators import (
    normalize_phone, ComplexityPasswordValidator, PasswordHistoryValidator,
    CustomMinimumLengthValidator, NoPersonalInformationValidator,
    NoSequentialCharactersValidator, NoRepeatedCharactersValidator
)
from accounts_app.constants import UserRoles

User = get_user_model()


class PhoneValidatorTests(TestCase):
    """Test cases for phone number validation"""

    def test_normalize_phone_valid(self):
        """Test phone normalization with valid numbers"""
        valid_numbers = [
            '+**********',
            '**********',
            '(*************',
            '************',
            '************'
        ]
        
        for phone in valid_numbers:
            try:
                result = normalize_phone(phone)
                self.assertTrue(result.startswith('+'))
                self.assertEqual(len(result), 11)  # +**********
            except ValidationError:
                self.fail(f"Valid phone number {phone} failed validation")

    def test_normalize_phone_invalid(self):
        """Test phone normalization with invalid numbers"""
        invalid_numbers = [
            '123',
            '123456',
            'abc123def',
            '',
            None
        ]
        
        for phone in invalid_numbers:
            with self.assertRaises(ValidationError):
                normalize_phone(phone)


class CustomMinimumLengthValidatorTests(TestCase):
    """Test cases for custom minimum length validator"""

    def test_valid_password_length(self):
        """Test validator with valid password length"""
        validator = CustomMinimumLengthValidator(min_length=12)
        valid_passwords = ['ValidPassword123', 'AnotherValidPass', 'Test123456789']
        
        for password in valid_passwords:
            try:
                validator.validate(password)
            except ValidationError:
                self.fail(f"Valid password {password} failed validation")

    def test_invalid_password_length(self):
        """Test validator with invalid password length"""
        validator = CustomMinimumLengthValidator(min_length=12)
        invalid_passwords = ['short', 'test123', 'password']
        
        for password in invalid_passwords:
            with self.assertRaises(ValidationError) as cm:
                validator.validate(password)
            self.assertIn('too short', str(cm.exception))


class ComplexityPasswordValidatorTests(TestCase):
    """Test cases for password complexity validator"""

    def test_valid_complex_password(self):
        """Test validator with valid complex passwords"""
        validator = ComplexityPasswordValidator()
        valid_passwords = [
            'Complex123!',
            'MySecurePass2023',
            'Abcd1234$',
            'Test@Password1'
        ]
        
        for password in valid_passwords:
            try:
                validator.validate(password)
            except ValidationError:
                self.fail(f"Valid password {password} failed validation")

    def test_invalid_complex_password(self):
        """Test validator with invalid complex passwords"""
        validator = ComplexityPasswordValidator()
        
        # Test missing uppercase
        with self.assertRaises(ValidationError):
            validator.validate('lowercase123!')
        
        # Test missing lowercase
        with self.assertRaises(ValidationError):
            validator.validate('UPPERCASE123!')
        
        # Test missing digit
        with self.assertRaises(ValidationError):
            validator.validate('TestPassword!')
        
        # Test missing special character
        with self.assertRaises(ValidationError):
            validator.validate('TestPassword123')


class NoPersonalInformationValidatorTests(TestCase):
    """Test cases for personal information validator"""

    def test_password_with_personal_info(self):
        """Test validator with passwords containing personal information"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='temp123',
            first_name='John',
            last_name='Doe',
            role=UserRoles.CUSTOMER
        )
        
        validator = NoPersonalInformationValidator()
        
        # Passwords containing user's name or email
        personal_passwords = [
            'John123456',
            'DoePassword1',
            'johndoe123',
            '<EMAIL>'
        ]
        
        for password in personal_passwords:
            with self.assertRaises(ValidationError):
                validator.validate(password, user=user)

    def test_password_without_personal_info(self):
        """Test validator with passwords not containing personal information"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='temp123',
            first_name='John',
            last_name='Doe',
            role=UserRoles.CUSTOMER
        )
        
        validator = NoPersonalInformationValidator()
        valid_passwords = ['SecurePassword123!', 'RandomPass456', 'Complex@Pass789']
        
        for password in valid_passwords:
            try:
                validator.validate(password, user=user)
            except ValidationError:
                self.fail(f"Valid password {password} failed validation")


class NoSequentialCharactersValidatorTests(TestCase):
    """Test cases for sequential characters validator"""

    def test_password_with_sequential_chars(self):
        """Test validator with passwords containing sequential characters"""
        validator = NoSequentialCharactersValidator(max_sequential=3)
        
        sequential_passwords = [
            'Abc12345',
            'Test123456',
            'Password1234'
        ]
        
        for password in sequential_passwords:
            with self.assertRaises(ValidationError):
                validator.validate(password)

    def test_password_without_sequential_chars(self):
        """Test validator with passwords not containing sequential characters"""
        validator = NoSequentialCharactersValidator(max_sequential=3)
        valid_passwords = ['SecurePassword123!', 'RandomPass456', 'Complex@Pass789']
        
        for password in valid_passwords:
            try:
                validator.validate(password)
            except ValidationError:
                self.fail(f"Valid password {password} failed validation")


class NoRepeatedCharactersValidatorTests(TestCase):
    """Test cases for repeated characters validator"""

    def test_password_with_repeated_chars(self):
        """Test validator with passwords containing repeated characters"""
        validator = NoRepeatedCharactersValidator(max_repeated=2)
        
        repeated_passwords = [
            'Testtttt123',
            'Passworddd1',
            'Aaaaaa123'
        ]
        
        for password in repeated_passwords:
            with self.assertRaises(ValidationError):
                validator.validate(password)

    def test_password_without_repeated_chars(self):
        """Test validator with passwords not containing repeated characters"""
        validator = NoRepeatedCharactersValidator(max_repeated=2)
        valid_passwords = ['SecurePassword123!', 'RandomPass456', 'Complex@Pass789']
        
        for password in valid_passwords:
            try:
                validator.validate(password)
            except ValidationError:
                self.fail(f"Valid password {password} failed validation")


class PasswordHistoryValidatorTests(TestCase):
    """Test cases for password history validator"""

    def test_password_reuse_detection(self):
        """Test that reused passwords are detected"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='initial123',
            role=UserRoles.CUSTOMER
        )
        
        validator = PasswordHistoryValidator(history_count=3)
        
        # This should pass since it's a new password
        try:
            validator.validate('newpassword123', user=user)
        except ValidationError:
            self.fail("New password should not trigger validation error")
        
        # Note: In a real test, you would need to create PasswordHistory objects
        # to test the actual reuse detection. This is a simplified test. 