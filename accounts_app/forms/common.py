# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Crispy Forms Imports ---
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field, Div, HTML, Submit
from crispy_forms.bootstrap import FormActions

# --- Local App Imports ---
from ..models import CustomUser
from ..mixins import EmailValidationMixin



# --- Form Mixins ---

class AccessibleFormMixin:
    """
    Mixin to add ARIA labels for better accessibility on form fields.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add ARIA labels for accessibility
        fields = getattr(self, 'fields', {})
        for name, field in fields.items():
            field.widget.attrs.setdefault('aria-label', field.label)




# --- Account Deactivation Form ---

class AccountDeactivationForm(AccessibleFormMixin, EmailValidationMixin, forms.Form):
    """
    Form for service provider account deactivation with email confirmation.

    Fields:
    - confirm_email: Email field to confirm the user's email address before deactivation.
    """

    confirm_email = forms.EmailField(
        label=_('Confirm your email address'),
        max_length=254,
        widget=forms.EmailInput(
            attrs={
                'placeholder': _('Enter your email address to confirm'),
                'autocomplete': 'email',
            }
        ),
        help_text=_(
            'Please enter your email address to confirm account deactivation.'
        )
    )

    def __init__(self, user: CustomUser, *args, **kwargs):
        """
        Initialize form with the current user for validation context.

        Args:
            user (CustomUser): The user requesting account deactivation.
        """
        self.user = user
        super().__init__(*args, **kwargs)
        
        # Configure crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        self.helper.layout = Layout(
            Div(
                Field('confirm_email', css_class='form-control'),
                css_class='mb-3'
            ),
            FormActions(
                Submit('deactivate', _('Deactivate Account'), css_class='btn-danger'),
                css_class='d-grid gap-2'
            )
        )

    def clean_confirm_email(self) -> str:
        """
        Validate that the provided email matches the authenticated user's email.

        Raises:
            ValidationError: If the emails do not match.

        Returns:
            str: The cleaned email address.
        """
        email = self.cleaned_data.get('confirm_email')
        if email and email != self.user.email:
            raise ValidationError(
                _('Email address does not match your account email.'),
                code='invalid_email_match'
            )
        return email or ''









