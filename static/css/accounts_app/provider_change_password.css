/* CozyWish Design System - Provider Change Password */
:root {
    /* Brand Colors */
    --cw-brand-primary: #2F160F;
    --cw-brand-light: #4a2a1f;
    --cw-brand-accent: #fae1d7;
    --cw-accent-light: #fef7f0;
    --cw-accent-dark: #f1d4c4;

    /* Neutral Colors */
    --cw-neutral-600: #525252;
    --cw-neutral-700: #404040;
    --cw-neutral-800: #262626;

    /* Typography */
    --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

    /* Shadows */
    --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Gradients */
    --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
    --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
    --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
}

/* Global Styles */
body {
    font-family: var(--cw-font-primary);
    line-height: 1.6;
    color: var(--cw-neutral-800);
    background: white;
    min-height: 100vh;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--cw-font-heading);
    font-weight: 600;
    color: var(--cw-brand-primary);
    line-height: 1.3;
}

/* Change Password Section */
.change-password-section {
    padding: 5rem 0;
    background: white;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.change-password-container {
    max-width: 800px;
    width: 100%;
    margin: 0 auto;
    padding: 0 2rem;
}

.change-password-card {
    background: white;
    border: 1px solid rgba(250, 225, 215, 0.3);
    border-radius: 1rem;
    box-shadow: var(--cw-shadow-lg);
    overflow: hidden;
    width: 100%;
}

/* Header Section */
.change-password-header {
    background: var(--cw-gradient-card-subtle);
    padding: 3rem 3rem 2rem;
    text-align: center;
    position: relative;
    border-bottom: 1px solid var(--cw-brand-accent);
}

.change-password-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="security-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M10,2 L18,10 L10,18 L2,10 Z" fill="%23f1d4c4" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23security-pattern)"/></svg>') repeat;
    opacity: 0.6;
    z-index: 1;
}

.change-password-header .content {
    position: relative;
    z-index: 2;
}

.change-password-icon {
    width: 80px;
    height: 80px;
    background: var(--cw-gradient-brand-button);
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--cw-shadow-md);
}

.change-password-title {
    font-family: var(--cw-font-display);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--cw-brand-primary);
    margin-bottom: 1rem;
    line-height: 1.2;
}

.change-password-subtitle {
    font-size: 1.125rem;
    color: var(--cw-neutral-600);
    margin-bottom: 0;
    line-height: 1.6;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* Form Styling */
.form-control-cw {
    border: 2px solid var(--cw-brand-accent);
    border-radius: 0.5rem;
    padding: 0.875rem 1rem;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: white;
    color: var(--cw-neutral-800);
    font-family: var(--cw-font-primary);
}

.form-control-cw:focus {
    border-color: var(--cw-brand-primary);
    box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    outline: none;
}

.form-control-cw.is-invalid {
    border-color: #dc2626;
    box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.1);
}

.form-label {
    color: var(--cw-neutral-700);
    font-weight: 600;
    font-family: var(--cw-font-heading);
    margin-bottom: 0.5rem;
}

.form-label i {
    color: var(--cw-brand-primary);
    margin-right: 0.5rem;
}

/* Input Group Styling */
.input-group {
    position: relative;
    display: flex;
    width: 100%;
}

.input-group .form-control-cw {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
}

.toggle-password {
    background: white;
    border: 2px solid var(--cw-brand-accent);
    border-left: none;
    color: var(--cw-neutral-700);
    padding: 0.875rem 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
    border-top-right-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
}

.toggle-password:hover {
    background: var(--cw-accent-light);
    color: var(--cw-brand-primary);
}

.input-group .form-control-cw:focus + .toggle-password {
    border-color: var(--cw-brand-primary);
}

/* Button Styling */
.btn-cw-primary {
    background: var(--cw-gradient-brand-button);
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    padding: 1rem 2rem;
    color: white;
    transition: all 0.2s ease;
    box-shadow: var(--cw-shadow-sm);
    font-family: var(--cw-font-heading);
    letter-spacing: 0.025em;
    font-size: 1.125rem;
}

.btn-cw-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
    color: white;
}

.btn-cw-secondary {
    border: 2px solid var(--cw-brand-primary);
    color: var(--cw-brand-primary);
    border-radius: 0.5rem;
    font-weight: 600;
    padding: 0.875rem 1.5rem;
    background: white;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    font-family: var(--cw-font-heading);
    letter-spacing: 0.025em;
}

.btn-cw-secondary:hover {
    background: var(--cw-brand-primary);
    color: white;
    transform: translateY(-2px);
    text-decoration: none;
    box-shadow: var(--cw-shadow-md);
}

/* Form Body */
.change-password-body {
    padding: 3rem;
}

/* Alert Styling */
.alert {
    border: none;
    border-radius: 0.75rem;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    font-family: var(--cw-font-primary);
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    color: white;
}

.alert-danger {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    color: white;
}

.alert-info {
    background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
    color: white;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    padding: 2rem 0;
    border-top: 2px solid var(--cw-brand-accent);
    margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .change-password-section {
        padding: 3rem 0;
    }

    .change-password-container {
        padding: 0 1.5rem;
    }

    .change-password-header {
        padding: 2rem 2rem 1.5rem;
    }

    .change-password-title {
        font-size: 2.25rem;
    }

    .change-password-subtitle {
        font-size: 1rem;
    }

    .change-password-body {
        padding: 2rem;
    }

    .form-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn-cw-primary,
    .btn-cw-secondary {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .change-password-container {
        padding: 0 1rem;
    }

    .change-password-header {
        padding: 1.5rem 1.5rem 1rem;
    }

    .change-password-body {
        padding: 1.5rem;
    }

    .change-password-title {
        font-size: 1.875rem;
    }

    .change-password-icon {
        width: 64px;
        height: 64px;
        font-size: 1.5rem;
    }
} 