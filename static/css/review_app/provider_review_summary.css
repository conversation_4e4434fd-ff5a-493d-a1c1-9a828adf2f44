/* Review App - Provider Review Summary Styles */

/* Rating Bar */
.rating-bar {
    margin-bottom: 1rem;
}

.rating-bar .progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.rating-bar .progress-bar {
    background-color: #007bff;
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Review Item */
.review-item {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

.review-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.review-item .rating {
    color: #6c757d;
    font-size: 0.9rem;
    margin-left: 0.5rem;
}

/* Stats Cards */
.card.text-center {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 12px;
    transition: transform 0.2s ease;
}

.card.text-center:hover {
    transform: translateY(-2px);
}

.card.text-center h3 {
    color: #007bff;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.card.text-center p {
    color: #6c757d;
    margin-bottom: 0;
    font-size: 0.9rem;
}

/* Review Insights */
.list-unstyled li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.list-unstyled li:last-child {
    border-bottom: none;
}

.list-unstyled strong {
    color: #495057;
}

/* Empty State */
.text-center.py-5 h3 {
    color: #6c757d;
    margin-bottom: 1rem;
}

.text-center.py-5 p {
    color: #6c757d;
    margin-bottom: 0.5rem;
} 