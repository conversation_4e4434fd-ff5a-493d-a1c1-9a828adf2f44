{% extends 'base.html' %}

{% block title %}Business Login - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
{% endblock %}

{% block content %}
<section class="login-section">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="content">
                    <div class="login-icon">
                        <i class="fas fa-store"></i>
                    </div>
                    <h1 class="login-title">Business Login</h1>
                    <p class="login-subtitle">Sign in to your CozyWish business account</p>
                </div>
            </div>

            <div class="login-body">
                <!-- Display messages -->
                {% if messages %}
                  {% for message in messages %}
                    <div class="alert-cw alert-cw-{% if message.tags == 'error' %}error{% elif message.tags == 'success' %}success{% else %}info{% endif %} mb-4">
                      <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
                      {{ message }}
                    </div>
                  {% endfor %}
                {% endif %}

                <form method="post">
                  {% csrf_token %}

                  {% if form.non_field_errors %}
                  <div class="alert-cw alert-cw-error mb-4">
                    {% for error in form.non_field_errors %}
                    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}

                  <!-- Account Information Section -->
                  <div class="form-section">
                    <!-- Email field -->
                    <div class="mb-4">
                      <label for="{{ form.email.id_for_label }}" class="form-label">
                        <i class="fas fa-envelope"></i>{{ form.email.label }}
                      </label>
                      {% if form.email.errors %}
                        {{ form.email|add_class:"form-control-cw is-invalid"|attr:"placeholder:Enter your business email" }}
                      {% else %}
                        {{ form.email|add_class:"form-control-cw"|attr:"placeholder:Enter your business email" }}
                      {% endif %}
                      {% if form.email.errors %}
                      <div class="invalid-feedback" role="alert" aria-live="polite">
                        {% for error in form.email.errors %}
                        <i class="fas fa-exclamation-circle"></i>{{ error }}
                        {% endfor %}
                      </div>
                      {% endif %}
                      {% if form.email.help_text %}
                      <div class="form-text">{{ form.email.help_text }}</div>
                      {% endif %}
                    </div>

                    <!-- Password field -->
                    <div class="mb-4">
                      <label for="{{ form.password.id_for_label }}" class="form-label">
                        <i class="fas fa-lock"></i>{{ form.password.label }}
                      </label>
                      <div class="input-group">
                        {% if form.password.errors %}
                          {{ form.password|add_class:"form-control-cw is-invalid"|attr:"placeholder:Enter your password" }}
                        {% else %}
                          {{ form.password|add_class:"form-control-cw"|attr:"placeholder:Enter your password" }}
                        {% endif %}
                        <button type="button" class="btn toggle-password" data-target="#{{ form.password.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                          <i class="fas fa-eye" aria-hidden="true"></i>
                        </button>
                      </div>
                      {% if form.password.errors %}
                      <div class="invalid-feedback" role="alert" aria-live="polite">
                        {% for error in form.password.errors %}
                        <i class="fas fa-exclamation-circle"></i>{{ error }}
                        {% endfor %}
                      </div>
                      {% endif %}
                      {% if form.password.help_text %}
                      <div class="form-text">{{ form.password.help_text }}</div>
                      {% endif %}
                    </div>

                    <!-- Remember me and forgot password -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="remember" name="remember">
                        <label class="form-check-label" for="remember">
                          Remember me
                        </label>
                      </div>
                      <a href="{% url 'accounts_app:service_provider_password_reset' %}" id="forgot-password-link">
                        Forgot password?
                      </a>
                    </div>

                    <!-- Submit button -->
                    <div class="d-grid mb-4">
                      <button type="submit" class="btn-cw-primary">
                        <i class="fas fa-sign-in-alt me-2"></i>Sign In
                      </button>
                    </div>
                  </div>
                </form>

                <!-- Sign up links -->
                <div class="text-center">
                  <p class="mb-3">Don't have a business account?</p>
                  <div class="d-grid mb-4">
                    <a href="{% url 'accounts_app:service_provider_signup' %}" class="btn-cw-secondary">
                      <i class="fas fa-store me-2"></i>Join as Business
                    </a>
                  </div>

                  <p class="mb-3">Looking for customer login?</p>
                  <div class="d-grid">
                    <a href="{% url 'accounts_app:customer_login' %}" class="btn-cw-ghost">
                      <i class="fas fa-user me-2"></i>Customer Login
                    </a>
                  </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Password Toggle JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    // Password toggle functionality
    function togglePassword(toggleBtn) {
        const targetSelector = toggleBtn.getAttribute('data-target');
        if (!targetSelector) {
            console.error('No data-target attribute found on toggle button');
            return;
        }

        const input = document.querySelector(targetSelector);
        if (!input) {
            console.error('Target input not found:', targetSelector);
            return;
        }

        // Toggle password visibility
        const isPassword = input.type === 'password';
        input.type = isPassword ? 'text' : 'password';

        // Update icon
        const icon = toggleBtn.querySelector('i');
        if (icon) {
            if (isPassword) {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Update accessibility attributes
        const newTitle = isPassword ? 'Hide password' : 'Show password';
        toggleBtn.title = newTitle;
        toggleBtn.setAttribute('aria-label', newTitle);
    }

    // Click event handler
    document.addEventListener('click', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn) {
            e.preventDefault();
            e.stopPropagation();
            togglePassword(toggleBtn);
        }
    });

    // Keyboard event handler for accessibility
    document.addEventListener('keydown', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn && (e.key === 'Enter' || e.key === ' ' || e.key === 'Spacebar')) {
            e.preventDefault();
            e.stopPropagation();
            togglePassword(toggleBtn);
        }
    });

    // Initialize toggle buttons
    const toggleButtons = document.querySelectorAll('.toggle-password');
    toggleButtons.forEach(function(btn) {
        // Ensure button has proper attributes
        if (!btn.hasAttribute('tabindex')) {
            btn.setAttribute('tabindex', '0');
        }
        
        // Add role for screen readers
        btn.setAttribute('role', 'button');
        
        // Ensure aria-label is set
        if (!btn.hasAttribute('aria-label')) {
            btn.setAttribute('aria-label', 'Toggle password visibility');
        }
    });
});
</script>
{% endblock %}
