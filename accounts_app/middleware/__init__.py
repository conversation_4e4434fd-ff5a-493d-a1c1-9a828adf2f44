"""
Middleware components for accounts_app.

This package contains middleware classes for handling account security features
such as account lockout and password history tracking.
"""

from .security import (
    AccountLockoutMiddleware,
    PasswordHistoryMiddleware,
    handle_failed_login,
    handle_successful_login,
    track_password_change,
    get_client_ip_from_request,
    handle_password_change,
)

__all__ = [
    'AccountLockoutMiddleware',
    'PasswordHistoryMiddleware',
    'handle_failed_login',
    'handle_successful_login',
    'track_password_change',
    'get_client_ip_from_request',
    'handle_password_change',
]
