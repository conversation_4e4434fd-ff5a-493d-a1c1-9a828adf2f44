{% extends 'base.html' %}

{% block title %}Navigation Components Test - CozyWish{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Breadcrumb Example -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item"><a href="/services">Services</a></li>
            <li class="breadcrumb-item active" aria-current="page">Navigation Test</li>
        </ol>
    </nav>

    <h1 class="mb-4">Navigation Components Test</h1>
    
    <!-- Tabs Example -->
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="mb-3">Tabs Component</h3>
            <ul class="nav nav-tabs" id="myTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="home-tab" data-bs-toggle="tab" data-bs-target="#home" type="button" role="tab" aria-controls="home" aria-selected="true">Home</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button" role="tab" aria-controls="profile" aria-selected="false">Profile</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab" aria-controls="contact" aria-selected="false">Contact</button>
                </li>
            </ul>
            <div class="tab-content" id="myTabContent">
                <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">
                    <div class="p-4">
                        <h4>Home Tab Content</h4>
                        <p>This is the home tab content with our enhanced styling.</p>
                    </div>
                </div>
                <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                    <div class="p-4">
                        <h4>Profile Tab Content</h4>
                        <p>This is the profile tab content with our enhanced styling.</p>
                    </div>
                </div>
                <div class="tab-pane fade" id="contact" role="tabpanel" aria-labelledby="contact-tab">
                    <div class="p-4">
                        <h4>Contact Tab Content</h4>
                        <p>This is the contact tab content with our enhanced styling.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar Example -->
    <div class="row">
        <div class="col-md-3">
            <h3 class="mb-3">Sidebar Navigation</h3>
            <div class="sidebar">
                <nav class="nav flex-column">
                    <a class="nav-link active" href="#">
                        <i class="fas fa-home"></i>Dashboard
                    </a>
                    <a class="nav-link" href="#">
                        <i class="fas fa-calendar"></i>Bookings
                    </a>
                    <a class="nav-link" href="#">
                        <i class="fas fa-user"></i>Profile
                    </a>
                    <a class="nav-link" href="#">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <a class="nav-link" href="#">
                        <i class="fas fa-bell"></i>Notifications
                    </a>
                </nav>
            </div>
        </div>
        <div class="col-md-9">
            <h3 class="mb-3">Main Content Area</h3>
            <p>This demonstrates the sidebar navigation component. The sidebar is styled with our brand colors and includes hover effects.</p>
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Content Card</h5>
                    <p class="card-text">This card shows how content looks alongside the sidebar navigation.</p>
                    <a href="#" class="btn btn-primary">Learn More</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 