# CozyWish

A Django-based venue booking and management platform.

## Quick Setup

```bash
# Clone and setup
git clone <repository-url>
cd CozyWish

# Create virtual environment
python -m venv ENV_COZYWISH
source ENV_COZYWISH/bin/activate  # Windows: ENV_COZYWISH\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Environment setup
cp .env.example .env
# Edit .env with your configuration

# Database setup
python manage.py makemigrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Run development server
python manage.py runserver
```

## Environment Configuration

Copy `.env.example` to `.env` and configure your environment variables:

```bash
# Required for all environments
SECRET_KEY=your-secret-key-here
DEBUG=True

# Database (development)
DATABASE_URL=sqlite:///db.sqlite3

# Email (optional for development)
EMAIL_HOST_PASSWORD=your-sendgrid-key
```

## Key Features

- **User Management**: Customer and provider authentication
- **Venue Management**: Venue listings, search, and booking
- **Booking System**: Cart-based booking with payments
- **Reviews**: Rating and feedback system
- **Admin Dashboard**: Comprehensive management interface
- **Notifications**: User notifications and announcements
- **Discounts**: Promotional codes and offers

## Development

```bash
# Run tests
python manage.py test

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic
```

## Deployment

**Health Check**: `/health/` - Returns `{"status": "healthy"}`

**Production Environment Variables**:
- `DATABASE_URL` - Database connection string
- `SECRET_KEY` - Django secret key
- `DEBUG=false` - Production mode
- `AWS_ACCESS_KEY_ID` - S3 storage
- `AWS_SECRET_ACCESS_KEY` - S3 storage
- `AWS_STORAGE_BUCKET_NAME` - S3 bucket
- `EMAIL_HOST_PASSWORD` - SendGrid API key

**Build Command**: `./build.sh`  
**Start Command**: `gunicorn project_root.wsgi:application`

## Project Structure

```
CozyWish/
├── project_root/          # Django project settings
├── accounts_app/          # User authentication
├── venues_app/            # Venue management
├── booking_cart_app/      # Booking system
├── payments_app/          # Payment processing
├── admin_app/             # Admin interface
├── dashboard_app/         # User dashboards
├── review_app/            # Reviews & ratings
├── notifications_app/     # Notifications
├── discount_app/          # Discounts & offers
├── templates/             # HTML templates
├── static/                # Static files
└── docs/                  # Documentation
```

## Settings

- **Development**: `project_root.settings.development`
- **Production**: `project_root.settings.production` 