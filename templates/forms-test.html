{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CozyWish Forms - Design System Demo</title>
    <!-- DaisyUI styles are loaded from base.html -->

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="{% static 'js/form-components.js' %}" defer></script>
</head>
<body>
    <div class="bg-brand-background min-vh-100">
        <!-- Header -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-spa text-brand-primary"></i>
                    CozyWish <span class="text-brand-primary">Forms</span>
                </a>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="#">Design System</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Forms</li>
                    </ol>
                </nav>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="container my-5">
            <div class="row">
                <div class="col-12">
                    <div class="text-center mb-5">
                        <h1 class="text-brand-primary fw-bold mb-3">
                            <i class="fas fa-wpforms"></i> Form Components
                        </h1>
                        <p class="lead text-muted">
                            Consistent, accessible form components for your booking platform
                        </p>
                    </div>
                </div>
            </div>

            <!-- Booking Form Demo -->
            <div class="row justify-content-center mb-5">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-calendar-plus text-brand-primary me-2"></i>
                                Booking Form
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="form-booking">
                                <h3 class="mb-4">Book a Service</h3>
                                <form>
                                    <div class="mb-3">
                                        <label for="service" class="form-label">Service Type</label>
                                        <select class="form-select" id="service">
                                            <option selected>Choose a service...</option>
                                            <option value="1">Massage</option>
                                            <option value="2">Facial</option>
                                            <option value="3">Spa Package</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="date" class="form-label">Preferred Date</label>
                                        <input type="date" class="form-control" id="date">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Your Name</label>
                                        <input type="text" class="form-control" id="name" placeholder="Enter your name">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" placeholder="Enter your email">
                                    </div>
                                    
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="terms">
                                        <label class="form-check-label" for="terms">
                                            I agree to the terms and conditions
                                        </label>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary w-100">Book Now</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Form Demo -->
            <div class="row justify-content-center mb-5">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-envelope text-brand-primary me-2"></i>
                                Contact Form
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="form-contact">
                                <h3 class="mb-4">Get in Touch</h3>
                                <form>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="firstName" class="form-label">First Name</label>
                                                <input type="text" class="form-control" id="firstName" placeholder="John">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="lastName" class="form-label">Last Name</label>
                                                <input type="text" class="form-control" id="lastName" placeholder="Doe">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="contactEmail" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="contactEmail" placeholder="<EMAIL>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="subject" class="form-label">Subject</label>
                                        <select class="form-select" id="subject">
                                            <option selected>Choose a subject...</option>
                                            <option value="1">General Inquiry</option>
                                            <option value="2">Booking Support</option>
                                            <option value="3">Technical Issue</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="message" class="form-label">Message</label>
                                        <textarea class="form-control" id="message" rows="4" placeholder="Tell us how we can help..."></textarea>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary">Send Message</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search Form Demo -->
            <div class="row justify-content-center mb-5">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-search text-brand-primary me-2"></i>
                                Search Form
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="input-group-search">
                                <div class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </div>
                                <input type="text" class="form-control" placeholder="Search for services, venues, or treatments...">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Validation Demo -->
            <div class="row justify-content-center mb-5">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-check-circle text-brand-primary me-2"></i>
                                Form Validation
                            </h4>
                        </div>
                        <div class="card-body">
                            <form class="was-validated" novalidate>
                                <div class="mb-3">
                                    <label for="validationCustom01" class="form-label">Valid input</label>
                                    <input type="text" class="form-control" id="validationCustom01" value="Mark" required>
                                    <div class="valid-feedback">
                                        Looks good!
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="validationCustom02" class="form-label">Invalid input</label>
                                    <input type="text" class="form-control" id="validationCustom02" value="" required>
                                    <div class="invalid-feedback">
                                        Please provide a valid value.
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="validationCustom03" class="form-label">File upload</label>
                                    <input type="file" class="form-control" id="validationCustom03" accept=".jpg,.png,.pdf">
                                </div>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="validationCheck">
                                    <label class="form-check-label" for="validationCheck">
                                        Check this checkbox
                                    </label>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault1">
                                        <label class="form-check-label" for="flexRadioDefault1">
                                            Option 1
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault2" checked>
                                        <label class="form-check-label" for="flexRadioDefault2">
                                            Option 2 (Selected)
                                        </label>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">Submit Form</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Component Guide -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-code text-brand-primary me-2"></i>
                                Implementation Guide
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>Available Classes</h5>
                                    <ul class="list-unstyled">
                                        <li><code>.form-booking</code> - Booking form container</li>
                                        <li><code>.form-contact</code> - Contact form container</li>
                                        <li><code>.input-group-search</code> - Search input with icon</li>
                                        <li><code>.form-control</code> - Enhanced input styling</li>
                                        <li><code>.form-select</code> - Enhanced select styling</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h5>Features</h5>
                                    <ul class="list-unstyled">
                                        <li>✓ Brand-consistent styling</li>
                                        <li>✓ Focus states with brand colors</li>
                                        <li>✓ Validation feedback styling</li>
                                        <li>✓ Custom checkbox/radio styling</li>
                                        <li>✓ File upload button styling</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="text-center py-4 bg-white border-top mt-5">
            <div class="container">
                <p class="text-muted mb-0">
                    <i class="fas fa-heart text-brand-primary"></i>
                    CozyWish Design System - Forms Component
                </p>
            </div>
        </footer>
    </div>

    <!-- DaisyUI JavaScript is loaded from base.html -->
</body>
</html> 