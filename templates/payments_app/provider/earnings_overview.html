{% extends 'base.html' %}

{% block title %}Earnings Overview - Service Provider - CozyWish{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'home_app:home' %}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'accounts_app:service_provider_profile' %}">Provider Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Earnings Overview</li>
                </ol>
            </nav>
            
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Earnings Overview</h1>
                <div class="btn-group" role="group">
                    <a href="{% url 'payments_app:provider_payment_history' %}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>Payment History
                    </a>
                    <a href="{% url 'payments_app:provider_payout_history' %}" class="btn btn-outline-success">
                        <i class="fas fa-money-bill-wave me-2"></i>Payout History
                    </a>
                </div>
            </div>
            
            {% include 'payments_app/includes/dashboard_skeleton.html' %}
            <div id="dashboard-content" style="display:none;">
            <!-- Earnings Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h5 class="card-title text-primary">Total Earnings</h5>
                            <h3 class="text-primary">${{ total_earnings|floatformat:2 }}</h3>
                            <small class="text-muted">All time</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <h5 class="card-title text-success">This Month</h5>
                            <h3 class="text-success">${{ monthly_earnings|floatformat:2 }}</h3>
                            <small class="text-muted">Last 30 days</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card border-info">
                        <div class="card-body text-center">
                            <h5 class="card-title text-info">This Week</h5>
                            <h3 class="text-info">${{ weekly_earnings|floatformat:2 }}</h3>
                            <small class="text-muted">Last 7 days</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card border-warning">
                        <div class="card-body text-center">
                            <h5 class="card-title text-warning">This Year</h5>
                            <h3 class="text-warning">${{ yearly_earnings|floatformat:2 }}</h3>
                            <small class="text-muted">Last 365 days</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Payout Information -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-credit-card me-2"></i>Pending Payouts
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span>Amount Ready for Payout:</span>
                                <strong class="text-success">${{ pending_payouts.amount|floatformat:2 }}</strong>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span>Next Payout Date:</span>
                                <strong>{{ pending_payouts.next_payout_date }}</strong>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span>Platform Fee ({{ platform_fee_rate|floatformat:1 }}%):</span>
                                <span class="text-muted">-${% widthratio total_earnings 1 platform_fee_rate %}</span>
                            </div>
                            <hr>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Stripe Integration:</strong> Payouts are processed automatically via Stripe. 
                                Funds are typically available in your bank account within 1-2 business days.
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>Payment Statistics
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span>Total Payments:</span>
                                <strong>{{ payment_stats.total_payments }}</strong>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span>Average Payment:</span>
                                <strong>${{ payment_stats.average_payment|floatformat:2 }}</strong>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span>Success Rate:</span>
                                <strong class="text-success">
                                    {% if payment_stats.total_payments > 0 %}
                                        100%
                                    {% else %}
                                        0%
                                    {% endif %}
                                </strong>
                            </div>
                            <hr>
                            <div class="text-center">
                                <a href="{% url 'payments_app:provider_payment_history' %}" class="btn btn-primary">
                                    View All Payments
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Payments -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Recent Payments
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_payments %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Customer</th>
                                        <th>Booking</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for payment in recent_payments %}
                                    <tr>
                                        <td>{{ payment.payment_date|date:"M d, Y" }}</td>
                                        <td>
                                            {% if payment.customer.first_name %}
                                                {{ payment.customer.first_name }} {{ payment.customer.last_name }}
                                            {% else %}
                                                {{ payment.customer.email }}
                                            {% endif %}
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ payment.booking.booking_id|truncatechars:8 }}</small>
                                        </td>
                                        <td>
                                            <strong>${{ payment.amount_paid|floatformat:2 }}</strong>
                                        </td>
                                        <td>
                                            {% if payment.payment_status == 'succeeded' %}
                                                <span class="badge bg-success">Succeeded</span>
                                            {% elif payment.payment_status == 'pending' %}
                                                <span class="badge bg-warning">Pending</span>
                                            {% elif payment.payment_status == 'failed' %}
                                                <span class="badge bg-danger">Failed</span>
                                            {% elif payment.payment_status == 'refunded' %}
                                                <span class="badge bg-secondary">Refunded</span>
                                            {% else %}
                                                <span class="badge bg-light text-dark">{{ payment.get_payment_status_display }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{% url 'payments_app:provider_payment_detail' payment_id=payment.payment_id %}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="{% url 'payments_app:provider_payment_history' %}" class="btn btn-outline-primary">
                                View All Payment History
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No payments received yet</h5>
                            <p class="text-muted">Payments from customers will appear here once you start receiving bookings.</p>
                            <a href="{% url 'venues_app:venue_list' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Create Your First Venue
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            </div><!-- /#dashboard-content -->

            <!-- Navigation Links -->
            <div class="d-flex justify-content-between mt-4">
                <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
                <div>
                    <a href="{% url 'booking_cart_app:provider_booking_list' %}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-calendar-check me-2"></i>View Bookings
                    </a>
                    <a href="{% url 'venues_app:provider_venues' %}" class="btn btn-primary">
                        <i class="fas fa-building me-2"></i>Manage Venues
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
