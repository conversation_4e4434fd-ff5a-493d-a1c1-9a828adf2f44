"""
Mixins package for accounts_app.

This package provides reusable mixins for views and forms
across the accounts_app.
"""

# Import all view mixins for easy access
from .view import (
    ProfileRequiredMixin,
    EmailVerifiedMixin,
    ProfileAndEmailRequiredMixin,
    CustomerProfileRequiredMixin,
    ServiceProviderProfileRequiredMixin,
)

# Import all form mixins for easy access
from .form import (
    EmailValidationMixin,
    PasswordValidationMixin,
    PhoneValidationMixin,
    ImageValidationMixin,
    URLValidationMixin,
    BusinessValidationMixin,
    TermsValidationMixin,
)

__all__ = [
    # View mixins
    'ProfileRequiredMixin',
    'EmailVerifiedMixin',
    'ProfileAndEmailRequiredMixin',
    'CustomerProfileRequiredMixin',
    'ServiceProviderProfileRequiredMixin',
    
    # Form validation mixins
    'EmailValidationMixin',
    'PasswordValidationMixin',
    'PhoneValidationMixin',
    'ImageValidationMixin',
    'URLValidationMixin',
    'BusinessValidationMixin',
    'TermsValidationMixin',
] 