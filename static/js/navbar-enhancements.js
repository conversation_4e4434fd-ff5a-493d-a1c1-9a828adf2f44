/**
 * CozyWish Navbar Enhancements
 * Enhanced functionality for the navbar component
 */

document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    // Get navbar element
    const navbar = document.getElementById('cozywish-navbar');
    
    if (!navbar) {
        return; // Navbar not found, exit early
    }

    // ===========================================
    // SCROLL EFFECTS
    // ===========================================
    
    function handleScroll() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    }

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);
    
    // Initial check
    handleScroll();

    // ===========================================
    // MOBILE MENU ENHANCEMENTS
    // ===========================================
    
    const navbarToggler = navbar.querySelector('.navbar-toggler');
    const navbarCollapse = navbar.querySelector('.navbar-collapse');
    
    if (navbarToggler && navbarCollapse) {
        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!navbarToggler.contains(e.target) && !navbarCollapse.contains(e.target)) {
                const bsCollapse = new bootstrap.Collapse(navbarCollapse);
                bsCollapse.hide();
            }
        });

        // Close menu when clicking on a link (mobile)
        const navLinks = navbarCollapse.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (window.innerWidth < 992) { // Mobile breakpoint
                    const bsCollapse = new bootstrap.Collapse(navbarCollapse);
                    bsCollapse.hide();
                }
            });
        });
    }

    // ===========================================
    // DROPDOWN ENHANCEMENTS
    // ===========================================
    
    const dropdowns = navbar.querySelectorAll('.dropdown');
    
    dropdowns.forEach(dropdown => {
        const dropdownToggle = dropdown.querySelector('.dropdown-toggle');
        const dropdownMenu = dropdown.querySelector('.dropdown-menu');
        
        if (dropdownToggle && dropdownMenu) {
            // Add hover effect for desktop
            if (window.innerWidth >= 992) {
                dropdown.addEventListener('mouseenter', function() {
                    const bsDropdown = new bootstrap.Dropdown(dropdownToggle);
                    bsDropdown.show();
                });
                
                dropdown.addEventListener('mouseleave', function() {
                    const bsDropdown = new bootstrap.Dropdown(dropdownToggle);
                    bsDropdown.hide();
                });
            }
        }
    });

    // ===========================================
    // ACTIVE LINK HIGHLIGHTING
    // ===========================================
    
    function highlightActiveLink() {
        const currentPath = window.location.pathname;
        const navLinks = navbar.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && href !== '#' && currentPath.includes(href)) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    }
    
    // Highlight active link on page load
    highlightActiveLink();

    // ===========================================
    // SEARCH FUNCTIONALITY
    // ===========================================
    
    const searchForm = navbar.querySelector('.navbar-search form');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const searchInput = this.querySelector('input[type="search"]');
            const query = searchInput.value.trim();
            
            if (query) {
                // You can customize this to redirect to search results
                console.log('Search query:', query);
                // Example: window.location.href = `/search/?q=${encodeURIComponent(query)}`;
            }
        });
    }

    // ===========================================
    // ACCESSIBILITY ENHANCEMENTS
    // ===========================================
    
    // Add keyboard navigation support
    const focusableElements = navbar.querySelectorAll(
        'a[href], button, input, textarea, select, [tabindex]:not([tabindex="-1"])'
    );
    
    focusableElements.forEach(element => {
        element.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });

    // ===========================================
    // PERFORMANCE OPTIMIZATIONS
    // ===========================================
    
    // Throttle scroll events for better performance
    let ticking = false;
    
    function updateNavbarOnScroll() {
        handleScroll();
        ticking = false;
    }
    
    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateNavbarOnScroll);
            ticking = true;
        }
    }
    
    // Replace scroll listener with throttled version
    window.removeEventListener('scroll', handleScroll);
    window.addEventListener('scroll', requestTick);

    // ===========================================
    // RESPONSIVE BEHAVIOR
    // ===========================================
    
    function handleResize() {
        // Reinitialize dropdowns on resize
        if (window.innerWidth >= 992) {
            // Desktop behavior
            dropdowns.forEach(dropdown => {
                dropdown.classList.remove('mobile-dropdown');
            });
        } else {
            // Mobile behavior
            dropdowns.forEach(dropdown => {
                dropdown.classList.add('mobile-dropdown');
            });
        }
    }
    
    // Handle resize events
    window.addEventListener('resize', handleResize);
    
    // Initial setup
    handleResize();

    // ===========================================
    // ANIMATION ENHANCEMENTS
    // ===========================================
    
    // Add smooth transitions for navbar elements
    const animatedElements = navbar.querySelectorAll('.nav-link, .btn, .navbar-brand');
    
    animatedElements.forEach(element => {
        element.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
    });

    // ===========================================
    // DEBUG MODE
    // ===========================================
    
    // Enable debug mode if navbar has debug attribute
    if (navbar.hasAttribute('data-debug')) {
        console.log('CozyWish Navbar Debug Mode Enabled');
        console.log('Navbar element:', navbar);
        console.log('Current variant:', navbar.classList.toString());
        
        // Log navbar state changes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    console.log('Navbar classes changed:', navbar.classList.toString());
                }
            });
        });
        
        observer.observe(navbar, {
            attributes: true,
            attributeFilter: ['class']
        });
    }
});

// ===========================================
// UTILITY FUNCTIONS
// ===========================================

/**
 * Get the current navbar variant
 * @returns {string} The current navbar variant
 */
function getNavbarVariant() {
    const navbar = document.getElementById('cozywish-navbar');
    if (!navbar) return null;
    
    const classes = navbar.classList;
    if (classes.contains('navbar-hero')) return 'hero';
    if (classes.contains('navbar-minimal')) return 'minimal';
    if (classes.contains('navbar-admin')) return 'admin';
    if (classes.contains('navbar-provider')) return 'provider';
    if (classes.contains('navbar-customer')) return 'customer';
    return 'default';
}

/**
 * Set the navbar variant
 * @param {string} variant - The variant to set
 */
function setNavbarVariant(variant) {
    const navbar = document.getElementById('cozywish-navbar');
    if (!navbar) return;
    
    // Remove all variant classes
    navbar.classList.remove('navbar-hero', 'navbar-minimal', 'navbar-admin', 'navbar-provider', 'navbar-customer');
    
    // Add the new variant class
    if (variant !== 'default') {
        navbar.classList.add(`navbar-${variant}`);
    }
}

/**
 * Toggle navbar scroll effect
 * @param {boolean} enabled - Whether to enable scroll effects
 */
function toggleNavbarScrollEffect(enabled) {
    const navbar = document.getElementById('cozywish-navbar');
    if (!navbar) return;
    
    if (enabled) {
        navbar.classList.remove('no-scroll-effect');
    } else {
        navbar.classList.add('no-scroll-effect');
    }
}

// Export functions for global use
window.CozyWishNavbar = {
    getVariant: getNavbarVariant,
    setVariant: setNavbarVariant,
    toggleScrollEffect: toggleNavbarScrollEffect
}; 