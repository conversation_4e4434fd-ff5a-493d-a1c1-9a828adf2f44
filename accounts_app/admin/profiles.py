# --- Django Imports ---
from django.contrib import admin
from django.utils.translation import gettext_lazy as _

# --- Local Imports ---
from ..models import CustomerProfile, ServiceProviderProfile


@admin.register(CustomerProfile)
class CustomerProfileAdmin(admin.ModelAdmin):
    """Admin for CustomerProfile."""
    list_display = ('user', 'get_full_name', 'phone_number', 'city', 'created_at')
    list_filter = ('gender', 'city', 'created_at')
    search_fields = ('user__email', 'first_name', 'last_name', 'phone_number')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (_('User'), {'fields': ('user',)}),
        (_('Personal Information'), {
            'fields': (
                'first_name', 'last_name', 'profile_picture',
                'gender', 'birth_month', 'birth_year'
            )
        }),
        (_('Contact Information'), {
            'fields': (
                'phone_number', 'address', 'city', 'zip_code'
            )
        }),
        (_('Metadata'), {'fields': ('created_at', 'updated_at')}),
    )

    def get_full_name(self, obj):
        return obj.get_full_name()
    get_full_name.short_description = 'Full Name'


@admin.register(ServiceProviderProfile)
class ServiceProviderProfileAdmin(admin.ModelAdmin):
    """Manage service provider profiles."""
    list_display = (
        'business_name', 'user', 'city', 'state',
        'is_public', 'created'
    )
    list_filter = ('state', 'is_public', 'created')
    search_fields = (
        'legal_name', 'display_name', 'user__email',
        'city', 'phone'
    )
    readonly_fields = ('created', 'updated')
    fieldsets = (
        (_('User'), {'fields': ('user',)}),
        (_('Business'), {'fields': (
            'legal_name', 'display_name', 'description',
            'logo', 'ein'
        )}),
        (_('Contact'), {'fields': (
            'phone', 'contact_name'
        )}),
        (_('Address'), {'fields': (
            'address', 'city', 'state',
            'county', 'zip_code'
        )}),
        (_('Web'), {'fields': (
            'website', 'instagram', 'facebook'
        )}),
        (_('Settings'), {'fields': ('is_public',)}),
        (_('Metadata'), {'fields': ('created', 'updated')}),
    ) 