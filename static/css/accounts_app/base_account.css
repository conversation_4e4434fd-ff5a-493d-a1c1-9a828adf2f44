/* Accounts App - Base Account Styles */

/* Account Wrapper */
.account-wrapper {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem 1rem;
}

/* Account Card */
.account-card {
    max-width: 520px;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: none;
    overflow: hidden;
}

.account-card-body {
    padding: 2rem;
}

/* Profile Wrapper */
.profile-wrapper {
    min-height: 100vh;
    background: #f8f9fa;
    padding: 2rem 0;
}

/* Messages Container */
.messages-container {
    margin-bottom: 1.5rem;
}

.messages-container .alert {
    border-radius: 0.75rem;
    border: none;
    margin-bottom: 1rem;
}

.messages-container .alert:last-child {
    margin-bottom: 0;
}

/* Form Elements */
.account-card .form-control,
.account-card .form-select {
    border-radius: 0.75rem;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.2s ease;
}

.account-card .form-control:focus,
.account-card .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.1);
}

/* Labels */
.account-card .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Buttons */
.account-card .btn {
    border-radius: 0.75rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.2s ease;
}

.account-card .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.account-card .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.account-card .btn-outline-primary {
    border: 2px solid #667eea;
    color: #667eea;
    background: transparent;
}

.account-card .btn-outline-primary:hover {
    background: #667eea;
    color: white;
}

/* Card Headers */
.account-card .card-header {
    background: white;
    border-bottom: 2px solid #f8f9fa;
    padding: 1.5rem 2rem 1rem;
}

.account-card .card-header h1,
.account-card .card-header h2,
.account-card .card-header h3,
.account-card .card-header h4,
.account-card .card-header h5,
.account-card .card-header h6 {
    margin: 0;
    color: #495057;
    font-weight: 700;
}

/* Card Body */
.account-card .card-body {
    padding: 1rem 2rem 2rem;
}

/* Links */
.account-card a {
    color: #667eea;
    text-decoration: none;
    transition: color 0.2s ease;
}

.account-card a:hover {
    color: #5a67d8;
    text-decoration: none;
}

/* Text Colors */
.account-card .text-muted {
    color: #6c757d !important;
}

.account-card .text-primary {
    color: #667eea !important;
}

.text-muted-cw {
    color: var(--cw-neutral-600) !important;
}

.d-none {
    display: none !important;
}

.border-top-accent {
    border-top: 1px solid var(--cw-brand-accent) !important;
}

/* Responsive Design */
@media (max-width: 576px) {
    .account-wrapper {
        padding: 1rem 0.5rem;
    }
    
    .account-card-body {
        padding: 1.5rem;
    }
    
    .account-card .card-header {
        padding: 1rem 1.5rem 0.5rem;
    }
    
    .account-card .card-body {
        padding: 0.5rem 1.5rem 1.5rem;
    }
} 