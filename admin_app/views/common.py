"""Shared helpers and decorators used across admin views."""

# --- Standard Library Imports ---
import logging

# --- Third-Party Imports ---
from django.contrib.auth import get_user_model
from django.contrib.auth.decorators import login_required, user_passes_test
from django.shortcuts import render
from django.db.models import Avg, Count, Q
from django.utils import timezone
from datetime import timedelta

# Shared constants
ITEMS_PER_PAGE = 20

# Get the custom user model
User = get_user_model()

# Set up logging
logger = logging.getLogger(__name__)


def is_admin_user(user):
    """Check if user is an admin/staff member."""
    return user.is_authenticated and (user.is_staff or user.is_superuser)


def admin_required(view_func):
    """Decorator ensuring the user is logged in and is an admin."""
    decorated_view = login_required(
        user_passes_test(is_admin_user, login_url='admin_app:admin_login')(view_func)
    )
    return decorated_view




