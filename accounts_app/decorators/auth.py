"""
Permission decorators and mixins for role-based access control.

This module provides decorators and mixins that enforce authentication and role-based
permissions for views in the accounts_app.
"""

from functools import wraps
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from django.http import HttpResponseForbidden, HttpResponse
from django.shortcuts import redirect
from django.urls import reverse_lazy
from django.utils.decorators import method_decorator
from django.contrib import messages
from django.utils.translation import gettext_lazy as _

from ..constants import UserRoles


# --- Permission Decorators for Function-Based Views ---

def customer_required(view_func):
    """
    Decorator to require that the user is authenticated and is a customer.
    
    Usage:
        @customer_required
        def my_view(request):
            ...
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('accounts_app:customer_login')
        
        if not request.user.is_customer:
            messages.error(request, _('Access denied. Customer access required.'))
            return redirect('home_app:home')  # Redirect to home for wrong role
        
        return view_func(request, *args, **kwargs)
    return wrapper


def service_provider_required(view_func):
    """
    Decorator to require that the user is authenticated and is a service provider.
    
    Usage:
        @service_provider_required
        def my_view(request):
            ...
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('accounts_app:service_provider_login')
        
        if not request.user.is_service_provider:
            messages.error(request, _('Access denied. Service provider access required.'))
            return redirect('home_app:home')  # Redirect to home for wrong role
        
        return view_func(request, *args, **kwargs)
    return wrapper


def admin_required(view_func):
    """
    Decorator to require that the user is authenticated and is an admin.
    
    Usage:
        @admin_required
        def my_view(request):
            ...
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('accounts_app:admin_login')
        
        if not request.user.is_admin:
            messages.error(request, _('Access denied. Administrator access required.'))
            return redirect('home_app:home')  # Redirect to home for wrong role
        
        return view_func(request, *args, **kwargs)
    return wrapper


def role_required(*allowed_roles):
    """
    Decorator to require that the user has one of the specified roles.
    
    Usage:
        @role_required(UserRoles.CUSTOMER, UserRoles.SERVICE_PROVIDER)
        def my_view(request):
            ...
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return redirect('accounts_app:customer_login')
            
            if request.user.role not in allowed_roles:
                messages.error(request, _('Access denied. Insufficient permissions.'))
                return HttpResponseForbidden('Access denied. Insufficient permissions.')
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def anonymous_required(view_func):
    """
    Decorator to require that the user is NOT authenticated.
    Redirects authenticated users to their appropriate home page.
    
    Usage:
        @anonymous_required
        def login_view(request):
            ...
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if request.user.is_authenticated:
            if request.user.is_customer:
                return redirect('accounts_app:customer_profile')
            elif request.user.is_service_provider:
                return redirect('accounts_app:service_provider_profile')
            elif request.user.is_admin:
                return redirect('admin_app:dashboard')
            else:
                return redirect('home_app:home')
        
        return view_func(request, *args, **kwargs)
    return wrapper


# --- Permission Mixins for Class-Based Views ---

class CustomerRequiredMixin(LoginRequiredMixin):
    """
    Mixin that requires the user to be authenticated and be a customer.
    
    Usage:
        class MyView(CustomerRequiredMixin, DetailView):
            ...
    """
    login_url = reverse_lazy('accounts_app:customer_login')
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if not request.user.is_customer:
            messages.error(request, _('Access denied. Customer access required.'))
            return HttpResponseForbidden('Access denied. Customer access required.')
        
        return super().dispatch(request, *args, **kwargs)


class ServiceProviderRequiredMixin(LoginRequiredMixin):
    """
    Mixin that requires the user to be authenticated and be a service provider.
    
    Usage:
        class MyView(ServiceProviderRequiredMixin, DetailView):
            ...
    """
    login_url = reverse_lazy('accounts_app:service_provider_login')
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if not request.user.is_service_provider:
            messages.error(request, _('Access denied. Service provider access required.'))
            return HttpResponseForbidden('Access denied. Service provider access required.')
        
        return super().dispatch(request, *args, **kwargs)


class AdminRequiredMixin(LoginRequiredMixin):
    """
    Mixin that requires the user to be authenticated and be an admin.
    
    Usage:
        class MyView(AdminRequiredMixin, DetailView):
            ...
    """
    login_url = reverse_lazy('accounts_app:admin_login')
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if not request.user.is_admin:
            messages.error(request, _('Access denied. Administrator access required.'))
            return HttpResponseForbidden('Access denied. Administrator access required.')
        
        return super().dispatch(request, *args, **kwargs)


class RoleRequiredMixin(LoginRequiredMixin):
    """
    Mixin that requires the user to have one of the specified roles.
    
    Usage:
        class MyView(RoleRequiredMixin, DetailView):
            allowed_roles = [UserRoles.CUSTOMER, UserRoles.SERVICE_PROVIDER]
            ...
    """
    allowed_roles = []
    login_url = reverse_lazy('accounts_app:customer_login')
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if request.user.role not in self.allowed_roles:
            messages.error(request, _('Access denied. Insufficient permissions.'))
            return HttpResponseForbidden('Access denied. Insufficient permissions.')
        
        return super().dispatch(request, *args, **kwargs)


class AnonymousRequiredMixin:
    """
    Mixin that requires the user to NOT be authenticated.
    Redirects authenticated users to their appropriate home page.
    
    Usage:
        class MyView(AnonymousRequiredMixin, FormView):
            ...
    """
    
    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            if request.user.is_customer:
                return redirect('accounts_app:customer_profile')
            elif request.user.is_service_provider:
                return redirect('accounts_app:service_provider_profile')
            elif request.user.is_admin:
                return redirect('admin_app:dashboard')
            else:
                return redirect('home_app:home')
        
        return super().dispatch(request, *args, **kwargs)


# --- Active User Decorators and Mixins ---

def require_active_user(view_func):
    """
    Decorator to require that the user is active.
    
    Usage:
        @require_active_user
        def my_view(request):
            ...
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('accounts_app:customer_login')
        
        if not request.user.is_active_user:
            messages.error(request, _('Account is not active.'))
            return HttpResponseForbidden('Account is not active.')
        
        return view_func(request, *args, **kwargs)
    return wrapper


class ActiveUserRequiredMixin(LoginRequiredMixin):
    """
    Mixin that requires the user to be active.
    
    Usage:
        class MyView(ActiveUserRequiredMixin, DetailView):
            ...
    """
    login_url = reverse_lazy('accounts_app:customer_login')
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if not request.user.is_active_user:
            messages.error(request, _('Account is not active.'))
            return HttpResponseForbidden('Account is not active.')
        
        return super().dispatch(request, *args, **kwargs)


# --- Utility Functions ---

def get_user_home_url(user):
    """
    Get the appropriate home URL for a user based on their role.
    
    Args:
        user: The user instance
        
    Returns:
        str: The URL name for the user's home page
    """
    if user.is_customer:
        return 'accounts_app:customer_profile'
    elif user.is_service_provider:
        return 'accounts_app:service_provider_profile'
    elif user.is_admin:
        return 'admin_app:dashboard'
    else:
        return 'home_app:home'


def get_user_login_url(user_role):
    """
    Get the appropriate login URL for a user role.
    
    Args:
        user_role: The user role (from UserRoles)
        
    Returns:
        str: The URL name for the login page
    """
    if user_role == UserRoles.CUSTOMER:
        return 'accounts_app:customer_login'
    elif user_role == UserRoles.SERVICE_PROVIDER:
        return 'accounts_app:service_provider_login'
    elif user_role == UserRoles.ADMIN:
        return 'accounts_app:admin_login'
    else:
        return 'accounts_app:customer_login'  # Default fallback 