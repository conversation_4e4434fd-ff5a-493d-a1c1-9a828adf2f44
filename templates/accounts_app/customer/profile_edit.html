{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}Edit Profile - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<!-- Custom CSS -->
<link rel="stylesheet" href="{% static 'css/accounts_app/customer_profile_edit.css' %}">
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Profile picture preview
    const profilePictureInput = document.getElementById('{{ form.profile_picture.id_for_label }}');
    if (profilePictureInput) {
        profilePictureInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('profile-picture-preview');
                    const container = preview.parentElement;
                    
                    // Remove existing content
                    preview.remove();
                    
                    // Create new image element
                    const img = document.createElement('img');
                    img.id = 'profile-picture-preview';
                    img.src = e.target.result;
                    img.alt = 'Profile Picture';
                    img.className = 'profile-picture-preview';
                    
                    // Add to container
                    container.appendChild(img);
                };
                reader.readAsDataURL(this.files[0]);
            }
        });
    }
});
</script>
{% endblock %}

{% block content %}
<section class="profile-edit-section">
    <div class="profile-edit-container">
        <div class="profile-edit-card">
            <div class="profile-edit-header">
                <div class="content">
                    <div class="edit-icon">
                        <i class="fas fa-user-edit"></i>
                    </div>
                    <h1 class="edit-title">Edit Profile</h1>
                    <p class="edit-subtitle">Update your personal information and preferences</p>
                </div>
            </div>
            <div class="profile-edit-body">

<form method="post" enctype="multipart/form-data" novalidate>
  {% csrf_token %}

  {% if form.non_field_errors %}
  <div class="alert-cw-error mb-4">
    {% for error in form.non_field_errors %}
    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
    {% endfor %}
  </div>
  {% endif %}

  <!-- Profile Picture Section -->
  <div class="mb-4">
    <h5 class="section-header">
      <i class="fas fa-camera"></i>Profile Picture
    </h5>
    <div class="profile-picture-upload-section">
      <div>
        <div class="profile-picture-preview-container">
          {% if form.instance.profile_picture %}
            <img id="profile-picture-preview" src="{{ form.instance.profile_picture.url }}" alt="Profile Picture" class="profile-picture-preview">
          {% else %}
            <div id="profile-picture-preview" class="profile-picture-placeholder">
              <i class="fas fa-user fa-3x"></i>
            </div>
          {% endif %}
        </div>
      </div>
      <div class="profile-picture-upload-controls">
        <label class="form-label" for="{{ form.profile_picture.id_for_label }}">Choose Profile Picture</label>
        {{ form.profile_picture|add_class:"form-control-cw" }}
        {% if form.profile_picture.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.profile_picture.errors %}
          <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
        <div class="profile-picture-upload-help">
          <div class="mb-2">
            <strong><i class="fas fa-info-circle me-1"></i>Upload Guidelines:</strong>
          </div>
          <ul class="mb-0 ps-3">
            <li>Recommended size: 400x400 pixels or larger</li>
            <li>Accepted formats: JPG, PNG, GIF</li>
            <li>Maximum file size: 5MB</li>
            <li>Square images work best for circular display</li>
          </ul>
        </div>
        {% if form.profile_picture.help_text %}
        <div class="form-text mt-2">{{ form.profile_picture.help_text }}</div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Personal Information Section -->
  <div class="mb-4">
    <h5 class="section-header">
      <i class="fas fa-user"></i>Personal Information
    </h5>
    <div class="row">
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.first_name.id_for_label }}">{{ form.first_name.label }}</label>
        {{ form.first_name|add_class:"form-control-cw" }}
        {% if form.first_name.errors %}
        <div class="invalid-feedback d-block" role="alert" aria-live="polite">
          {% for error in form.first_name.errors %}
          <i class="fas fa-exclamation-circle me-1" aria-hidden="true"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.last_name.id_for_label }}">{{ form.last_name.label }}</label>
        {{ form.last_name|add_class:"form-control-cw" }}
        {% if form.last_name.errors %}
        <div class="invalid-feedback d-block" role="alert" aria-live="polite">
          {% for error in form.last_name.errors %}
          <i class="fas fa-exclamation-circle me-1" aria-hidden="true"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.phone_number.id_for_label }}">{{ form.phone_number.label }}</label>
        {{ form.phone_number|add_class:"form-control-cw"|attr:"aria-describedby:phone_help" }}
        {% if form.phone_number.errors %}
        <div class="invalid-feedback d-block" role="alert" aria-live="polite">
          {% for error in form.phone_number.errors %}
          <i class="fas fa-exclamation-circle me-1" aria-hidden="true"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
        {% if form.phone_number.help_text %}
        <div class="form-text" id="phone_help">{{ form.phone_number.help_text }}</div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.gender.id_for_label }}">{{ form.gender.label }}</label>
        {{ form.gender|add_class:"form-select-cw" }}
        {% if form.gender.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.gender.errors %}
          <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.birth_month.id_for_label }}">{{ form.birth_month.label }}</label>
        {{ form.birth_month|add_class:"form-select-cw" }}
        {% if form.birth_month.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.birth_month.errors %}
          <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.birth_year.id_for_label }}">{{ form.birth_year.label }}</label>
        {{ form.birth_year|add_class:"form-select-cw" }}
        {% if form.birth_year.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.birth_year.errors %}
          <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Address Information Section -->
  <div class="mb-4">
    <h5 class="section-header">
      <i class="fas fa-map-marker-alt"></i>Address Information
    </h5>
    <div class="row">
      <div class="col-12 mb-3">
        <label class="form-label" for="{{ form.address.id_for_label }}">{{ form.address.label }}</label>
        {{ form.address|add_class:"form-control-cw" }}
        {% if form.address.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.address.errors %}
          <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.city.id_for_label }}">{{ form.city.label }}</label>
        {{ form.city|add_class:"form-control-cw" }}
        {% if form.city.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.city.errors %}
          <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.zip_code.id_for_label }}">{{ form.zip_code.label }}</label>
        {{ form.zip_code|add_class:"form-control-cw" }}
        {% if form.zip_code.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.zip_code.errors %}
          <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Form Actions -->
  <div class="form-actions">
    <div class="d-flex justify-content-between flex-wrap gap-3">
      <a href="{% url 'accounts_app:customer_profile' %}" class="btn-cw-secondary">
        <i class="fas fa-arrow-left"></i>Cancel
      </a>
      <button type="submit" class="btn-cw-primary">
        <i class="fas fa-save"></i>Save Changes
      </button>
    </div>
  </div>
</form>
            </div>
        </div>
    </div>
</section>
{% endblock %}
