# DaisyUI 5.0.46 Integration - CozyWish Project

## Overview
CozyWish has migrated from Bootstrap 5 + Custom Design System to **DaisyUI 5.0.46** with Tailwind CSS. This implementation preserves all brand colors and provides a modern, utility-first approach to styling.

## Version Information
- **DaisyUI Version**: 5.0.46
- **Tailwind CSS**: Latest (via CDN)
- **Implementation**: CDN-based (no build process required)

## Brand Color Mappings

### CozyWish Brand Colors (Original)
- **Main Background**: `#F8F9FA`
- **Text Color**: `#262626`
- **Primary Brand Button**: `#43251B`
- **Brand Accent**: `#FAE1D7`
- **Brand Light**: `#FFF9F4`

### DaisyUI Theme Configuration (`cozywish` theme)
```javascript
cozywish: {
    "primary": "#43251B",      // Primary Brand Button Color
    "secondary": "#FAE1D7",    // Brand Accent Color
    "accent": "#FAE1D7",       // Brand Accent Color
    "neutral": "#262626",      // Text Color
    "base-100": "#F8F9FA",     // Main Background Color
    "base-200": "#FFF9F4",     // Brand Light Color
    "base-300": "#f5f5f5",     // Neutral Light
    "info": "#0284c7",         // Info Blue
    "success": "#059669",      // Success Green
    "warning": "#d97706",      // Warning Orange
    "error": "#dc2626"         // Error Red
}
```

## Theme Implementation

### HTML Setup
```html
<html lang="en" data-theme="cozywish">
```

### CSS/JavaScript Configuration
The theme is configured in `templates/base.html` using Tailwind's configuration:

```javascript
tailwind.config = {
    daisyui: {
        themes: [
            {
                cozywish: {
                    // Theme colors defined above
                }
            }
        ]
    }
}
```

## Common Component Usage Examples

### Buttons
```html
<!-- Primary Button -->
<button class="btn btn-primary">Primary Action</button>

<!-- Secondary Button -->
<button class="btn btn-secondary">Secondary Action</button>

<!-- Outline Button -->
<button class="btn btn-outline btn-primary">Outline</button>

<!-- Button Sizes -->
<button class="btn btn-primary btn-xs">Extra Small</button>
<button class="btn btn-primary btn-sm">Small</button>
<button class="btn btn-primary btn-md">Medium</button>
<button class="btn btn-primary btn-lg">Large</button>
```

### Cards
```html
<div class="card w-96 bg-base-100 shadow-xl">
    <div class="card-body">
        <h2 class="card-title">Card Title</h2>
        <p>Card content goes here</p>
        <div class="card-actions justify-end">
            <button class="btn btn-primary">Action</button>
        </div>
    </div>
</div>
```

### Modals
```html
<!-- Modal Trigger -->
<button class="btn" onclick="my_modal.showModal()">Open Modal</button>

<!-- Modal -->
<dialog id="my_modal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg">Modal Title</h3>
        <p class="py-4">Modal content</p>
        <div class="modal-action">
            <form method="dialog">
                <button class="btn">Close</button>
            </form>
        </div>
    </div>
</dialog>
```

### Alerts/Toasts
```html
<!-- Alert -->
<div class="alert alert-info">
    <span>Info message</span>
</div>

<!-- Toast (using JavaScript helper) -->
<script>
    CozyWish.showToast('Success message', 'success');
</script>
```

### Forms
```html
<div class="form-control w-full max-w-xs">
    <label class="label">
        <span class="label-text">Email</span>
    </label>
    <input type="email" class="input input-bordered w-full max-w-xs" />
    <label class="label">
        <span class="label-text-alt">Helper text</span>
    </label>
</div>
```

## Migration Notes from Bootstrap to DaisyUI

### Class Mapping
| Bootstrap 5 | DaisyUI 5.0.46 |
|-------------|-----------------|
| `btn btn-primary` | `btn btn-primary` |
| `btn btn-secondary` | `btn btn-secondary` |
| `card` | `card` |
| `modal` | `modal` |
| `alert` | `alert` |
| `form-control` | `form-control` |
| `container` | `container mx-auto` |
| `row` | `grid grid-cols-*` |
| `col-md-*` | `grid-cols-*` |

### JavaScript Compatibility
A backward compatibility layer is provided in `base.html` for existing Bootstrap JavaScript:

```javascript
// Existing Bootstrap code will continue to work
const modal = new bootstrap.Modal(document.getElementById('myModal'));
modal.show();

// New DaisyUI helper functions
CozyWish.showModal('myModal');
CozyWish.hideModal('myModal');
CozyWish.showToast('Message', 'success');
```

### Removed Dependencies
- ❌ Bootstrap 5 CSS/JS
- ❌ `static/design-system/` directory
- ❌ Custom CSS overrides for Bootstrap
- ❌ Bootstrap-specific JavaScript components

### New Features Available
- ✅ Complete Tailwind CSS utility classes
- ✅ DaisyUI component library
- ✅ Custom CozyWish theme
- ✅ Responsive design utilities
- ✅ Dark mode support (if needed)
- ✅ Better accessibility features

## Responsive Design
DaisyUI uses Tailwind's responsive system:

```html
<!-- Responsive grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    <!-- Content -->
</div>

<!-- Responsive text -->
<h1 class="text-2xl md:text-4xl lg:text-6xl">Responsive Heading</h1>
```

## Accessibility
DaisyUI includes built-in accessibility features:
- ARIA attributes on components
- Keyboard navigation support
- Screen reader compatibility
- Focus management
- High contrast mode support

## Performance
- **CDN-based**: No build process required
- **Tree-shaking**: Unused CSS is automatically removed
- **Smaller bundle**: DaisyUI is lighter than Bootstrap + custom CSS
- **Fast loading**: Cached CDN resources

## Customization
Additional customization can be done by:
1. Adding custom CSS in `<style>` blocks
2. Extending Tailwind configuration
3. Using DaisyUI utility classes
4. Creating custom component classes

## Testing
Ensure all existing functionality works by:
1. Testing modal interactions
2. Verifying form submissions
3. Checking responsive behavior
4. Validating accessibility features
5. Testing JavaScript integrations

## Support
For issues or questions:
1. Check DaisyUI documentation: https://daisyui.com/
2. Refer to Tailwind CSS docs: https://tailwindcss.com/
3. Review this migration guide
4. Test with the CozyWish helpers in `base.html` 