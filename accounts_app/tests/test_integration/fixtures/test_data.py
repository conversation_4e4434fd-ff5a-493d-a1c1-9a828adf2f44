"""
Test data fixtures and factories for integration tests.

This module provides reusable test data factories and utilities
for creating consistent test scenarios across integration tests.
"""

from django.contrib.auth import get_user_model
from accounts_app.models import CustomerProfile, ServiceProviderProfile, UserSecurity
from accounts_app.constants import UserRoles, UserStatus

User = get_user_model()


class CustomerTestData:
    """Factory for creating customer test data."""
    
    def get_valid_customer_registration_data(self):
        """Get valid customer registration form data."""
        return {
            'email': '<EMAIL>',
            'password1': 'SecurePass456!',
            'password2': 'SecurePass456!',
            'first_name': '<PERSON>',
            'last_name': 'Doe',
            'agree_to_terms': True,
        }
    
    def create_active_customer(self, **kwargs):
        """Create an active customer user with profile."""
        defaults = {
            'email': '<EMAIL>',
            'password': 'SecurePass456!',
            'role': UserRoles.CUSTOMER,
            'status': UserStatus.ACTIVE,
            'email_verified': True,
            'is_active': True,
            'first_name': '<PERSON>',
            'last_name': 'Doe',
        }
        defaults.update(kwargs)
        
        user = User.objects.create_user(**defaults)
        CustomerProfile.objects.create(user=user)
        UserSecurity.objects.create(user=user)
        
        return user
    
    def create_inactive_customer(self, **kwargs):
        """Create an inactive customer user."""
        kwargs.update({
            'is_active': False,
            'status': UserStatus.INACTIVE,
        })
        return self.create_active_customer(**kwargs)
    
    def create_unverified_customer(self, **kwargs):
        """Create an unverified customer user."""
        kwargs.update({
            'email_verified': False,
            'status': UserStatus.PENDING_VERIFICATION,
        })
        return self.create_active_customer(**kwargs)


class ServiceProviderTestData:
    """Factory for creating service provider test data."""
    
    def get_valid_provider_registration_data(self):
        """Get valid service provider registration form data."""
        return {
            'email': '<EMAIL>',
            'password1': 'SecurePass456!',
            'password2': 'SecurePass456!',
            'first_name': 'Jane',
            'last_name': 'Smith',
            'business_name': 'Test Spa LLC',
            'business_phone_number': '+***********',
            'contact_person_name': 'Jane Smith',
            'business_address': '123 Business St',
            'city': 'Los Angeles',
            'state': 'CA',
            'zip_code': '90210',
            'agree_to_terms': True,
        }
    
    def create_active_provider(self, **kwargs):
        """Create an active service provider user with profile."""
        defaults = {
            'email': '<EMAIL>',
            'password': 'SecurePass456!',
            'role': UserRoles.SERVICE_PROVIDER,
            'status': UserStatus.ACTIVE,
            'email_verified': True,
            'is_active': True,
            'first_name': 'Jane',
            'last_name': 'Smith',
        }
        defaults.update(kwargs)
        
        user = User.objects.create_user(**defaults)
        ServiceProviderProfile.objects.create(
            user=user,
            legal_name='Test Spa LLC',
            contact_name='Jane Smith',
            address='123 Business St',
            city='Los Angeles',
            state='CA',
            zip_code='90210',
            phone='+***********'
        )
        UserSecurity.objects.create(user=user)
        
        return user
    
    def create_unverified_provider(self, **kwargs):
        """Create an unverified service provider user."""
        # Ensure unique email for unverified provider
        if 'email' not in kwargs:
            kwargs['email'] = '<EMAIL>'
        kwargs.update({
            'email_verified': False,
            'is_active': False,
            'status': UserStatus.PENDING_VERIFICATION,
        })
        return self.create_active_provider(**kwargs)


class SecurityTestData:
    """Factory for creating security-related test data."""
    
    def create_user_with_failed_attempts(self, user, attempt_count=3):
        """Create a user with specified failed login attempts."""
        security = UserSecurity.objects.get(user=user)
        security.failed_login_attempts = attempt_count
        security.save()
        return security
    
    def create_locked_user(self, user):
        """Create a user with a locked account."""
        security = UserSecurity.objects.get(user=user)
        security.lock_account(30)  # Lock for 30 minutes
        return security


class CommonTestData:
    """Factory for common test data used across tests."""
    
    def create_test_users_all_roles(self):
        """Create test users for all roles."""
        customer_data = CustomerTestData()
        provider_data = ServiceProviderTestData()
        
        customer = customer_data.create_active_customer(email='<EMAIL>')
        provider = provider_data.create_active_provider(email='<EMAIL>')
        
        admin = User.objects.create_user(
            email='<EMAIL>',
            password='SecurePass456!',
            role=UserRoles.ADMIN,
            status=UserStatus.ACTIVE,
            email_verified=True,
            is_active=True,
            is_staff=True,
            is_superuser=True
        )
        
        return {
            'customer': customer,
            'provider': provider,
            'admin': admin,
        }
    
    def get_invalid_login_data_cases(self):
        """Get various invalid login data cases for testing."""
        return [
            # Empty fields
            {'email': '', 'password': ''},
            # Invalid email format
            {'email': 'invalid-email', 'password': 'TestPass456!'},
            # Wrong password
            {'email': '<EMAIL>', 'password': 'WrongPassword'},
            # Non-existent user
            {'email': '<EMAIL>', 'password': 'TestPass456!'},
            # SQL injection attempt
            {'email': "'; DROP TABLE users; --", 'password': 'TestPass456!'},
            # XSS attempt
            {'email': '<script>alert("xss")</script>', 'password': 'TestPass456!'},
        ]
    
    def get_invalid_registration_data_cases(self):
        """Get various invalid registration data cases for testing."""
        return [
            # Password mismatch
            {
                'email': '<EMAIL>',
                'password1': 'StrongPass456!',
                'password2': 'DifferentPass789!',
                'agree_to_terms': True,
            },
            # Weak password
            {
                'email': '<EMAIL>',
                'password1': 'weak',
                'password2': 'weak',
                'agree_to_terms': True,
            },
            # Invalid email
            {
                'email': 'invalid-email',
                'password1': 'StrongPass456!',
                'password2': 'StrongPass456!',
                'agree_to_terms': True,
            },
            # Missing terms agreement
            {
                'email': '<EMAIL>',
                'password1': 'StrongPass456!',
                'password2': 'StrongPass456!',
                'agree_to_terms': False,
            },
        ] 