[pytest]


# --- D<PERSON><PERSON> Settings ---
DJANGO_SETTINGS_MODULE = project_root.settings.testing


# --- Test Discovery ---
python_files = tests.py test_*.py *_tests.py
python_classes = Test* *Tests *TestCase
python_functions = test_*



# --- Test Paths ---
testpaths =
    accounts_app/tests
    venues_app/tests
    booking_cart_app/tests
    payments_app/tests
    dashboard_app/tests
    review_app/tests
    notifications_app/tests
    admin_app/tests
    discount_app/tests
    utils/tests
    utility_app


# --- Pytest Options ---
addopts =
    --tb=short
    --strict-markers
    --strict-config
    --reuse-db
    --nomigrations
    --disable-warnings
    --maxfail=10
    -v




# --- Custom Markers ---
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    models: marks tests as model tests
    views: marks tests as view tests
    forms: marks tests as form tests
    utils: marks tests as utility tests
    commands: marks tests as management command tests
    logging: marks tests as logging tests
    security: marks tests as security-related tests
    performance: marks tests as performance tests


# --- Minimum Pytest Version ---
minversion = 6.0



# --- Suppress specific warnings (Django & Deprecation) ---
filterwarnings =
    ignore::django.utils.deprecation.RemovedInDjango60Warning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning




