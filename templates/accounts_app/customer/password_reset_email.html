<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your CozyWish Password</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <div class="email-logo">
                🔑
            </div>
            <h1 class="email-title">Reset Your Password</h1>
            <p class="email-subtitle">CozyWish Account Security</p>
        </div>

        <!-- Body -->
        <div class="email-body">
            <div class="email-content">
                <p><strong>Hello,</strong></p>

                <p>You're receiving this email because you requested a password reset for your customer account at <strong>CozyWish</strong>.</p>

                <p>To reset your password, click the button below:</p>

                <div class="reset-button-container">
                    {% block reset_link %}
                    <a href="{{ protocol }}://{{ domain }}{% url 'accounts_app:customer_password_reset_confirm' uidb64=uid token=token %}" class="reset-button">
                        Reset My Password
                    </a>
                    {% endblock %}
                </div>

                <p>Or copy and paste this link into your browser:</p>

                <div class="reset-link">
                    {{ protocol }}://{{ domain }}{% url 'accounts_app:customer_password_reset_confirm' uidb64=uid token=token %}
                </div>

                <div class="email-info">
                    <p><strong>Account Email:</strong> {{ user.email }}</p>
                </div>

                <div class="security-note">
                    <p><strong>⚠️ Security Notice:</strong> This reset link will expire in 24 hours for your protection.</p>
                </div>

                <p>If you didn't request this password reset, please ignore this email. Your password won't be changed.</p>

                <p>If you're having trouble with the button above, you can also reset your password by visiting our website and using the "Forgot Password" option.</p>
            </div>
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <p>Thanks for using <span class="brand-name">CozyWish</span>!</p>
            <p>Your trusted spa and wellness marketplace</p>
            <p class="email-footer-note">
                This email was sent to {{ user.email }}. If you have any questions, please contact our support team.
            </p>
        </div>
    </div>
</body>
</html>
