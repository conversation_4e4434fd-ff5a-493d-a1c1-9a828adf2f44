/* CozyWish Design System - Base Styles */

/* CSS Variables */
:root {
    /* Brand Colors */
    --cw-brand-primary: #2F160F;
    --cw-brand-light: #4a2a1f;
    --cw-brand-accent: #fae1d7;
    --cw-accent-light: #fef7f0;
    --cw-accent-dark: #f1d4c4;

    /* Neutral Colors */
    --cw-neutral-600: #525252;
    --cw-neutral-700: #404040;
    --cw-neutral-800: #262626;

    /* Semantic Colors */
    --cw-success: #059669;
    --cw-success-light: #f0fff4;
    --cw-success-border: #bbf7d0;
    --cw-warning: #d97706;
    --cw-warning-light: #fffbeb;
    --cw-warning-border: #fed7aa;
    --cw-error: #dc2626;
    --cw-error-light: #fef2f2;
    --cw-error-border: #fecaca;

    /* Typography */
    --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Sego<PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

    /* Shadows */
    --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Gradients */
    --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
    --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
    --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
}

/* Global Styles */
body {
    font-family: var(--cw-font-primary);
    line-height: 1.6;
    color: var(--cw-neutral-800);
    background: white;
    min-height: 100vh;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--cw-font-heading);
    font-weight: 600;
    color: var(--cw-brand-primary);
    line-height: 1.3;
}

/* Common Section Styles */
.section-cw {
    padding: 5rem 0;
    background: white;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.container-cw {
    max-width: 900px;
    width: 100%;
    margin: 0 auto;
    padding: 0 2rem;
}

.card-cw {
    background: white;
    border: 1px solid rgba(250, 225, 215, 0.3);
    border-radius: 1rem;
    box-shadow: var(--cw-shadow-lg);
    overflow: hidden;
    width: 100%;
}

/* Header Section Styles */
.header-cw {
    background: var(--cw-gradient-card-subtle);
    padding: 3rem 3rem 2rem;
    text-align: center;
    position: relative;
    border-bottom: 1px solid var(--cw-brand-accent);
}

.header-cw::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="%23f1d4c4" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23pattern)"/></svg>') repeat;
    opacity: 0.6;
    z-index: 1;
}

.header-cw .content {
    position: relative;
    z-index: 2;
}

.icon-cw {
    width: 80px;
    height: 80px;
    background: var(--cw-gradient-brand-button);
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--cw-shadow-md);
}

.title-cw {
    font-family: var(--cw-font-display);
    font-size: 2.75rem;
    font-weight: 700;
    color: var(--cw-brand-primary);
    margin-bottom: 1rem;
    line-height: 1.2;
}

.subtitle-cw {
    font-size: 1.25rem;
    color: var(--cw-neutral-600);
    margin-bottom: 0;
    line-height: 1.6;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.body-cw {
    padding: 3rem;
}

/* Form Styling */
.form-control-cw {
    border: 2px solid var(--cw-brand-accent);
    border-radius: 0.5rem;
    padding: 0.875rem 1rem;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: white;
    color: var(--cw-neutral-800);
    font-family: var(--cw-font-primary);
}

.form-control-cw:focus {
    border-color: var(--cw-brand-primary);
    box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    outline: none;
}

.form-control-cw.is-invalid {
    border-color: var(--cw-error);
    box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.1);
}

.form-label-cw {
    color: var(--cw-neutral-700);
    font-weight: 600;
    font-family: var(--cw-font-heading);
    margin-bottom: 0.5rem;
}

.form-label-cw i {
    color: var(--cw-brand-primary);
    margin-right: 0.5rem;
}

/* Button Styling */
.btn-cw-primary {
    background: var(--cw-gradient-brand-button);
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    padding: 1rem 2rem;
    color: white;
    transition: all 0.2s ease;
    box-shadow: var(--cw-shadow-sm);
    font-family: var(--cw-font-heading);
    letter-spacing: 0.025em;
    width: 100%;
    font-size: 1.125rem;
}

.btn-cw-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
    color: white;
}

.btn-cw-secondary {
    border: 2px solid var(--cw-brand-primary);
    color: var(--cw-brand-primary);
    border-radius: 0.5rem;
    font-weight: 600;
    padding: 0.875rem 1.5rem;
    background: white;
    transition: all 0.2s ease;
    font-family: var(--cw-font-heading);
    letter-spacing: 0.025em;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    cursor: pointer;
}

.btn-cw-secondary:hover {
    background: var(--cw-brand-primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--cw-shadow-md);
}

/* Alert Styling */
.alert-cw {
    border-radius: 0.5rem;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid transparent;
    font-family: var(--cw-font-primary);
}

.alert-cw-success {
    background-color: var(--cw-success-light);
    color: var(--cw-success);
    border-color: var(--cw-success-border);
}

.alert-cw-error {
    background-color: var(--cw-error-light);
    color: var(--cw-error);
    border-color: var(--cw-error-border);
}

.alert-cw-warning {
    background-color: var(--cw-warning-light);
    color: var(--cw-warning);
    border-color: var(--cw-warning-border);
}

/* Badge Styling */
.badge-cw-primary {
    background: var(--cw-gradient-brand-button);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 600;
    font-family: var(--cw-font-heading);
    letter-spacing: 0.025em;
}

.badge-cw-secondary {
    background: white;
    color: var(--cw-brand-primary);
    border: 1px solid var(--cw-brand-primary);
    padding: 0.25rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 600;
    font-family: var(--cw-font-heading);
    letter-spacing: 0.025em;
}

/* Profile Page Styles */
.profile-wrapper {
    background: white;
    min-height: 100vh;
    padding: 2rem 0;
}

.profile-header {
    background: var(--cw-gradient-card-subtle);
    border-radius: 1rem 1rem 0 0;
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.profile-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="profile-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="%23f1d4c4" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23profile-pattern)"/></svg>') repeat;
    opacity: 0.6;
    z-index: 1;
}

.profile-header .content {
    position: relative;
    z-index: 2;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid white;
    box-shadow: var(--cw-shadow-md);
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--cw-gradient-brand-button);
    color: white;
    font-size: 3rem;
}

.profile-name {
    font-family: var(--cw-font-display);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--cw-brand-primary);
    margin-bottom: 0.5rem;
}

.profile-role {
    color: var(--cw-neutral-600);
    font-size: 1.125rem;
    margin-bottom: 1.5rem;
}

/* Card Styles */
.card-header-cw {
    background: var(--cw-gradient-card-subtle);
    border-bottom: 1px solid var(--cw-brand-accent);
    padding: 1.5rem;
}

.card-title-cw {
    font-family: var(--cw-font-heading);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--cw-brand-primary);
    margin-bottom: 0;
}

.card-title-cw i {
    color: var(--cw-brand-primary);
    margin-right: 0.5rem;
}

.card-body-cw {
    padding: 1.5rem;
}

/* Form Section Styles */
.form-section {
    margin-bottom: 2rem;
}

.form-section-title {
    font-family: var(--cw-font-heading);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--cw-brand-primary);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--cw-brand-accent);
}

.form-section-title i {
    color: var(--cw-brand-primary);
    margin-right: 0.5rem;
}

/* Links */
.link-cw {
    color: var(--cw-brand-primary);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s ease;
}

.link-cw:hover {
    color: var(--cw-brand-light);
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-cw {
        padding: 0 1rem;
    }
    
    .header-cw {
        padding: 2rem 1.5rem 1.5rem;
    }
    
    .body-cw {
        padding: 2rem 1.5rem;
    }
    
    .title-cw {
        font-size: 2rem;
    }
    
    .subtitle-cw {
        font-size: 1rem;
    }
    
    .profile-name {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .section-cw {
        padding: 2rem 0;
    }
    
    .container-cw {
        padding: 0 0.5rem;
    }
    
    .btn-cw-primary {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }
    
    .title-cw {
        font-size: 1.75rem;
    }
    
    .icon-cw {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
} 