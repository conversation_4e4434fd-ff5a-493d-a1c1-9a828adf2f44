{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}{{ error_title|default:"Error" }} - CozyWish{% endblock %}

{% block booking_extra_css %}
<style>
.error-container {
    min-height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
}

.error-card {
    background: #fff;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    padding: 3rem;
    text-align: center;
    max-width: 600px;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.error-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, rgba(220, 53, 69, 0.05) 0%, rgba(255, 193, 7, 0.05) 100%);
    transform: rotate(12deg);
    z-index: 0;
}

.error-content {
    position: relative;
    z-index: 1;
}

.error-icon {
    width: 120px;
    height: 120px;
    margin: 0 auto 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
    animation: pulse 2s infinite;
}

.error-icon.error-400 {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.error-icon.error-403 {
    background: linear-gradient(135deg, #fd7e14 0%, #e55100 100%);
}

.error-icon.error-404 {
    background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
}

.error-icon.error-500 {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.error-icon.default {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.error-code {
    font-size: 6rem;
    font-weight: bold;
    color: #dc3545;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    animation: fadeIn 1s ease-in;
}

.error-title {
    font-size: 2rem;
    font-weight: 600;
    color: #212529;
    margin-bottom: 1rem;
    animation: fadeIn 1s ease-in 0.2s both;
}

.error-message {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 2rem;
    line-height: 1.6;
    animation: fadeIn 1s ease-in 0.4s both;
}

.error-details {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    text-align: left;
    animation: fadeIn 1s ease-in 0.6s both;
}

.error-details h6 {
    color: #495057;
    margin-bottom: 1rem;
}

.error-details code {
    background: #e9ecef;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    color: #e83e8c;
}

.recovery-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 2rem;
    animation: fadeIn 1s ease-in 0.8s both;
}

.btn-recovery {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 10px;
    padding: 1rem 2rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-recovery:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-secondary-recovery {
    background: transparent;
    border: 2px solid #6c757d;
    color: #6c757d;
    border-radius: 10px;
    padding: 1rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-secondary-recovery:hover {
    background: #6c757d;
    color: white;
    transform: translateY(-2px);
}

.help-section {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 15px;
    padding: 2rem;
    margin-top: 2rem;
    animation: fadeIn 1s ease-in 1s both;
}

.help-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.contact-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.contact-option {
    background: white;
    border: 1px solid #e3f2fd;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
}

.contact-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    text-decoration: none;
    color: inherit;
}

.contact-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
}

.back-button {
    position: fixed;
    top: 2rem;
    left: 2rem;
    background: rgba(255,255,255,0.9);
    border: 1px solid #e9ecef;
    border-radius: 50px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    color: #495057;
    text-decoration: none;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.back-button:hover {
    background: white;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transform: translateY(-1px);
    color: #495057;
    text-decoration: none;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@media (max-width: 768px) {
    .error-card {
        padding: 2rem 1rem;
        margin: 1rem;
    }
    
    .error-code {
        font-size: 4rem;
    }
    
    .error-title {
        font-size: 1.5rem;
    }
    
    .recovery-actions {
        flex-direction: column;
    }
    
    .btn-recovery,
    .btn-secondary-recovery {
        width: 100%;
    }
    
    .back-button {
        position: static;
        margin-bottom: 2rem;
        display: inline-block;
    }
    
    .contact-options {
        grid-template-columns: 1fr;
    }
}

.animate-bounce {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        transform: translate3d(0,-30px,0);
    }
    70% {
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        transform: translate3d(0,-15px,0);
    }
    90% {
        transform: translate3d(0,-4px,0);
    }
}
</style>
{% endblock %}

{% block booking_content %}
<a href="javascript:history.back()" class="back-button">
    <i class="fas fa-arrow-left me-2"></i>Go Back
</a>

<div class="error-container">
    <div class="error-card">
        <div class="error-content">
            <!-- Error Icon -->
            <div class="error-icon {% if error_code %}error-{{ error_code }}{% else %}default{% endif %}">
                {% if error_code == '400' %}
                    <i class="fas fa-exclamation-triangle"></i>
                {% elif error_code == '403' %}
                    <i class="fas fa-ban"></i>
                {% elif error_code == '404' %}
                    <i class="fas fa-search"></i>
                {% elif error_code == '500' %}
                    <i class="fas fa-server"></i>
                {% else %}
                    <i class="fas fa-bug"></i>
                {% endif %}
            </div>

            <!-- Error Code -->
            {% if error_code %}
            <div class="error-code">{{ error_code }}</div>
            {% endif %}

            <!-- Error Title -->
            <h1 class="error-title">
                {{ error_title|default:"Oops! Something went wrong" }}
            </h1>

            <!-- Error Message -->
            <p class="error-message">
                {{ error_message|default:"We encountered an unexpected error while processing your request. Don't worry, our team has been notified and we're working to fix this issue." }}
            </p>

            <!-- Error Details (for development/debugging) -->
            {% if error_details and debug %}
            <div class="error-details">
                <h6><i class="fas fa-info-circle me-2"></i>Technical Details</h6>
                <p><strong>Error Type:</strong> <code>{{ error_details.type|default:"Unknown" }}</code></p>
                {% if error_details.message %}
                <p><strong>Message:</strong> <code>{{ error_details.message }}</code></p>
                {% endif %}
                {% if error_details.file %}
                <p><strong>File:</strong> <code>{{ error_details.file }}</code></p>
                {% endif %}
                {% if error_details.line %}
                <p><strong>Line:</strong> <code>{{ error_details.line }}</code></p>
                {% endif %}
            </div>
            {% endif %}

            <!-- Recovery Actions -->
            <div class="recovery-actions">
                {% if error_code == '404' %}
                    <a href="{% url 'venues_app:venue_list' %}" class="btn-recovery">
                        <i class="fas fa-search me-2"></i>Browse Services
                    </a>
                    <a href="{% url 'home_app:home' %}" class="btn-secondary-recovery">
                        <i class="fas fa-home me-2"></i>Go Home
                    </a>
                {% elif error_code == '403' %}
                    <a href="{% url 'accounts_app:login' %}" class="btn-recovery">
                        <i class="fas fa-sign-in-alt me-2"></i>Sign In
                    </a>
                    <a href="{% url 'home_app:home' %}" class="btn-secondary-recovery">
                        <i class="fas fa-home me-2"></i>Go Home
                    </a>
                {% elif error_code == '500' %}
                    <button onclick="window.location.reload()" class="btn-recovery">
                        <i class="fas fa-refresh me-2"></i>Try Again
                    </button>
                    <a href="{% url 'home_app:home' %}" class="btn-secondary-recovery">
                        <i class="fas fa-home me-2"></i>Go Home
                    </a>
                {% else %}
                    <button onclick="window.location.reload()" class="btn-recovery">
                        <i class="fas fa-refresh me-2"></i>Refresh Page
                    </button>
                    <a href="{% url 'booking_cart_app:cart_view' %}" class="btn-secondary-recovery">
                        <i class="fas fa-shopping-cart me-2"></i>View Cart
                    </a>
                    <a href="{% url 'home_app:home' %}" class="btn-secondary-recovery">
                        <i class="fas fa-home me-2"></i>Go Home
                    </a>
                {% endif %}
            </div>

            <!-- Help Section -->
            <div class="help-section">
                <div class="help-icon animate-bounce">
                    <i class="fas fa-life-ring"></i>
                </div>
                <h4 class="mb-3">Need Help?</h4>
                <p class="mb-4">Our support team is here to assist you. Choose your preferred way to get in touch:</p>
                
                <div class="contact-options">
                    <a href="tel:+1234567890" class="contact-option">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h6>Call Us</h6>
                        <p class="small text-muted mb-0">+****************</p>
                        <p class="small text-muted">Available 24/7</p>
                    </a>
                    
                    <a href="mailto:<EMAIL>" class="contact-option">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h6>Email Support</h6>
                        <p class="small text-muted mb-0"><EMAIL></p>
                        <p class="small text-muted">Response within 2 hours</p>
                    </a>
                    
                    <a href="#" class="contact-option" onclick="openLiveChat()">
                        <div class="contact-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h6>Live Chat</h6>
                        <p class="small text-muted mb-0">Chat with our team</p>
                        <p class="small text-muted">Online now</p>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-retry for 500 errors after 5 seconds (optional)
    {% if error_code == '500' and auto_retry %}
    let countdown = 5;
    const retryBtn = document.querySelector('.btn-recovery');
    const originalText = retryBtn.innerHTML;
    
    const countdownTimer = setInterval(function() {
        retryBtn.innerHTML = `<i class="fas fa-refresh me-2"></i>Auto-retry in ${countdown}s`;
        countdown--;
        
        if (countdown < 0) {
            clearInterval(countdownTimer);
            retryBtn.innerHTML = '<i class="fas fa-refresh me-2"></i>Retrying...';
            window.location.reload();
        }
    }, 1000);
    
    // Allow manual retry to cancel auto-retry
    retryBtn.addEventListener('click', function() {
        clearInterval(countdownTimer);
        retryBtn.innerHTML = originalText;
    });
    {% endif %}
    
    // Send error report (optional)
    {% if send_error_report %}
    fetch('/api/error-report/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: JSON.stringify({
            error_code: '{{ error_code|default:"unknown" }}',
            error_message: '{{ error_message|escapejs }}',
            user_agent: navigator.userAgent,
            url: window.location.href,
            timestamp: new Date().toISOString()
        })
    }).catch(err => console.log('Error report failed:', err));
    {% endif %}
});

function openLiveChat() {
    // Implement your live chat solution here
    alert('Live chat feature would be implemented here');
}

// Add some interactive feedback
document.querySelectorAll('.btn-recovery, .btn-secondary-recovery').forEach(button => {
    button.addEventListener('click', function() {
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
            this.style.transform = '';
        }, 150);
    });
});
</script>
{% endblock %} 