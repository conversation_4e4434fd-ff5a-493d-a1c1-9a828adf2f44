# Accounts App Comprehensive Test Suite

## Overview

This directory contains a comprehensive test suite for the `accounts_app` Django application. The tests are organized into multiple directories based on functionality and follow best practices for Django testing.

## Test Structure

```
accounts_app/tests/
├── __init__.py                     # Main test discovery file
├── test_config.py                  # Test configuration and utilities
├── README.md                       # This file
├── test_models/                    # Model tests
│   ├── __init__.py
│   ├── test_custom_user.py         # CustomUser model tests
│   ├── test_user_profile.py        # UserProfile model tests
│   ├── test_customer_profile.py    # CustomerProfile model tests
│   └── test_security_models.py     # Security-related model tests
├── test_views/                     # View tests
│   ├── __init__.py
│   ├── test_customer_views.py      # Customer view tests
│   └── test_common_views.py        # Common view tests
├── test_forms/                     # Form tests
│   ├── __init__.py
│   └── test_customer_forms.py      # Customer form tests
├── test_utils/                     # Utility tests
│   ├── __init__.py
│   ├── test_validators.py          # Custom validator tests
│   └── test_decorators.py          # Custom decorator tests
├── test_services/                  # Service layer tests
│   ├── __init__.py
│   └── test_auth_services.py       # Authentication service tests
├── test_security/                  # Security tests
│   └── __init__.py
├── test_middleware/                # Middleware tests
│   └── __init__.py
├── test_urls/                      # URL pattern tests
│   ├── __init__.py
│   └── test_url_patterns.py        # URL routing tests
├── test_templates/                 # Template tests
│   └── __init__.py
└── test_integration/               # Integration tests
    └── __init__.py
```

## Test Categories

### 1. Model Tests (`test_models/`)
- **CustomUser**: User creation, authentication, managers, properties
- **UserProfile**: Profile management, validation, relationships
- **CustomerProfile**: Customer-specific profile functionality
- **Security Models**: UserSecurity, AccountLockout, EmailVerificationToken

### 2. View Tests (`test_views/`)
- **Customer Views**: Login, registration, profile management
- **Common Views**: Password reset, email verification, shared functionality
- **Authentication**: Login/logout flows, permissions
- **Profile Management**: CRUD operations, validation

### 3. Form Tests (`test_forms/`)
- **Registration Forms**: Customer/provider registration validation
- **Login Forms**: Authentication form validation
- **Profile Forms**: Profile update forms
- **Security Forms**: Password change, email change forms

### 4. Utility Tests (`test_utils/`)
- **Validators**: Custom field validators (password strength, phone numbers, etc.)
- **Decorators**: Custom decorators (login_required, role_required, etc.)
- **Helper Functions**: Utility functions and helpers

### 5. Service Tests (`test_services/`)
- **Authentication Services**: Login/logout, session management
- **Email Services**: Email verification, password reset
- **Security Services**: Two-factor authentication, account lockout

### 6. URL Tests (`test_urls/`)
- **URL Patterns**: URL routing and resolution
- **Permissions**: URL-level access control
- **Parameters**: URL parameter handling

## Running Tests

### Prerequisites
```bash
pip install pytest
pip install coverage
```

### All Tests
```bash
# Using Django's test runner
python manage.py test accounts_app.tests

# Using pytest
pytest accounts_app/tests/
```

### Specific Test Categories
```bash
# Model tests only
python manage.py test accounts_app.tests.test_models

# View tests only  
python manage.py test accounts_app.tests.test_views

# Form tests only
python manage.py test accounts_app.tests.test_forms

# Utility tests only
python manage.py test accounts_app.tests.test_utils
```

### Specific Test Files
```bash
# Test specific model
python manage.py test accounts_app.tests.test_models.test_custom_user

# Test specific view
python manage.py test accounts_app.tests.test_views.test_customer_views

# Test specific form
python manage.py test accounts_app.tests.test_forms.test_customer_forms
```

### With Coverage
```bash
# Run tests with coverage
coverage run --source=accounts_app manage.py test accounts_app.tests
coverage report
coverage html

# View detailed coverage report
open htmlcov/index.html
```

### Parallel Testing
```bash
# Run tests in parallel (faster)
python manage.py test accounts_app.tests --parallel 4
```

## Test Data and Factories

### Using TestDataFactory
```python
from accounts_app.tests.test_config import TestDataFactory

factory = TestDataFactory()

# Create test users
customer = factory.create_customer_user()
provider = factory.create_provider_user()
admin = factory.create_admin_user()

# Create with custom data
custom_user = factory.create_customer_user(
    email='<EMAIL>',
    password='custom_password'
)
```

### Test Utilities
```python
from accounts_app.tests.test_config import TestUtils

utils = TestUtils()

# Create test files
test_file = utils.create_test_file('content', 'test.txt')
test_image = utils.create_test_image(width=200, height=200)

# Assert redirects
utils.assert_redirects_to_login(response)
```

## Test Best Practices

### 1. Test Naming
- Use descriptive test names that explain what is being tested
- Follow the pattern: `test_<action>_<condition>_<expected_result>`

```python
def test_user_login_with_valid_credentials_succeeds(self):
    """Test that user can login with valid credentials"""
    
def test_user_login_with_invalid_password_fails(self):
    """Test that user cannot login with invalid password"""
```

### 2. Test Structure (AAA Pattern)
- **Arrange**: Set up test data
- **Act**: Execute the action being tested
- **Assert**: Verify the expected outcome

```python
def test_user_creation(self):
    # Arrange
    user_data = {
        'email': '<EMAIL>',
        'password': 'testpass123',
        'role': UserRoles.CUSTOMER
    }
    
    # Act
    user = User.objects.create_user(**user_data)
    
    # Assert
    self.assertEqual(user.email, '<EMAIL>')
    self.assertEqual(user.role, UserRoles.CUSTOMER)
```

### 3. Test Isolation
- Each test should be independent
- Use `setUp()` and `tearDown()` methods for common test data
- Don't rely on data from other tests

### 4. Mocking External Dependencies
```python
from unittest.mock import patch, MagicMock

@patch('accounts_app.services.send_email')
def test_email_verification_sends_email(self, mock_send_email):
    # Test implementation
    mock_send_email.assert_called_once()
```

### 5. Testing Both Success and Failure Cases
```python
def test_password_validation_success(self):
    """Test password validation with valid password"""
    # Test valid password
    
def test_password_validation_failure(self):
    """Test password validation with invalid password"""
    # Test invalid password
```

## Test Configuration

### Settings
The test suite uses optimized settings for faster test execution:
- In-memory SQLite database
- Faster password hashing
- Local memory email backend
- Disabled caching

### Environment Variables
```bash
# Set test environment
export DJANGO_SETTINGS_MODULE=project_root.settings.test

# Enable debug mode for tests
export DJANGO_DEBUG=True
```

## Continuous Integration

### GitHub Actions Example
```yaml
name: Run Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.9
      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Run tests
        run: python manage.py test accounts_app.tests
      - name: Run coverage
        run: |
          coverage run --source=accounts_app manage.py test accounts_app.tests
          coverage report
```

## Performance Testing

### Load Testing
```python
def test_authentication_performance(self):
    """Test authentication performance under load"""
    import time
    
    start_time = time.time()
    for i in range(100):
        authenticate_user('<EMAIL>', 'password')
    end_time = time.time()
    
    # Should complete within reasonable time
    self.assertLess(end_time - start_time, 1.0)
```

### Memory Testing
```python
def test_memory_usage(self):
    """Test memory usage during operations"""
    import psutil
    import os
    
    process = psutil.Process(os.getpid())
    initial_memory = process.memory_info().rss
    
    # Perform operations
    for i in range(1000):
        User.objects.create_user(f'user{i}@example.com', 'password')
    
    final_memory = process.memory_info().rss
    memory_increase = final_memory - initial_memory
    
    # Memory increase should be reasonable
    self.assertLess(memory_increase, 50 * 1024 * 1024)  # 50MB
```

## Security Testing

### Authentication Tests
- Test login/logout flows
- Test password strength validation
- Test account lockout mechanisms
- Test session management

### Authorization Tests
- Test role-based access control
- Test permission checking
- Test unauthorized access attempts

### Data Protection Tests
- Test password hashing
- Test sensitive data handling
- Test CSRF protection
- Test XSS prevention

## Debugging Tests

### Running Individual Tests
```bash
# Run single test method
python manage.py test accounts_app.tests.test_models.test_custom_user.CustomUserModelTests.test_user_creation

# Run with verbose output
python manage.py test accounts_app.tests --verbosity=2

# Run with pdb debugging
python manage.py test accounts_app.tests --pdb
```

### Common Issues
1. **Database errors**: Ensure test database permissions
2. **Import errors**: Check Python path and module structure
3. **Async issues**: Use `sync_to_async` for async code testing
4. **Media files**: Use temporary directories for file uploads

## Test Metrics

### Coverage Goals
- **Overall Coverage**: > 90%
- **Model Coverage**: > 95%
- **View Coverage**: > 85%
- **Form Coverage**: > 90%
- **Utility Coverage**: > 95%

### Performance Benchmarks
- **Individual Test**: < 1 second
- **Full Test Suite**: < 5 minutes
- **Memory Usage**: < 100MB increase
- **Database Queries**: < 10 per test (average)

## Contributing

### Adding New Tests
1. Choose appropriate test directory based on functionality
2. Follow existing naming conventions
3. Include both positive and negative test cases
4. Add docstrings explaining test purpose
5. Update this README if adding new test categories

### Test Review Checklist
- [ ] Tests are properly organized
- [ ] Test names are descriptive
- [ ] Both success and failure cases covered
- [ ] External dependencies are mocked
- [ ] Tests are isolated and independent
- [ ] Performance considerations addressed
- [ ] Security implications tested
- [ ] Documentation updated

## Troubleshooting

### Common Test Failures
1. **Database connection errors**: Check database settings
2. **Missing dependencies**: Install required packages
3. **Permission errors**: Check file/directory permissions
4. **Timeout errors**: Increase test timeout settings
5. **Memory errors**: Optimize test data usage

### Getting Help
- Check Django testing documentation
- Review existing test patterns in the codebase
- Ask questions in team chat or issue tracker
- Run tests with `--debug-mode` for detailed output

---

This comprehensive test suite ensures the reliability, security, and performance of the accounts_app. Regular testing and maintenance of these tests is crucial for maintaining code quality and catching regressions early. 