{% extends 'base.html' %}

{% block extra_css %}
    {% load static %}
    {% load widget_tweaks %}

    <!-- Preconnect for Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* CozyWish Design System - Venues Base */
        :root {
            /* Brand Colors */
            --cw-brand-primary: #2F160F;
            --cw-brand-light: #4a2a1f;
            --cw-brand-accent: #fae1d7;
            --cw-accent-light: #fef7f0;
            --cw-accent-dark: #f1d4c4;

            /* Secondary Colors */
            --cw-secondary-50: #f9f7f4;
            --cw-secondary-100: #f1ebe2;
            --cw-secondary-200: #e3d5c4;
            --cw-secondary-300: #d1b89e;
            --cw-secondary-400: #bc9876;
            --cw-secondary-500: #ad7f5a;
            --cw-secondary-600: #a0704e;
            --cw-secondary-700: #855a42;
            --cw-secondary-800: #6c4a39;
            --cw-secondary-900: #583d30;
            --cw-secondary-950: #2f1f18;

            /* Neutral Colors */
            --cw-neutral-50: #fafafa;
            --cw-neutral-100: #f5f5f5;
            --cw-neutral-200: #e5e5e5;
            --cw-neutral-300: #d4d4d4;
            --cw-neutral-400: #a3a3a3;
            --cw-neutral-500: #737373;
            --cw-neutral-600: #525252;
            --cw-neutral-700: #404040;
            --cw-neutral-800: #262626;
            --cw-neutral-900: #171717;
            --cw-neutral-950: #0a0a0a;

            /* Semantic Colors */
            --cw-success: #059669;
            --cw-warning: #d97706;
            --cw-error: #dc2626;
            --cw-info: #0284c7;

            /* Typography */
            --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

            /* Shadows */
            --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --cw-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --cw-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

            /* Gradients */
            --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
            --cw-gradient-hero-alt: radial-gradient(ellipse at center, var(--cw-accent-light) 0%, var(--cw-accent-dark) 40%, #ffffff 100%);
            --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
            --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
            --cw-gradient-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
            --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
            --cw-gradient-accent: linear-gradient(135deg, var(--cw-brand-accent) 0%, var(--cw-accent-dark) 100%);
        }

        /* Global Styles */
        body {
            font-family: var(--cw-font-primary);
            line-height: 1.6;
            color: var(--cw-neutral-800);
            background: var(--cw-gradient-hero);
            min-height: 100vh;
        }

        /* Typography */
        h1, h2, h3, h4, h5, h6 {
            font-family: var(--cw-font-heading);
            font-weight: 600;
            color: var(--cw-secondary-950);
            line-height: 1.3;
        }

        .display-font {
            font-family: var(--cw-font-display);
        }

        /* Venues wrapper - professional background */
        .venues-wrapper {
            background: var(--cw-gradient-hero);
            min-height: 100vh;
            padding: 2rem 0;
            font-family: var(--cw-font-primary);
        }

        /* Typography */
        .venues-wrapper h1, .venues-wrapper h2, .venues-wrapper h3,
        .venues-wrapper h4, .venues-wrapper h5, .venues-wrapper h6 {
            font-family: var(--cw-font-heading);
            font-weight: 600;
            color: var(--cw-brand-primary);
            margin-bottom: 1rem;
        }

        .venues-wrapper h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .venues-wrapper .lead {
            font-size: 1.1rem;
            color: var(--cw-neutral-600);
            margin-bottom: 2rem;
        }

        /* Custom Cards */
        .venues-wrapper .card {
            border: 1px solid var(--cw-neutral-200);
            border-radius: 1rem;
            box-shadow: var(--cw-shadow-md);
            background: white;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .venues-wrapper .card:hover {
            transform: translateY(-2px);
            box-shadow: var(--cw-shadow-lg);
        }

        .venues-wrapper .card-cw {
            border: 1px solid var(--cw-neutral-200);
            border-radius: 1rem;
            box-shadow: var(--cw-shadow-md);
            background: white;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .venues-wrapper .card-cw:hover {
            transform: translateY(-2px);
            box-shadow: var(--cw-shadow-lg);
        }

        .venues-wrapper .card-cw-featured {
            border: 2px solid var(--cw-brand-primary);
            background: var(--cw-gradient-card-subtle);
        }

        .venues-wrapper .card-cw-brand {
            border: 2px solid var(--cw-brand-primary);
            background: var(--cw-gradient-card);
            box-shadow: 0 10px 15px -3px rgba(47, 22, 15, 0.1);
        }

        .venues-wrapper .card-cw-accent {
            border: 1px solid var(--cw-brand-accent);
            background: var(--cw-brand-accent);
            box-shadow: var(--cw-shadow-sm);
        }

        .venues-wrapper .card-header {
            background: white !important;
            color: var(--cw-brand-primary) !important;
            border-bottom: 2px solid var(--cw-brand-accent);
            padding: 1rem 1.5rem;
            font-weight: 600;
        }

        .venues-wrapper .card-header h1,
        .venues-wrapper .card-header h2,
        .venues-wrapper .card-header h3,
        .venues-wrapper .card-header h4,
        .venues-wrapper .card-header h5,
        .venues-wrapper .card-header h6 {
            color: var(--cw-brand-primary) !important;
            margin-bottom: 0;
        }

        .venues-wrapper .card-header .mb-0 {
            color: var(--cw-brand-primary) !important;
        }

        .venues-wrapper .card-body {
            padding: 1.5rem;
        }

        .venues-wrapper .card-title {
            color: var(--cw-brand-primary);
            font-weight: 600;
            margin-bottom: 0.75rem;
        }

        /* Custom Buttons */
        .venues-wrapper .btn {
            font-family: var(--cw-font-primary);
            font-weight: 500;
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .venues-wrapper .btn-cw-primary {
            background: var(--cw-gradient-brand-button);
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            color: white;
            transition: all 0.2s ease;
            box-shadow: var(--cw-shadow-sm);
        }
        .venues-wrapper .btn-cw-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
            color: white;
        }

        .venues-wrapper .btn-cw-brand {
            background: var(--cw-gradient-brand-button);
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            color: white;
            transition: all 0.2s ease;
            box-shadow: var(--cw-shadow-sm);
        }
        .venues-wrapper .btn-cw-brand:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
            color: white;
        }

        .venues-wrapper .btn-cw-secondary {
            border: 2px solid var(--cw-brand-primary);
            color: var(--cw-brand-primary);
            border-radius: 0.5rem;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            background: white;
            transition: all 0.2s ease;
        }
        .venues-wrapper .btn-cw-secondary:hover {
            background: var(--cw-brand-primary);
            color: white;
            transform: translateY(-1px);
        }

        .venues-wrapper .btn-cw-brand-outline {
            border: 2px solid var(--cw-brand-primary);
            color: var(--cw-brand-primary);
            border-radius: 0.5rem;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            background: white;
            transition: all 0.2s ease;
        }
        .venues-wrapper .btn-cw-brand-outline:hover {
            background: var(--cw-brand-primary);
            color: white;
            transform: translateY(-1px);
        }

        .venues-wrapper .btn-cw-accent {
            background: var(--cw-gradient-accent);
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            color: var(--cw-brand-primary);
            transition: all 0.2s ease;
            box-shadow: var(--cw-shadow-sm);
        }
        .venues-wrapper .btn-cw-accent:hover {
            transform: translateY(-1px);
            box-shadow: var(--cw-shadow-md);
            color: var(--cw-brand-primary);
        }

        .venues-wrapper .btn-cw-accent-outline {
            border: 2px solid var(--cw-brand-accent);
            color: var(--cw-brand-primary);
            border-radius: 0.5rem;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            background: white;
            transition: all 0.2s ease;
        }
        .venues-wrapper .btn-cw-accent-outline:hover {
            background: var(--cw-brand-accent);
            color: var(--cw-brand-primary);
            transform: translateY(-1px);
        }

        .venues-wrapper .btn-cw-ghost {
            color: var(--cw-brand-primary);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border: none;
            background: transparent;
            transition: all 0.2s ease;
        }
        .venues-wrapper .btn-cw-ghost:hover {
            color: var(--cw-brand-light);
            text-decoration: none;
        }

        /* Legacy button support */
        .venues-wrapper .btn-primary {
            background: var(--cw-gradient-brand-button);
            color: white;
            border: none;
        }

        .venues-wrapper .btn-primary:hover {
            background: var(--cw-brand-light);
            color: white;
        }

        .venues-wrapper .btn-outline-primary {
            background: white;
            color: var(--cw-brand-primary);
            border: 2px solid var(--cw-brand-primary);
        }

        .venues-wrapper .btn-outline-primary:hover {
            background: var(--cw-brand-primary);
            color: white;
        }

        .venues-wrapper .btn-outline-secondary {
            background: white;
            color: var(--cw-brand-primary);
            border: 2px solid var(--cw-brand-primary);
        }

        .venues-wrapper .btn-outline-secondary:hover {
            background: var(--cw-brand-primary);
            color: white;
        }

        /* Custom Forms */
        .venues-wrapper .form-control-cw {
            border: 2px solid var(--cw-neutral-200);
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.2s ease;
        }
        .venues-wrapper .form-control-cw:focus {
            border-color: var(--cw-brand-primary);
            box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        }

        .venues-wrapper .form-label {
            color: var(--cw-brand-primary);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .venues-wrapper .form-control,
        .venues-wrapper .form-select {
            border: 2px solid var(--cw-neutral-200);
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            font-family: var(--cw-font-primary);
            background: white;
            color: var(--cw-neutral-800);
            transition: all 0.2s ease;
        }

        .venues-wrapper .form-control:focus,
        .venues-wrapper .form-select:focus {
            border-color: var(--cw-brand-primary);
            box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
            background: white;
            color: var(--cw-neutral-800);
        }

        .venues-wrapper .form-control.is-invalid {
            border-color: var(--cw-error);
        }

        .venues-wrapper .invalid-feedback {
            color: var(--cw-error);
            font-weight: 500;
            margin-top: 0.5rem;
        }

        .venues-wrapper .form-text {
            color: var(--cw-neutral-600);
            font-size: 0.875rem;
        }

        /* Tables - professional styling */
        .venues-wrapper .table {
            color: var(--cw-neutral-800);
            border-color: var(--cw-neutral-200);
        }

        .venues-wrapper .table th {
            background: var(--cw-accent-light);
            color: var(--cw-brand-primary);
            border-color: var(--cw-neutral-200);
            font-weight: 600;
            padding: 1rem;
        }

        .venues-wrapper .table td {
            border-color: var(--cw-neutral-200);
            padding: 1rem;
            vertical-align: middle;
        }

        .venues-wrapper .table-hover tbody tr:hover {
            background-color: var(--cw-accent-light);
        }

        /* Badges */
        .venues-wrapper .badge-cw-primary {
            background: var(--cw-brand-primary);
            color: white;
            font-weight: 500;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
        }

        .venues-wrapper .badge-cw-secondary {
            background: var(--cw-neutral-100);
            color: var(--cw-neutral-700);
            font-weight: 500;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
        }

        .venues-wrapper .badge {
            font-weight: 500;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
        }

        /* Bootstrap badge overrides for brand consistency */
        .venues-wrapper .badge.bg-success {
            background-color: var(--cw-success) !important;
            color: white !important;
        }

        .venues-wrapper .badge.bg-secondary {
            background-color: var(--cw-neutral-500) !important;
            color: white !important;
        }

        .venues-wrapper .badge.bg-primary {
            background-color: var(--cw-brand-primary) !important;
            color: white !important;
        }

        .venues-wrapper .badge.bg-info {
            background-color: var(--cw-info) !important;
            color: white !important;
        }

        .venues-wrapper .badge.bg-warning {
            background-color: var(--cw-warning) !important;
            color: white !important;
        }

        .venues-wrapper .badge.bg-danger {
            background-color: var(--cw-error) !important;
            color: white !important;
        }

        .venues-wrapper .badge-approved {
            background: var(--cw-success);
            color: white;
        }

        .venues-wrapper .badge-pending {
            background: var(--cw-warning);
            color: white;
        }

        .venues-wrapper .badge-rejected {
            background: var(--cw-error);
            color: white;
        }

        /* Alerts */
        .venues-wrapper .alert-cw-success {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #166534;
            border-radius: 0.5rem;
        }

        .venues-wrapper .alert-cw-warning {
            background: #fffbeb;
            border: 1px solid #fed7aa;
            color: #92400e;
            border-radius: 0.5rem;
        }

        .venues-wrapper .alert-cw-error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #991b1b;
            border-radius: 0.5rem;
        }

        .venues-wrapper .alert {
            border-radius: 0.5rem;
            border: 1px solid var(--cw-neutral-200);
        }

        .venues-wrapper .alert-info {
            background: var(--cw-accent-light);
            border-color: var(--cw-brand-accent);
            color: var(--cw-brand-primary);
        }

        .venues-wrapper .alert-danger {
            background: #fef2f2;
            border-color: #fecaca;
            color: #991b1b;
        }

        /* Pagination - professional styling */
        .venues-wrapper .pagination .page-link {
            color: var(--cw-brand-primary);
            background: white;
            border: 1px solid var(--cw-neutral-200);
            margin: 0 0.125rem;
            border-radius: 0.375rem;
        }

        .venues-wrapper .pagination .page-link:hover {
            color: var(--cw-brand-primary);
            background: var(--cw-accent-light);
            border-color: var(--cw-brand-accent);
        }

        .venues-wrapper .pagination .page-item.active .page-link {
            color: white;
            background: var(--cw-brand-primary);
            border-color: var(--cw-brand-primary);
        }

        .venues-wrapper .pagination .page-item.disabled .page-link {
            color: var(--cw-neutral-400);
            background: white;
            border-color: var(--cw-neutral-200);
        }

        /* Statistics cards */
        .venues-wrapper .stat-card {
            text-align: center;
            padding: 2rem 1rem;
        }

        .venues-wrapper .stat-card i {
            font-size: 3rem;
            color: var(--cw-brand-primary);
            margin-bottom: 1rem;
        }

        .venues-wrapper .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--cw-brand-primary);
            margin-bottom: 0.5rem;
        }

        .venues-wrapper .stat-label {
            color: var(--cw-neutral-600);
            font-weight: 500;
        }

        /* Additional Bootstrap component styling for brand consistency */
        .venues-wrapper .btn-success {
            background: var(--cw-success) !important;
            color: white !important;
            border-color: var(--cw-success) !important;
        }

        .venues-wrapper .btn-success:hover {
            background: #047857 !important;
            color: white !important;
            border-color: #047857 !important;
        }

        .venues-wrapper .btn-danger {
            background: var(--cw-error) !important;
            color: white !important;
            border-color: var(--cw-error) !important;
        }

        .venues-wrapper .btn-danger:hover {
            background: #b91c1c !important;
            color: white !important;
            border-color: #b91c1c !important;
        }

        .venues-wrapper .btn-info {
            background: var(--cw-info) !important;
            color: white !important;
            border-color: var(--cw-info) !important;
        }

        .venues-wrapper .btn-info:hover {
            background: #0369a1 !important;
            color: white !important;
            border-color: #0369a1 !important;
        }

        .venues-wrapper .btn-warning {
            background: var(--cw-warning) !important;
            color: white !important;
            border-color: var(--cw-warning) !important;
        }

        .venues-wrapper .btn-warning:hover {
            background: #b45309 !important;
            color: white !important;
            border-color: #b45309 !important;
        }

        /* List group items */
        .venues-wrapper .list-group-item {
            background: white !important;
            color: var(--cw-neutral-800) !important;
            border: 1px solid var(--cw-neutral-200) !important;
        }

        .venues-wrapper .list-group-item:hover {
            background: var(--cw-accent-light) !important;
        }

        /* Text colors for brand consistency */
        .venues-wrapper .text-muted {
            color: var(--cw-neutral-600) !important;
        }

        .venues-wrapper .text-success {
            color: var(--cw-success) !important;
        }

        .venues-wrapper .text-danger {
            color: var(--cw-error) !important;
        }

        .venues-wrapper .text-info {
            color: var(--cw-info) !important;
        }

        .venues-wrapper .text-warning {
            color: var(--cw-warning) !important;
        }

        /* Brand Colors */
        .venues-wrapper .text-brand-cw { color: var(--cw-brand-primary) !important; }
        .venues-wrapper .text-brand-light-cw { color: var(--cw-brand-light) !important; }
        .venues-wrapper .text-accent-cw { color: var(--cw-brand-accent) !important; }
        .venues-wrapper .text-secondary-cw { color: var(--cw-secondary-950) !important; }
        .venues-wrapper .text-neutral-cw { color: var(--cw-neutral-600) !important; }
        .venues-wrapper .bg-brand-cw { background-color: var(--cw-brand-primary) !important; }
        .venues-wrapper .bg-brand-accent-cw { background-color: var(--cw-brand-accent) !important; }
        .venues-wrapper .bg-accent-dark-cw { background-color: var(--cw-accent-dark) !important; }
        .venues-wrapper .bg-light-cw { background-color: var(--cw-accent-light) !important; }

        /* Utility Classes */
        .venues-wrapper .shadow-cw-sm { box-shadow: var(--cw-shadow-sm); }
        .venues-wrapper .shadow-cw-md { box-shadow: var(--cw-shadow-md); }
        .venues-wrapper .shadow-cw-lg { box-shadow: var(--cw-shadow-lg); }

        .venues-wrapper .rounded-cw { border-radius: 0.5rem; }
        .venues-wrapper .rounded-cw-lg { border-radius: 1rem; }
        .venues-wrapper .rounded-cw-full { border-radius: 9999px; }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .venues-wrapper {
                padding: 1rem 0;
            }

            .venues-wrapper h1 {
                font-size: 2rem;
            }

            .venues-wrapper .card-body {
                padding: 1rem;
            }

            .venues-wrapper .btn {
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }
        }
    </style>

    {% block venues_extra_css %}{% endblock %}
{% endblock %}

{% block content %}
<div class="venues-wrapper">
    <div class="container-fluid">
        <div class="container">
            {% block venues_content %}{% endblock %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
    {% block venues_extra_js %}{% endblock %}
{% endblock %}
