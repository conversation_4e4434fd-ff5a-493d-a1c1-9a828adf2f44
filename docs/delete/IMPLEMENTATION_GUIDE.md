# Django Modernization Implementation Guide

## 🚀 Getting Started

### Prerequisites
1. **Backup your current project completely**
2. **Create a new git branch:** `git checkout -b modernization`
3. **Ensure Python 3.11+ is installed**
4. **Have Redis server available** (for caching)

## 📋 Phase 1: Development Environment Setup

### Step 1.1: Install Development Tools

```bash
# Install new packages
pip install django-debug-toolbar==4.4.6
pip install django-extensions==3.2.3
pip install django-environ==0.11.2
pip install sentry-sdk[django]==2.18.0

# Install code quality tools
pip install black==24.10.0
pip install isort==5.13.2
pip install flake8==7.1.1
pip install pre-commit==4.0.1
```

### Step 1.2: Update Settings

Add to `INSTALLED_APPS` in `settings.py`:
```python
INSTALLED_APPS = [
    # ... existing apps ...
    'debug_toolbar',  # Add for development only
    'django_extensions',
]

# Add debug toolbar middleware (development only)
if DEBUG:
    MIDDLEWARE.insert(0, 'debug_toolbar.middleware.DebugToolbarMiddleware')
    INTERNAL_IPS = ['127.0.0.1', 'localhost']
```

### Step 1.3: Setup Code Formatting

Create `.pre-commit-config.yaml`:
```yaml
repos:
  - repo: https://github.com/psf/black
    rev: 24.10.0
    hooks:
      - id: black
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
  - repo: https://github.com/pycqa/flake8
    rev: 7.1.1
    hooks:
      - id: flake8
```

Install pre-commit hooks:
```bash
pre-commit install
```

## 📋 Phase 2: Authentication Modernization

### Step 2.1: Install Authentication Packages

```bash
pip install django-allauth==65.4.0
pip install django-guardian==2.4.0
pip install django-otp==1.5.4
pip install django-simple-history==3.8.0
```

### Step 2.2: Update Settings for Allauth

Add to `INSTALLED_APPS`:
```python
INSTALLED_APPS = [
    # ... existing apps ...
    'allauth',
    'allauth.account',
    'allauth.socialaccount',
    'allauth.socialaccount.providers.google',
    'allauth.socialaccount.providers.apple',
    'guardian',
    'django_otp',
    'simple_history',
]

# Allauth settings
AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',
    'allauth.account.auth_backends.AuthenticationBackend',
    'guardian.backends.ObjectPermissionBackend',
]

ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_USERNAME_REQUIRED = False
ACCOUNT_AUTHENTICATION_METHOD = 'email'
ACCOUNT_EMAIL_VERIFICATION = 'mandatory'
```

### Step 2.3: Update URLs

Add to `project_root/urls.py`:
```python
urlpatterns = [
    # ... existing patterns ...
    path('accounts/', include('allauth.urls')),
]
```

## 📋 Phase 3: API Development

### Step 3.1: Install DRF Packages

```bash
pip install djangorestframework==3.15.2
pip install djangorestframework-simplejwt==5.3.0
pip install drf-spectacular==0.27.2
pip install django-cors-headers==4.4.0
pip install django-filter==24.3
```

### Step 3.2: Update Settings for DRF

Add to `INSTALLED_APPS`:
```python
INSTALLED_APPS = [
    # ... existing apps ...
    'rest_framework',
    'rest_framework_simplejwt',
    'drf_spectacular',
    'corsheaders',
    'django_filters',
]

# DRF Settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter',
    ],
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
}

# JWT Settings
from datetime import timedelta
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=60),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
}

# CORS Settings
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",  # React dev server
    "http://127.0.0.1:3000",
]

# API Documentation
SPECTACULAR_SETTINGS = {
    'TITLE': 'CozyWish API',
    'DESCRIPTION': 'Venue booking and management platform API',
    'VERSION': '1.0.0',
    'SERVE_INCLUDE_SCHEMA': False,
}
```

### Step 3.3: Create API Structure

Create `api/` directory in project root:
```bash
mkdir api
touch api/__init__.py
touch api/urls.py
touch api/serializers.py
touch api/views.py
touch api/permissions.py
```

## 📋 Phase 4: Form & UI Improvements

### Step 4.1: Install Form Packages

```bash
pip install crispy-bootstrap5==2024.10
pip install django-formtools==2.5.1
pip install django-htmx==1.19.0
pip install django-cleanup==8.1.0
```

### Step 4.2: Update Settings

```python
INSTALLED_APPS = [
    # ... existing apps ...
    'crispy_forms',
    'crispy_bootstrap5',  # Replace crispy_bootstrap4
    'formtools',
    'django_htmx',
    'django_cleanup',
]

# Update crispy forms settings
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
CRISPY_TEMPLATE_PACK = "bootstrap5"
```

## 📋 Phase 5: Search Enhancement

### Step 5.1: Install Search Packages

```bash
pip install django-elasticsearch-dsl==8.0
```

### Step 5.2: Configure Elasticsearch

Add to settings:
```python
INSTALLED_APPS = [
    # ... existing apps ...
    'django_elasticsearch_dsl',
]

# Elasticsearch configuration
ELASTICSEARCH_DSL = {
    'default': {
        'hosts': 'localhost:9200'
    },
}
```

## 📋 Phase 6: Performance Optimization

### Step 6.1: Install Performance Packages

```bash
pip install django-redis==5.4.0
pip install django-silk==5.2.0
pip install django-compressor==4.5.1
pip install django-prometheus==2.3.1
```

### Step 6.2: Configure Redis Caching

Update settings:
```python
INSTALLED_APPS = [
    # ... existing apps ...
    'silk',
    'compressor',
    'django_prometheus',
]

# Redis Cache Configuration
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Session storage
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'
```

## 📋 Phase 7: Security Enhancement

### Step 7.1: Install Security Packages

```bash
pip install django-ratelimit==4.1.0
pip install django-auditlog==3.0.0
pip install django-health-check==3.18.3
pip install django-dbbackup==4.2.0
```

### Step 7.2: Configure Security

Add to settings:
```python
INSTALLED_APPS = [
    # ... existing apps ...
    'auditlog',
    'health_check',
    'health_check.db',
    'health_check.cache',
    'health_check.storage',
    'dbbackup',
]

# Security Headers
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'

# Rate limiting
RATELIMIT_ENABLE = True
```

## 📋 Phase 8: Testing Enhancement

### Step 8.1: Install Testing Packages

```bash
pip install pytest-django==4.9.0
pip install factory-boy==3.3.1
pip install faker==30.8.2
```

### Step 8.2: Create Test Configuration

Create `pytest.ini`:
```ini
[tool:pytest]
DJANGO_SETTINGS_MODULE = project_root.settings.testing
addopts = --reuse-db --nomigrations
python_files = tests.py test_*.py *_tests.py
```

## 🔄 Migration Commands

Run these commands after each phase:

```bash
# Make migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Collect static files
python manage.py collectstatic --noinput

# Run tests
python manage.py test

# Check for issues
python manage.py check
```

## ✅ Verification Steps

After each phase, verify:

1. **All tests pass:** `python manage.py test`
2. **No migration issues:** `python manage.py showmigrations`
3. **Static files work:** Check admin and debug toolbar
4. **No console errors:** Check browser developer tools
5. **Performance:** Monitor page load times

## 🚨 Troubleshooting

### Common Issues:

1. **Import Errors:** Check INSTALLED_APPS order
2. **Migration Conflicts:** Use `--merge` flag
3. **Static Files:** Run `collectstatic` after changes
4. **Cache Issues:** Clear Redis cache
5. **Permission Errors:** Check file permissions

### Rollback Strategy:

If issues occur:
1. **Git reset:** `git reset --hard HEAD~1`
2. **Database restore:** From backup
3. **Requirements:** `pip install -r requirements.txt`
4. **Migrations:** `python manage.py migrate`

## 📞 Next Steps

1. Complete Phase 1 and test thoroughly
2. Move to Phase 2 only after Phase 1 is stable
3. Document any custom configurations
4. Update deployment scripts
5. Train team on new tools and processes

Remember: **Test everything in development before production!**
