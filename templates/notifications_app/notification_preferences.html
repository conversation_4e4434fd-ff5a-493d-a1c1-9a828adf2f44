{% extends 'notifications_app/base_notifications.html' %}
{% load static %}

{% block title %}Notification Preferences - CozyWish{% endblock %}

{% block notifications_extra_css %}
<style>
    /* Minimal Notification Preferences Design */
    
    /* Page Container */
    .notifications-wrapper {
        background: white;
        min-height: 100vh;
        padding: 2rem 0;
    }

    .notifications-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* Page Header */
    .preferences-header {
        text-align: left;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--cw-neutral-200);
    }

    .preferences-title {
        font-family: var(--cw-font-heading);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-secondary-950);
        margin: 0 0 0.5rem 0;
        letter-spacing: -0.02em;
        text-transform: uppercase;
    }

    .preferences-subtitle {
        color: var(--cw-neutral-600);
        font-size: 1.125rem;
        margin: 0;
    }

    /* Preference Sections */
    .preference-section {
        background: white;
        border: 1px solid var(--cw-neutral-200);
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .section-title {
        font-family: var(--cw-font-heading);
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-secondary-950);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--cw-neutral-100);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--cw-neutral-600);
        font-size: 1.125rem;
    }

    .section-icon.email {
        background: rgba(2, 132, 199, 0.1);
        color: var(--cw-info);
    }

    .section-icon.push {
        background: rgba(5, 150, 105, 0.1);
        color: var(--cw-success);
    }

    .section-icon.frequency {
        background: rgba(217, 119, 6, 0.1);
        color: var(--cw-warning);
    }

    /* Preference Items */
    .preference-item {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 1.5rem;
        border: 1px solid var(--cw-neutral-200);
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        background: var(--cw-neutral-50);
        transition: all 0.2s ease;
    }

    .preference-item:hover {
        border-color: var(--cw-brand-primary);
        background: white;
    }

    .preference-item:last-child {
        margin-bottom: 0;
    }

    .preference-content {
        flex: 1;
        margin-right: 1rem;
    }

    .preference-title {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-secondary-950);
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    .preference-description {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        line-height: 1.5;
    }

    /* Toggle Switch */
    .preference-toggle {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 28px;
        flex-shrink: 0;
    }

    .preference-toggle input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: var(--cw-neutral-300);
        transition: .4s;
        border-radius: 28px;
    }

    .toggle-slider:before {
        position: absolute;
        content: "";
        height: 22px;
        width: 22px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    input:checked + .toggle-slider {
        background-color: var(--cw-brand-primary);
    }

    input:checked + .toggle-slider:before {
        transform: translateX(22px);
    }

    /* Form Elements */
    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: var(--cw-secondary-950);
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
        display: block;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .form-select {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid var(--cw-neutral-200);
        border-radius: 0.5rem;
        font-family: var(--cw-font-primary);
        font-size: 1rem;
        background: white;
        transition: all 0.2s ease;
    }

    .form-select:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    /* Action Buttons */
    .action-section {
        background: white;
        border: 1px solid var(--cw-neutral-200);
        border-radius: 0.75rem;
        padding: 2rem;
        text-align: center;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
        font-size: 0.875rem;
    }

    .action-btn.primary {
        background: var(--cw-brand-primary);
        color: white;
    }

    .action-btn.primary:hover {
        background: var(--cw-brand-light);
        color: white;
        transform: translateY(-1px);
    }

    .action-btn.secondary {
        background: white;
        color: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-primary);
    }

    .action-btn.secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .preferences-title {
            font-size: 2rem;
        }

        .preference-item {
            flex-direction: column;
            gap: 1rem;
        }

        .preference-content {
            margin-right: 0;
        }

        .preference-toggle {
            align-self: flex-start;
        }

        .action-buttons {
            flex-direction: column;
            align-items: stretch;
        }
    }
</style>
{% endblock %}

{% block notifications_content %}
<div class="notifications-wrapper">
    <div class="notifications-container">
        <!-- Page Header -->
        <div class="preferences-header">
            <h1 class="preferences-title">NOTIFICATION PREFERENCES</h1>
            <p class="preferences-subtitle">Customize how and when you receive notifications</p>
        </div>

        <!-- System Messages -->
        {% if messages %}
        <div class="mb-3">
            {% for message in messages %}
            <div class="alert alert-info alert-dismissible fade show notification-alert-cw" role="alert">
                <i class="fas fa-info-circle me-2"></i>{{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <form method="post">
            {% csrf_token %}

            <!-- Email Notifications -->
            <div class="preference-section">
                <h2 class="section-title">
                    <div class="section-icon email">
                        <i class="fas fa-envelope"></i>
                    </div>
                    Email Notifications
                </h2>

                <div class="preference-item">
                    <div class="preference-content">
                        <div class="preference-title">Booking Confirmations</div>
                        <div class="preference-description">Get notified when your bookings are confirmed or updated</div>
                    </div>
                    <label class="preference-toggle">
                        <input type="checkbox" name="email_booking" {% if preferences.email_booking %}checked{% endif %}>
                        <span class="toggle-slider"></span>
                    </label>
                </div>

                <div class="preference-item">
                    <div class="preference-content">
                        <div class="preference-title">Payment Updates</div>
                        <div class="preference-description">Receive notifications about payment confirmations and receipts</div>
                    </div>
                    <label class="preference-toggle">
                        <input type="checkbox" name="email_payment" {% if preferences.email_payment %}checked{% endif %}>
                        <span class="toggle-slider"></span>
                    </label>
                </div>

                <div class="preference-item">
                    <div class="preference-content">
                        <div class="preference-title">Reviews & Feedback</div>
                        <div class="preference-description">Get notified when you receive reviews or feedback requests</div>
                    </div>
                    <label class="preference-toggle">
                        <input type="checkbox" name="email_review" {% if preferences.email_review %}checked{% endif %}>
                        <span class="toggle-slider"></span>
                    </label>
                </div>

                <div class="preference-item">
                    <div class="preference-content">
                        <div class="preference-title">Promotional Offers</div>
                        <div class="preference-description">Receive special offers, discounts, and promotional content</div>
                    </div>
                    <label class="preference-toggle">
                        <input type="checkbox" name="email_promotional" {% if preferences.email_promotional %}checked{% endif %}>
                        <span class="toggle-slider"></span>
                    </label>
                </div>
            </div>

            <!-- Push Notifications -->
            <div class="preference-section">
                <h2 class="section-title">
                    <div class="section-icon push">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    Push Notifications
                </h2>

                <div class="preference-item">
                    <div class="preference-content">
                        <div class="preference-title">Instant Alerts</div>
                        <div class="preference-description">Get immediate notifications for urgent updates</div>
                    </div>
                    <label class="preference-toggle">
                        <input type="checkbox" name="push_instant" {% if preferences.push_instant %}checked{% endif %}>
                        <span class="toggle-slider"></span>
                    </label>
                </div>

                <div class="preference-item">
                    <div class="preference-content">
                        <div class="preference-title">Booking Reminders</div>
                        <div class="preference-description">Receive reminders about upcoming appointments</div>
                    </div>
                    <label class="preference-toggle">
                        <input type="checkbox" name="push_reminders" {% if preferences.push_reminders %}checked{% endif %}>
                        <span class="toggle-slider"></span>
                    </label>
                </div>

                <div class="preference-item">
                    <div class="preference-content">
                        <div class="preference-title">Marketing Messages</div>
                        <div class="preference-description">Receive push notifications about new features and offers</div>
                    </div>
                    <label class="preference-toggle">
                        <input type="checkbox" name="push_marketing" {% if preferences.push_marketing %}checked{% endif %}>
                        <span class="toggle-slider"></span>
                    </label>
                </div>
            </div>

            <!-- Frequency Settings -->
            <div class="preference-section">
                <h2 class="section-title">
                    <div class="section-icon frequency">
                        <i class="fas fa-clock"></i>
                    </div>
                    Frequency Settings
                </h2>

                <div class="form-group">
                    <label for="email_frequency" class="form-label">Email Digest Frequency</label>
                    <select name="email_frequency" id="email_frequency" class="form-select">
                        <option value="immediate" {% if preferences.email_frequency == 'immediate' %}selected{% endif %}>Immediate</option>
                        <option value="daily" {% if preferences.email_frequency == 'daily' %}selected{% endif %}>Daily Digest</option>
                        <option value="weekly" {% if preferences.email_frequency == 'weekly' %}selected{% endif %}>Weekly Digest</option>
                        <option value="monthly" {% if preferences.email_frequency == 'monthly' %}selected{% endif %}>Monthly Digest</option>
                        <option value="never" {% if preferences.email_frequency == 'never' %}selected{% endif %}>Never</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="notification_quiet_hours_start" class="form-label">Quiet Hours Start</label>
                    <select name="notification_quiet_hours_start" id="notification_quiet_hours_start" class="form-select">
                        {% for hour in quiet_hours_options %}
                        <option value="{{ hour.value }}" {% if preferences.notification_quiet_hours_start == hour.value %}selected{% endif %}>
                            {{ hour.label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <div class="form-group">
                    <label for="notification_quiet_hours_end" class="form-label">Quiet Hours End</label>
                    <select name="notification_quiet_hours_end" id="notification_quiet_hours_end" class="form-select">
                        {% for hour in quiet_hours_options %}
                        <option value="{{ hour.value }}" {% if preferences.notification_quiet_hours_end == hour.value %}selected{% endif %}>
                            {{ hour.label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-section">
                <div class="action-buttons">
                    <button type="submit" class="action-btn primary">
                        <i class="fas fa-save"></i>
                        Save Preferences
                    </button>
                    <a href="{% url 'notifications_app:notification_list' %}" class="action-btn secondary">
                        <i class="fas fa-arrow-left"></i>
                        Back to Notifications
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- JavaScript for enhanced interactions -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth transitions for toggle switches
    const toggleSwitches = document.querySelectorAll('.preference-toggle input');
    
    toggleSwitches.forEach(toggle => {
        toggle.addEventListener('change', function() {
            // Add a subtle feedback animation
            const slider = this.nextElementSibling;
            slider.style.transform = 'scale(1.05)';
            setTimeout(() => {
                slider.style.transform = 'scale(1)';
            }, 150);
        });
    });

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            // Add loading state to submit button
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                submitBtn.disabled = true;
                
                // Re-enable after a delay (in case of validation errors)
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 3000);
            }
        });
    }
});
</script>
{% endblock %}
