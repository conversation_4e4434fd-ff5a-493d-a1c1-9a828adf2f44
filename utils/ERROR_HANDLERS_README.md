# Enhanced Error Handlers Documentation

## Overview

The CozyWish project uses a sophisticated error handling system that provides user-friendly error pages, comprehensive logging, analytics tracking, and context-aware messaging. This system handles all HTTP errors (400, 403, 404, 500) and provides specialized handlers for CSRF failures and database errors.

## Features

### 🎯 Context-Aware Error Messages
- **User Type Detection**: Different messages for anonymous users, customers, providers, and staff
- **Device Detection**: Mobile-optimized error pages
- **AJAX Support**: JSON responses for AJAX requests
- **Localization Ready**: Easy to extend with multilingual support

### 📊 Error Analytics & Monitoring
- **Real-time Tracking**: Error counts stored in cache for immediate monitoring
- **Long-term Logging**: Comprehensive error logs with user context
- **Performance Metrics**: Track error handler response times
- **Rate Limiting**: Prevent error page spam and abuse

### 🔒 Security Features
- **Security Event Logging**: Special handling for permission denied and CSRF failures
- **User Activity Tracking**: Monitor suspicious error patterns
- **Rate Limiting**: Prevent abuse of error pages
- **Safe Error Information**: No sensitive data leaked in error messages

### 🎨 Enhanced User Experience
- **Helpful Links**: Context-aware navigation suggestions
- **Recovery Guidance**: Specific instructions based on error type
- **Professional Design**: Consistent with CozyWish brand
- **Fallback Mechanisms**: Graceful degradation when templates fail

## Error Handler Classes

### ErrorContext
Provides context-aware error handling based on user and request state.

```python
context = ErrorContext(request)
print(context.user_type)  # 'anonymous', 'customer', 'provider', 'staff'
print(context.is_ajax)    # True for AJAX requests
print(context.is_mobile)  # True for mobile devices
```

### ErrorRateLimiter
Prevents abuse by limiting error requests per user/IP.

```python
# Check if user has exceeded rate limit
if ErrorRateLimiter.is_rate_limited(request, '404', limit=10, window=60):
    return HttpResponseTooManyRequests()
```

### ErrorAnalytics
Tracks error patterns for monitoring and improvement.

```python
# Track error for analytics
ErrorAnalytics.track_error(request, '404', context)
```

## Error Handler Functions

### Core Error Handlers

#### `custom_404_view(request, exception=None)`
- **Purpose**: Handle page not found errors
- **Features**: Intelligent routing suggestions, user-specific messaging
- **Template**: `404.html`

#### `custom_500_view(request)`
- **Purpose**: Handle server errors
- **Features**: System status information, automatic error reporting
- **Template**: `500.html`

#### `custom_403_view(request, exception=None)`
- **Purpose**: Handle permission denied errors
- **Features**: Permission escalation guidance, security event logging
- **Template**: `403.html`

#### `custom_400_view(request, exception=None)`
- **Purpose**: Handle bad request errors
- **Features**: Input validation guidance, form error suggestions
- **Template**: `400.html`

### Specialized Handlers

#### `custom_csrf_failure_view(request, reason="")`
- **Purpose**: Handle CSRF token failures
- **Features**: User-friendly CSRF guidance, automatic refresh suggestions
- **Security**: Logs CSRF failures as security events

#### `handle_database_error(request, exception)`
- **Purpose**: Handle database connection errors
- **Features**: Service status messaging, retry suggestions
- **Fallback**: Uses 500.html template with database-specific context

## Configuration

### Django Settings

Add to your `settings.py`:

```python
# Error handler configuration
SUPPORT_EMAIL = '<EMAIL>'

# Error handler URLs
handler404 = 'utils.error_handlers.custom_404_view'
handler500 = 'utils.error_handlers.custom_500_view'
handler403 = 'utils.error_handlers.custom_403_view'
handler400 = 'utils.error_handlers.custom_400_view'

# CSRF failure handler
CSRF_FAILURE_VIEW = 'utils.error_handlers.custom_csrf_failure_view'
```

### URL Configuration

In your main `urls.py`:

```python
from django.conf import settings
from django.conf.urls import handler404, handler500, handler403, handler400

# Error handlers will be automatically used by Django
```

## Usage Examples

### Basic Error Handling

```python
from utils.error_handlers import custom_404_view, ErrorContext

# In your view
def my_view(request):
    try:
        # Your view logic here
        pass
    except ObjectDoesNotExist:
        return custom_404_view(request)
```

### Manual Error Analytics

```python
from utils.error_handlers import ErrorAnalytics, ErrorContext

# Track custom errors
context = ErrorContext(request)
ErrorAnalytics.track_error(request, 'custom_error', context)
```

### Get Error Statistics

```python
from utils.error_handlers import get_error_statistics

# Get current hour error statistics
stats = get_error_statistics()
print(stats)  # {'404': 5, '500': 2, '403': 1, '400': 3}
```

### AJAX Error Handling

```javascript
// Frontend JavaScript
$.ajax({
    url: '/api/endpoint/',
    type: 'POST',
    error: function(xhr, status, error) {
        if (xhr.status === 403) {
            const response = JSON.parse(xhr.responseText);
            if (response.error === 'csrf_failure') {
                // Handle CSRF failure
                location.reload();
            }
        }
    }
});
```

## Template Context Variables

All error templates receive these context variables:

```python
{
    'request_path': '/current/path/',
    'support_email': '<EMAIL>',
    'error_code': '404',
    'error_title': 'Page Not Found',
    'error_message': 'Context-aware error message',
    'user_type': 'customer',
    'is_authenticated': True,
    'is_mobile': False,
    'timestamp': datetime.now(),
    'debug': False,
    'helpful_links': [
        {'url': 'dashboard', 'text': 'Dashboard', 'icon': 'fas fa-tachometer-alt'},
        # ... more links based on user type
    ]
}
```

## Customization

### Adding New Error Types

1. **Create Handler Function**:
```python
def custom_422_view(request, exception=None):
    return _handle_error_response(
        request=request,
        error_code='422',
        exception=exception,
        template_name='422.html',
        response_class=HttpResponseUnprocessableEntity
    )
```

2. **Add to Settings**:
```python
handler422 = 'utils.error_handlers.custom_422_view'
```

3. **Create Template**: Create `templates/422.html` following the existing pattern.

### Customizing Error Messages

Modify the `ErrorContext.get_contextual_message()` method to add new user types or error messages:

```python
def get_contextual_message(self, error_type: str) -> Dict[str, str]:
    messages = {
        'your_user_type': {
            '404': 'Your custom 404 message',
            '500': 'Your custom 500 message',
            # ... more messages
        }
    }
    # ... rest of the method
```

### Adding New Analytics

Extend the `ErrorAnalytics.track_error()` method to add custom tracking:

```python
def track_error(request, error_type: str, context: ErrorContext):
    # Your custom analytics logic
    analytics_data = {
        'custom_field': 'custom_value',
        # ... existing fields
    }
    # ... rest of the method
```

## Performance Considerations

### Caching Strategy
- Error analytics are cached for 1 hour
- Rate limiting uses cache with 60-second windows
- Template rendering is optimized with fallback mechanisms

### Database Impact
- Error handlers are designed to work even during database failures
- Minimal database queries in error handling paths
- Async logging for performance

### Memory Usage
- Context objects are lightweight and short-lived
- Cache entries have appropriate TTLs
- Error tracking uses efficient data structures

## Monitoring and Alerting

### Error Statistics Dashboard

```python
from utils.error_handlers import get_error_statistics

def admin_dashboard(request):
    error_stats = get_error_statistics()
    return render(request, 'admin/dashboard.html', {
        'error_stats': error_stats
    })
```

### Log Analysis

Error logs include structured data for analysis:

```python
{
    'error_type': 'http_404',
    'user_type': 'customer',
    'is_mobile': True,
    'path': '/venue/123/',
    'timestamp': '2024-01-01T12:00:00Z',
    # ... more fields
}
```

## Security Best Practices

### Error Information Disclosure
- Never expose sensitive information in error messages
- Use generic messages for security-related errors
- Log detailed information separately from user-facing messages

### Rate Limiting
- Implement rate limiting to prevent abuse
- Use different limits for different user types
- Monitor rate limit violations

### CSRF Protection
- Specialized CSRF error handler with security logging
- Automatic refresh suggestions for expired tokens
- Security event tracking for CSRF failures

## Testing

### Unit Tests

```python
from django.test import TestCase
from utils.error_handlers import ErrorContext, ErrorRateLimiter

class ErrorHandlerTests(TestCase):
    def test_error_context_user_type(self):
        # Test user type detection
        pass
    
    def test_rate_limiting(self):
        # Test rate limiting functionality
        pass
```

### Integration Tests

```python
from django.test import Client

class ErrorHandlerIntegrationTests(TestCase):
    def test_404_response(self):
        client = Client()
        response = client.get('/nonexistent-page/')
        self.assertEqual(response.status_code, 404)
        self.assertContains(response, 'Page Not Found')
```

## Troubleshooting

### Common Issues

1. **Templates Not Found**: Ensure all error templates exist in the templates directory
2. **Import Errors**: Check that all imports are available in your Python path
3. **Cache Issues**: Clear cache if rate limiting behaves unexpectedly
4. **Permission Errors**: Ensure proper permissions for log files and cache

### Debug Mode

In development, set `DEBUG = True` to see detailed error information in templates.

### Log Analysis

Check logs for error handler performance and issues:

```bash
# Search for error handler logs
grep "error_handler" /path/to/logs/django.log

# Monitor error rates
grep "HTTP.*error tracked" /path/to/logs/django.log | wc -l
```

## Best Practices

1. **Consistent Messaging**: Use context-aware messages appropriate for each user type
2. **Security First**: Never expose sensitive information in error messages
3. **Performance**: Keep error handlers lightweight and fast
4. **Monitoring**: Regularly review error statistics and patterns
5. **User Experience**: Provide helpful recovery suggestions and navigation
6. **Documentation**: Keep error handling documentation up to date

## Contributing

When adding new error handlers or modifying existing ones:

1. Follow the existing pattern and structure
2. Add comprehensive tests
3. Update documentation
4. Consider security implications
5. Test with different user types and scenarios
6. Monitor performance impact

## Support

For questions or issues with the error handling system:

- **Email**: <EMAIL>
- **Documentation**: Check this README and inline code documentation
- **Logs**: Check Django logs for detailed error information
- **Testing**: Use the test suite to verify functionality 