/**
 * CozyWish Design System - Form Components
 * Reusable form field components based on customer login design
 */

/* CSS Custom Properties for Form Components */
:root {
    /* Brand Colors */
    --cw-brand-primary: #2F160F;
    --cw-brand-light: #4a2a1f;
    --cw-brand-accent: #fae1d7;
    --cw-accent-light: #fef7f0;
    --cw-accent-dark: #f1d4c4;

    /* Neutral Colors */
    --cw-neutral-50: #fafafa;
    --cw-neutral-100: #f5f5f5;
    --cw-neutral-200: #e5e5e5;
    --cw-neutral-600: #525252;
    --cw-neutral-700: #404040;
    --cw-neutral-800: #262626;

    /* Typography */
    --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
    --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;

    /* Shadows */
    --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* ========================================
   FORM FIELD COMPONENTS
   ======================================== */

/* Form Field Container */
.form-field-cw {
    margin-bottom: 1.5rem;
}

/* Form Label */
.form-label-cw {
    color: var(--cw-neutral-700);
    font-weight: 600;
    font-family: var(--cw-font-heading);
    margin-bottom: 0.5rem;
    display: block;
    font-size: 0.95rem;
    letter-spacing: 0.025em;
}

.form-label-cw i {
    color: var(--cw-brand-primary);
    margin-right: 0.5rem;
    width: 1rem;
    text-align: center;
}

/* Form Control */
.form-control-cw {
    border: 2px solid var(--cw-brand-accent);
    border-radius: 0.5rem;
    padding: 0.875rem 1rem;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: white;
    color: var(--cw-neutral-800);
    font-family: var(--cw-font-primary);
    width: 100%;
    line-height: 1.5;
}

.form-control-cw:focus {
    border-color: var(--cw-brand-primary);
    box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    outline: none;
}

.form-control-cw::placeholder {
    color: var(--cw-neutral-600);
    opacity: 0.7;
}

.form-control-cw.is-invalid {
    border-color: #dc2626;
    box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.1);
}

.form-control-cw.is-valid {
    border-color: #16a34a;
    box-shadow: 0 0 0 0.2rem rgba(22, 163, 74, 0.1);
}

/* Form Control Sizes */
.form-control-cw.form-control-sm {
    padding: 0.625rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
}

.form-control-cw.form-control-lg {
    padding: 1.125rem 1.25rem;
    font-size: 1.125rem;
    border-radius: 0.625rem;
}

/* Input Group */
.input-group-cw {
    position: relative;
    display: flex;
    align-items: stretch;
    width: 100%;
}

.input-group-cw .form-control-cw {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
    border-radius: 0.5rem 0 0 0.5rem;
}

.input-group-cw .form-control-cw:focus {
    z-index: 3;
}

/* Input Group Addons */
.input-group-cw .input-group-text {
    display: flex;
    align-items: center;
    padding: 0.875rem 1rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: var(--cw-neutral-700);
    text-align: center;
    white-space: nowrap;
    background-color: var(--cw-neutral-50);
    border: 2px solid var(--cw-brand-accent);
    border-left: none;
    border-radius: 0 0.5rem 0.5rem 0;
    font-family: var(--cw-font-primary);
}

.input-group-cw .input-group-text i {
    color: var(--cw-brand-primary);
}

/* Password Toggle Button */
.toggle-password-cw {
    border: 2px solid var(--cw-brand-accent) !important;
    border-left: none !important;
    background: white !important;
    color: var(--cw-brand-primary) !important;
    padding: 0.875rem 1rem !important;
    border-radius: 0 0.5rem 0.5rem 0 !important;
    transition: all 0.2s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
}

.toggle-password-cw:hover,
.toggle-password-cw:focus {
    background: var(--cw-accent-light) !important;
    border-color: var(--cw-brand-primary) !important;
    color: var(--cw-brand-primary) !important;
}

.input-group-cw .form-control-cw:focus + .toggle-password-cw {
    border-color: var(--cw-brand-primary) !important;
}

/* Error Messages */
.invalid-feedback-cw {
    display: block !important;
    width: 100%;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #dc2626;
    font-weight: 500;
    font-family: var(--cw-font-primary);
}

.invalid-feedback-cw i {
    margin-right: 0.25rem;
}

/* Success Messages */
.valid-feedback-cw {
    display: block !important;
    width: 100%;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #16a34a;
    font-weight: 500;
    font-family: var(--cw-font-primary);
}

.valid-feedback-cw i {
    margin-right: 0.25rem;
}

/* Form Text */
.form-text-cw {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: var(--cw-neutral-600);
    font-family: var(--cw-font-primary);
    line-height: 1.4;
}

/* ========================================
   CHECKBOX & RADIO COMPONENTS
   ======================================== */

/* Checkbox Container */
.form-check-cw {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

/* Checkbox Input */
.form-check-input-cw {
    border: 2px solid var(--cw-brand-accent);
    border-radius: 0.25rem;
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.75rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.form-check-input-cw:checked {
    background-color: var(--cw-brand-primary);
    border-color: var(--cw-brand-primary);
}

.form-check-input-cw:focus {
    border-color: var(--cw-brand-primary);
    box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
}

/* Checkbox Label */
.form-check-label-cw {
    color: var(--cw-neutral-700);
    font-family: var(--cw-font-primary);
    font-weight: 500;
    cursor: pointer;
    line-height: 1.4;
}

/* Radio Input */
.form-radio-input-cw {
    border: 2px solid var(--cw-brand-accent);
    border-radius: 50%;
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.75rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.form-radio-input-cw:checked {
    background-color: var(--cw-brand-primary);
    border-color: var(--cw-brand-primary);
}

.form-radio-input-cw:focus {
    border-color: var(--cw-brand-primary);
    box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
}

/* ========================================
   SELECT COMPONENTS
   ======================================== */

/* Select Control */
.form-select-cw {
    border: 2px solid var(--cw-brand-accent);
    border-radius: 0.5rem;
    padding: 0.875rem 1rem;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: white;
    color: var(--cw-neutral-800);
    font-family: var(--cw-font-primary);
    width: 100%;
    line-height: 1.5;
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%232F160F' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 16px 12px;
    padding-right: 2.5rem;
    appearance: none;
}

.form-select-cw:focus {
    border-color: var(--cw-brand-primary);
    box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    outline: none;
}

.form-select-cw.is-invalid {
    border-color: #dc2626;
    box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.1);
}

/* ========================================
   TEXTAREA COMPONENTS
   ======================================== */

/* Textarea Control */
.form-textarea-cw {
    border: 2px solid var(--cw-brand-accent);
    border-radius: 0.5rem;
    padding: 0.875rem 1rem;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: white;
    color: var(--cw-neutral-800);
    font-family: var(--cw-font-primary);
    width: 100%;
    line-height: 1.5;
    resize: vertical;
    min-height: 120px;
}

.form-textarea-cw:focus {
    border-color: var(--cw-brand-primary);
    box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    outline: none;
}

.form-textarea-cw::placeholder {
    color: var(--cw-neutral-600);
    opacity: 0.7;
}

.form-textarea-cw.is-invalid {
    border-color: #dc2626;
    box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.1);
}

/* ========================================
   FORM SECTIONS
   ======================================== */

/* Form Section */
.form-section-cw {
    margin-bottom: 2.5rem;
    padding: 2rem;
    background: white;
    border-radius: 0.75rem;
    border: 1px solid var(--cw-brand-accent);
}

.form-section-cw .form-section-title {
    font-family: var(--cw-font-heading);
    font-weight: 600;
    color: var(--cw-brand-primary);
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
}

.form-section-cw .form-section-title i {
    margin-right: 0.75rem;
    color: var(--cw-brand-primary);
}

/* Form Row */
.form-row-cw {
    display: flex;
    flex-wrap: wrap;
    margin-right: -0.75rem;
    margin-left: -0.75rem;
}

.form-row-cw > .form-field-cw {
    padding-right: 0.75rem;
    padding-left: 0.75rem;
}

/* Form Column Sizes */
.form-col-cw {
    flex: 1 0 0%;
}

.form-col-cw-6 {
    flex: 0 0 auto;
    width: 50%;
}

.form-col-cw-4 {
    flex: 0 0 auto;
    width: 33.333333%;
}

.form-col-cw-3 {
    flex: 0 0 auto;
    width: 25%;
}

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */

@media (max-width: 768px) {
    .form-section-cw {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .form-row-cw > .form-field-cw {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }

    .form-col-cw-6,
    .form-col-cw-4,
    .form-col-cw-3 {
        width: 100%;
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .form-section-cw {
        padding: 1rem;
    }

    .form-control-cw,
    .form-select-cw,
    .form-textarea-cw {
        font-size: 16px; /* Prevents zoom on iOS */
    }
}

/* ========================================
   ACCESSIBILITY
   ======================================== */

/* Focus indicators for keyboard navigation */
.form-control-cw:focus-visible,
.form-select-cw:focus-visible,
.form-textarea-cw:focus-visible,
.form-check-input-cw:focus-visible,
.form-radio-input-cw:focus-visible,
.toggle-password-cw:focus-visible {
    outline: 2px solid var(--cw-brand-primary);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .form-control-cw,
    .form-select-cw,
    .form-textarea-cw {
        border-width: 3px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .form-control-cw,
    .form-select-cw,
    .form-textarea-cw,
    .form-check-input-cw,
    .form-radio-input-cw,
    .toggle-password-cw {
        transition: none;
    }
} 