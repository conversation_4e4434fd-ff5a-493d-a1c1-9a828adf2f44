# --- Django Imports ---
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from .auth import CustomUser
from ..constants import (
    ThemePreferences, LanguagePreferences, TimezonePreferences,
    NotificationPreferences, DefaultValues
)


class UserPreferences(models.Model):
    """
    Stores user preferences and settings.
    """
    user = models.OneToOneField(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='user_preferences',
        verbose_name=_('user account')
    )
    theme_preference = models.CharField(
        _('theme preference'),
        max_length=10,
        choices=ThemePreferences.CHOICES,
        default=DefaultValues.DEFAULT_THEME
    )
    language_preference = models.CharField(
        _('language preference'),
        max_length=5,
        choices=LanguagePreferences.CHOICES,
        default=DefaultValues.DEFAULT_LANGUAGE
    )
    timezone_preference = models.CharField(
        _('timezone preference'),
        max_length=50,
        choices=TimezonePreferences.CHOICES,
        default=DefaultValues.DEFAULT_TIMEZONE
    )
    notification_preference = models.CharField(
        _('notification preference'),
        max_length=10,
        choices=NotificationPreferences.CHOICES,
        default=DefaultValues.DEFAULT_NOTIFICATION_PREFERENCE
    )
    email_notifications = models.BooleanField(
        _('email notifications'),
        default=True
    )
    marketing_emails = models.BooleanField(
        _('marketing emails'),
        default=False
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('User Preferences')
        verbose_name_plural = _('User Preferences')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user'], name='user_preferences_user_idx'),
            models.Index(fields=['created_at'], name='user_preferences_created_idx'),
        ]

    def __str__(self):
        return f"{self.user.email} - Preferences"


class EmailPreferences(models.Model):
    """
    Manages user email notification preferences for granular control over email communications.
    
    This model allows users to opt-in/out of different types of email notifications
    while maintaining compliance with email marketing regulations.
    """
    
    user = models.OneToOneField(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='email_preferences',
        verbose_name=_('user account')
    )
    
    # Core email preferences
    email_notifications_enabled = models.BooleanField(
        _('email notifications enabled'),
        default=True,
        help_text=_('Master switch for all email notifications')
    )
    
    # Transactional emails (usually required by law)
    account_notifications = models.BooleanField(
        _('account notifications'),
        default=True,
        help_text=_('Account security, password changes, login alerts')
    )
    
    booking_notifications = models.BooleanField(
        _('booking notifications'),
        default=True,
        help_text=_('Booking confirmations, cancellations, reminders')
    )
    
    payment_notifications = models.BooleanField(
        _('payment notifications'),
        default=True,
        help_text=_('Payment confirmations, receipts, refunds')
    )
    
    # Marketing and promotional emails
    marketing_emails = models.BooleanField(
        _('marketing emails'),
        default=False,
        help_text=_('Promotional offers, newsletters, product updates')
    )
    
    event_notifications = models.BooleanField(
        _('event notifications'),
        default=True,
        help_text=_('New events, event updates, early access notifications')
    )
    
    review_notifications = models.BooleanField(
        _('review notifications'),
        default=True,
        help_text=_('Review requests, review responses, rating updates')
    )
    
    # Frequency preferences
    DIGEST_FREQUENCY_CHOICES = [
        ('immediate', _('Immediate')),
        ('daily', _('Daily Digest')),
        ('weekly', _('Weekly Digest')),
        ('monthly', _('Monthly Digest')),
        ('disabled', _('Disabled')),
    ]
    
    digest_frequency = models.CharField(
        _('digest frequency'),
        max_length=20,
        choices=DIGEST_FREQUENCY_CHOICES,
        default='immediate',
        help_text=_('How often to receive grouped notifications')
    )
    
    # Email format preferences
    EMAIL_FORMAT_CHOICES = [
        ('html', _('HTML (Rich Text)')),
        ('plain', _('Plain Text')),
    ]
    
    email_format = models.CharField(
        _('email format'),
        max_length=10,
        choices=EMAIL_FORMAT_CHOICES,
        default='html',
        help_text=_('Preferred email format')
    )
    
    # Unsubscribe tracking
    unsubscribe_all = models.BooleanField(
        _('unsubscribe all'),
        default=False,
        help_text=_('User has unsubscribed from all non-essential emails')
    )
    
    unsubscribe_date = models.DateTimeField(
        _('unsubscribe date'),
        null=True,
        blank=True,
        help_text=_('When the user unsubscribed from all emails')
    )
    
    # Timestamps
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Email Preferences')
        verbose_name_plural = _('Email Preferences')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user'], name='email_preferences_user_idx'),
            models.Index(fields=['created_at'], name='email_preferences_created_idx'),
            models.Index(fields=['digest_frequency'], name='email_preferences_digest_idx'),
        ]

    def __str__(self):
        return f"{self.user.email} - Email Preferences"

    def can_send_email(self, email_type: str) -> bool:
        """
        Check if a specific type of email can be sent to this user.
        
        Args:
            email_type (str): Type of email to check
            
        Returns:
            bool: True if email can be sent, False otherwise
        """
        # Check if email notifications are enabled
        if not self.email_notifications_enabled:
            return False
        
        # Check if user has unsubscribed from all emails
        if self.unsubscribe_all:
            return False
        
        # Check specific email type preferences
        email_type_mapping = {
            'account': self.account_notifications,
            'booking': self.booking_notifications,
            'payment': self.payment_notifications,
            'marketing': self.marketing_emails,
            'event': self.event_notifications,
            'review': self.review_notifications,
        }
        
        return email_type_mapping.get(email_type, False)

    def disable_all_emails(self):
        """Disable all email notifications for this user"""
        self.unsubscribe_all = True
        self.unsubscribe_date = timezone.now()
        self.save()

    def enable_default_emails(self):
        """
        Enable default email settings for existing users.
        
        This method sets reasonable defaults for users who haven't configured
        their email preferences yet.
        """
        self.account_notifications = True
        self.booking_notifications = True
        self.payment_notifications = True
        self.marketing_emails = False
        self.event_notifications = True
        self.review_notifications = True
        self.digest_frequency = 'immediate'
        self.email_format = 'html'
        self.unsubscribe_all = False
        self.save()


class ProfilePrivacySettings(models.Model):
    """
    Controls user profile data visibility and privacy preferences.
    
    This model allows users to control what information is visible to other users
    and provides granular privacy controls for profile data.
    """
    
    # Profile visibility levels
    VISIBILITY_CHOICES = [
        ('public', _('Public')),
        ('private', _('Private')),
        ('friends_only', _('Friends Only')),
    ]
    
    user = models.OneToOneField(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='privacy_settings',
        verbose_name=_('user account')
    )
    
    # General profile visibility
    profile_visibility = models.CharField(
        _('profile visibility'),
        max_length=20,
        choices=VISIBILITY_CHOICES,
        default='public',
        help_text=_('Who can see your profile')
    )
    
    # Individual field visibility settings
    show_email = models.BooleanField(
        _('show email'),
        default=False,
        help_text=_('Show email address on profile')
    )
    
    show_phone = models.BooleanField(
        _('show phone'),
        default=False,
        help_text=_('Show phone number on profile')
    )
    
    show_address = models.BooleanField(
        _('show address'),
        default=False,
        help_text=_('Show address on profile')
    )
    
    show_birth_date = models.BooleanField(
        _('show birth date'),
        default=False,
        help_text=_('Show birth date on profile')
    )
    
    show_gender = models.BooleanField(
        _('show gender'),
        default=True,
        help_text=_('Show gender on profile')
    )
    
    show_profile_picture = models.BooleanField(
        _('show profile picture'),
        default=True,
        help_text=_('Show profile picture')
    )
    
    show_last_seen = models.BooleanField(
        _('show last seen'),
        default=True,
        help_text=_('Show last login/activity time')
    )
    
    # Activity and interaction privacy
    show_booking_history = models.BooleanField(
        _('show booking history'),
        default=False,
        help_text=_('Show booking history to other users')
    )
    
    show_reviews = models.BooleanField(
        _('show reviews'),
        default=True,
        help_text=_('Show reviews written by you')
    )
    
    show_favorites = models.BooleanField(
        _('show favorites'),
        default=False,
        help_text=_('Show your favorite venues/services')
    )
    
    # Search and discovery settings
    discoverable_in_search = models.BooleanField(
        _('discoverable in search'),
        default=True,
        help_text=_('Allow others to find you in search')
    )
    
    allow_friend_requests = models.BooleanField(
        _('allow friend requests'),
        default=True,
        help_text=_('Allow other users to send friend requests')
    )
    
    allow_messages = models.BooleanField(
        _('allow messages'),
        default=True,
        help_text=_('Allow other users to send you messages')
    )
    
    # Data collection and tracking
    allow_analytics = models.BooleanField(
        _('allow analytics'),
        default=True,
        help_text=_('Allow collection of usage analytics')
    )
    
    allow_personalized_ads = models.BooleanField(
        _('allow personalized ads'),
        default=False,
        help_text=_('Allow personalized advertising')
    )
    
    # GDPR compliance
    data_processing_consent = models.BooleanField(
        _('data processing consent'),
        default=True,
        help_text=_('Consent to process personal data')
    )
    
    marketing_consent = models.BooleanField(
        _('marketing consent'),
        default=False,
        help_text=_('Consent to receive marketing communications')
    )
    
    third_party_sharing = models.BooleanField(
        _('third party sharing'),
        default=False,
        help_text=_('Allow sharing data with trusted third parties')
    )
    
    # Timestamps
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    
    class Meta:
        verbose_name = _('Privacy Settings')
        verbose_name_plural = _('Privacy Settings')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user'], name='privacy_user_idx'),
            models.Index(fields=['profile_visibility'], name='privacy_visibility_idx'),
            models.Index(fields=['discoverable_in_search'], name='privacy_discoverable_idx'),
        ]
    
    def __str__(self):
        return f"Privacy settings for {self.user.email}"
    
    def get_visibility_display_name(self):
        """Get human-readable display name for visibility setting"""
        return dict(self.VISIBILITY_CHOICES).get(self.profile_visibility, self.profile_visibility)
    
    def is_field_visible(self, field_name: str, viewing_user=None) -> bool:
        """
        Check if a specific field is visible to the viewing user.
        
        Args:
            field_name: Name of the field to check
            viewing_user: User trying to view the field (None for anonymous)
            
        Returns:
            bool: True if field is visible, False otherwise
        """
        # Owner can always see their own fields
        if viewing_user and viewing_user == self.user:
            return True
        
        # Check profile visibility first
        if self.profile_visibility == 'private':
            return False
        
        # Check specific field visibility
        field_visibility_map = {
            'email': self.show_email,
            'phone': self.show_phone,
            'address': self.show_address,
            'birth_date': self.show_birth_date,
            'gender': self.show_gender,
            'profile_picture': self.show_profile_picture,
            'last_seen': self.show_last_seen,
            'booking_history': self.show_booking_history,
            'reviews': self.show_reviews,
            'favorites': self.show_favorites,
        }
        
        return field_visibility_map.get(field_name, False)
    
    def can_be_contacted(self, contacting_user=None) -> bool:
        """
        Check if user can be contacted by another user.
        
        Args:
            contacting_user: User trying to contact
            
        Returns:
            bool: True if user can be contacted, False otherwise
        """
        if not self.allow_messages:
            return False
        
        if self.profile_visibility == 'private':
            return False
        
        # Add friend-specific logic here if needed
        return True
    
    @classmethod
    def get_default_settings(cls) -> dict:
        """Get default privacy settings for new users"""
        return {
            'profile_visibility': 'public',
            'show_email': False,
            'show_phone': False,
            'show_address': False,
            'show_birth_date': False,
            'show_gender': True,
            'show_profile_picture': True,
            'show_last_seen': True,
            'show_booking_history': False,
            'show_reviews': True,
            'show_favorites': False,
            'discoverable_in_search': True,
            'allow_friend_requests': True,
            'allow_messages': True,
            'allow_analytics': True,
            'allow_personalized_ads': False,
            'data_processing_consent': True,
            'marketing_consent': False,
            'third_party_sharing': False,
        } 