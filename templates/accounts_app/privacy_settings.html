{% extends 'accounts_app/base_account.html' %}
{% load crispy_forms_tags %}

{% block page_title %}Privacy Settings{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12 col-lg-8 mx-auto">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>Privacy Settings
                    </h4>
                    <p class="mb-0 small">Control who can see your profile information and how your data is used</p>
                </div>
                
                <div class="card-body">
                    <form method="post" id="privacy-settings-form">
                        {% csrf_token %}
                        
                        <!-- Profile Visibility Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-eye me-2"></i>Profile Visibility
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        {{ form.profile_visibility|as_crispy_field }}
                                    </div>
                                    <div class="col-md-6">
                                        <div class="alert alert-info">
                                            <small>
                                                <strong>Public:</strong> Anyone can view your profile<br>
                                                <strong>Private:</strong> Only you can view your profile<br>
                                                <strong>Friends Only:</strong> Only approved friends can view
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Field Visibility Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-user-circle me-2"></i>Profile Information
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        {{ form.show_email|as_crispy_field }}
                                        {{ form.show_phone|as_crispy_field }}
                                        {{ form.show_address|as_crispy_field }}
                                        {{ form.show_birth_date|as_crispy_field }}
                                    </div>
                                    <div class="col-md-6">
                                        {{ form.show_gender|as_crispy_field }}
                                        {{ form.show_profile_picture|as_crispy_field }}
                                        {{ form.show_last_seen|as_crispy_field }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Activity Privacy Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-history me-2"></i>Activity & Interactions
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        {{ form.show_booking_history|as_crispy_field }}
                                        {{ form.show_reviews|as_crispy_field }}
                                        {{ form.show_favorites|as_crispy_field }}
                                    </div>
                                    <div class="col-md-6">
                                        {{ form.allow_friend_requests|as_crispy_field }}
                                        {{ form.allow_messages|as_crispy_field }}
                                        {{ form.discoverable_in_search|as_crispy_field }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Data Usage Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-chart-line me-2"></i>Data Usage & Analytics
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        {{ form.allow_analytics|as_crispy_field }}
                                        {{ form.allow_personalized_ads|as_crispy_field }}
                                    </div>
                                    <div class="col-md-6">
                                        <div class="alert alert-info">
                                            <small>
                                                <strong>Analytics:</strong> Help us improve our services<br>
                                                <strong>Personalized Ads:</strong> Show ads relevant to your interests
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- GDPR Compliance Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-balance-scale me-2"></i>Data Processing & Consent
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        {{ form.data_processing_consent|as_crispy_field }}
                                        {{ form.marketing_consent|as_crispy_field }}
                                        {{ form.third_party_sharing|as_crispy_field }}
                                    </div>
                                    <div class="col-md-6">
                                        <div class="alert alert-warning">
                                            <small>
                                                <strong>Required:</strong> Data processing consent is required for basic functionality<br>
                                                <strong>Optional:</strong> Marketing and third-party sharing consents are optional
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-save me-2"></i>Save Settings
                                        </button>
                                        <a href="{% url 'accounts_app:privacy_settings' %}" class="btn btn-secondary ms-2">
                                            <i class="fas fa-times me-2"></i>Cancel
                                        </a>
                                    </div>
                                    <div>
                                        <a href="{% url 'accounts_app:data_export' %}" class="btn btn-info">
                                            <i class="fas fa-download me-2"></i>Export My Data
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Quick Privacy Actions -->
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-lightning-bolt me-2"></i>Quick Privacy Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <button type="button" class="btn btn-outline-success w-100 mb-2" 
                                    onclick="setPrivacyLevel('public')">
                                <i class="fas fa-globe me-2"></i>Make Profile Public
                            </button>
                        </div>
                        <div class="col-md-4">
                            <button type="button" class="btn btn-outline-warning w-100 mb-2" 
                                    onclick="setPrivacyLevel('friends_only')">
                                <i class="fas fa-users me-2"></i>Friends Only
                            </button>
                        </div>
                        <div class="col-md-4">
                            <button type="button" class="btn btn-outline-danger w-100 mb-2" 
                                    onclick="setPrivacyLevel('private')">
                                <i class="fas fa-lock me-2"></i>Make Profile Private
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function setPrivacyLevel(level) {
    const form = document.getElementById('privacy-settings-form');
    const formData = new FormData(form);
    formData.append('privacy_level', level);
    
    fetch('{% url "accounts_app:privacy_quick_toggle" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': formData.get('csrfmiddlewaretoken'),
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating privacy settings: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating privacy settings');
    });
}
</script>
{% endblock %} 