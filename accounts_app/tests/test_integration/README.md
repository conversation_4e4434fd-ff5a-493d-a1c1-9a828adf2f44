# Authentication Workflow Integration Tests

This directory contains comprehensive integration tests for the authentication system in the CozyWish project. The tests are organized into multiple directories to maintain clean separation of concerns and avoid having too much code in a single file.

## Test Structure

### Directory Organization

```
test_integration/
├── __init__.py                 # Base test utilities and common mixins
├── fixtures/                   # Reusable test data and fixtures
│   ├── __init__.py
│   └── test_data.py           # Test data factories
├── auth_workflows/             # Basic authentication flows
│   ├── __init__.py
│   ├── test_customer_auth.py  # Customer authentication tests
│   └── test_provider_auth.py  # Service provider authentication tests
├── security_features/          # Security-related tests
│   ├── __init__.py
│   └── test_account_lockout.py # Account lockout and brute force protection
├── email_verification/         # Email verification workflows
│   ├── __init__.py
│   └── test_verification_flow.py # Email verification tests
├── password_management/        # Password-related workflows
│   ├── __init__.py
│   └── test_password_workflows.py # Password reset, change, history tests
├── user_roles/                 # Role-specific tests
│   ├── __init__.py
│   └── test_role_permissions.py # Role-based access control tests
├── cross_app/                  # Cross-app integration
│   ├── __init__.py
│   └── test_system_integration.py # System-wide integration tests
└── README.md                   # This file
```

### Test Categories

#### 1. Authentication Workflows (`auth_workflows/`)
- **Customer Authentication**: Registration, login, logout, session management
- **Service Provider Authentication**: Registration with email verification, login, logout
- **Authentication Services Integration**: Tests for authentication service layer

#### 2. Security Features (`security_features/`)
- **Account Lockout**: Brute force protection, automatic lockout after failed attempts
- **IP-based Lockout**: IP blocking for suspicious activity
- **Failed Login Tracking**: Comprehensive logging of authentication attempts
- **Security Service Integration**: Integration with security middleware and services

#### 3. Email Verification (`email_verification/`)
- **Token Generation and Validation**: Email verification token handling
- **Verification Workflows**: Complete email verification journeys
- **Security Features**: Token expiration, tampering protection, rate limiting
- **Service Integration**: Integration with email verification services

#### 4. Password Management (`password_management/`)
- **Password Reset**: Forgot password workflow for all user types
- **Password Change**: Authenticated password change
- **Password History**: Password reuse prevention
- **Security Features**: Password strength validation, security logging

#### 5. User Roles (`user_roles/`)
- **Role-based Authentication**: Different login restrictions for each role
- **Access Control**: Role-based page access restrictions
- **Registration**: Role-specific registration workflows
- **Cross-role Security**: Prevention of role switching and session isolation

#### 6. Cross-app Integration (`cross_app/`)
- **System Integration**: Authentication across different Django apps
- **Session Consistency**: Session state management across apps
- **Error Handling**: Graceful handling of authentication errors
- **Security**: Cross-app security features

## Running the Tests

### Prerequisites

1. **Django Environment**: Ensure you have Django and all project dependencies installed
2. **Database**: Make sure you have a test database configured
3. **Test Dependencies**: Install testing dependencies (pytest, pytest-django, etc.)

### Running All Integration Tests

```bash
# Run all integration tests
python manage.py test accounts_app.tests.test_integration

# Run with pytest
pytest accounts_app/tests/test_integration/

# Run with coverage
pytest accounts_app/tests/test_integration/ --cov=accounts_app --cov-report=html
```

### Running Specific Test Categories

```bash
# Run only authentication workflow tests
python manage.py test accounts_app.tests.test_integration.auth_workflows

# Run only security feature tests
python manage.py test accounts_app.tests.test_integration.security_features

# Run only email verification tests
python manage.py test accounts_app.tests.test_integration.email_verification

# Run only password management tests
python manage.py test accounts_app.tests.test_integration.password_management

# Run only user role tests
python manage.py test accounts_app.tests.test_integration.user_roles

# Run only cross-app integration tests
python manage.py test accounts_app.tests.test_integration.cross_app
```

### Running Specific Test Files

```bash
# Run customer authentication tests
python manage.py test accounts_app.tests.test_integration.auth_workflows.test_customer_auth

# Run provider authentication tests
python manage.py test accounts_app.tests.test_integration.auth_workflows.test_provider_auth

# Run account lockout tests
python manage.py test accounts_app.tests.test_integration.security_features.test_account_lockout
```

### Running with Pytest

```bash
# Run all integration tests with pytest
pytest accounts_app/tests/test_integration/

# Run specific test file
pytest accounts_app/tests/test_integration/auth_workflows/test_customer_auth.py

# Run specific test class
pytest accounts_app/tests/test_integration/auth_workflows/test_customer_auth.py::CustomerRegistrationWorkflowTest

# Run specific test method
pytest accounts_app/tests/test_integration/auth_workflows/test_customer_auth.py::CustomerRegistrationWorkflowTest::test_complete_customer_registration_flow
```

## Test Data and Fixtures

### Test Data Factories

The `fixtures/test_data.py` file provides reusable test data factories:

- **CustomerTestData**: Creates customer users and test data
- **ServiceProviderTestData**: Creates service provider users and test data
- **SecurityTestData**: Creates security-related test data
- **CommonTestData**: Creates common test data used across tests

### Base Test Classes

The `__init__.py` file provides base test utilities:

- **BaseIntegrationTest**: Base class with common setup and utilities
- Common helper methods for user creation, login, and assertions

## Test Patterns and Best Practices

### 1. Complete User Journeys
Tests focus on complete user journeys rather than individual components:
- Registration → Email Verification → Login → Access Protected Resources
- Password Reset → Token Validation → Password Change → Login with New Password

### 2. Real-world Scenarios
Tests simulate real-world scenarios:
- Multiple failed login attempts leading to account lockout
- Concurrent user sessions
- Cross-browser authentication
- Security attack simulation

### 3. Both unittest and pytest Styles
Tests are written in both unittest and pytest styles to demonstrate different approaches:
- **unittest**: Traditional Django test classes
- **pytest**: Modern pytest-style tests with fixtures

### 4. Comprehensive Coverage
Tests cover:
- Happy path scenarios
- Error conditions
- Edge cases
- Security scenarios
- Cross-app integration

## Common Test Scenarios

### Authentication Flow Testing
```python
def test_complete_authentication_flow(self):
    # 1. User registration
    # 2. Email verification (for providers)
    # 3. Login
    # 4. Access protected resources
    # 5. Logout
    # 6. Verify access is revoked
```

### Security Testing
```python
def test_brute_force_protection(self):
    # 1. Multiple failed login attempts
    # 2. Account lockout triggered
    # 3. Login blocked even with correct credentials
    # 4. Automatic unlock after timeout
    # 5. Successful login after unlock
```

### Role-based Access Testing
```python
def test_role_based_access_control(self):
    # 1. Login as customer
    # 2. Verify customer resource access
    # 3. Verify provider resource denial
    # 4. Switch to provider
    # 5. Verify provider resource access
    # 6. Verify customer resource denial
```

## Debugging and Troubleshooting

### Common Issues

1. **Test Database Issues**: Make sure your test database is properly configured
2. **Email Backend**: Tests use mocked email sending to avoid actual email sending
3. **Session Issues**: Tests use Django's test client which handles sessions automatically
4. **Timezone Issues**: Tests use timezone-aware datetime objects

### Debug Tips

```bash
# Run tests with verbose output
python manage.py test accounts_app.tests.test_integration -v 2

# Run specific test with debug output
python manage.py test accounts_app.tests.test_integration.auth_workflows.test_customer_auth.CustomerRegistrationWorkflowTest.test_complete_customer_registration_flow -v 2

# Use pytest for better error reporting
pytest accounts_app/tests/test_integration/ -v -s
```

### Test Output Analysis

Tests provide detailed output including:
- Step-by-step workflow verification
- Database state verification
- Session state verification
- Security feature verification
- Error message verification

## Integration with CI/CD

These tests are designed to be run in CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
- name: Run Authentication Integration Tests
  run: |
    python manage.py test accounts_app.tests.test_integration --keepdb
    pytest accounts_app/tests/test_integration/ --cov=accounts_app --cov-report=xml
```

## Contributing

When adding new authentication features:

1. **Add corresponding integration tests** in the appropriate directory
2. **Follow the existing test patterns** and naming conventions
3. **Test both happy path and error conditions**
4. **Include security testing** for sensitive operations
5. **Update this README** if adding new test categories

## Security Considerations

These tests verify important security features:
- Account lockout and brute force protection
- Session security and isolation
- Role-based access control
- Email verification security
- Password security features
- Cross-app security boundaries

Regular execution of these tests helps ensure the authentication system remains secure and functional across updates and changes. 