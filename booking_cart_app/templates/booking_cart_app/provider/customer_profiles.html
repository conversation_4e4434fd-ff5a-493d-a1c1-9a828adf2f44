{% extends "base.html" %}
{% load static %}
{% load humanize %}

{% block title %}Customer Profiles{% endblock %}

{% block extra_css %}
<link href="{% static 'css/booking_cart_app/customer_profiles.css' %}" rel="stylesheet">
{% endblock %}
        border-color: #4f46e5;
        color: #4f46e5;
        text-decoration: none;
    }
    
    .action-btn.primary {
        background: #4f46e5;
        border-color: #4f46e5;
        color: white;
    }
    
    .action-btn.primary:hover {
        background: #4338ca;
        border-color: #4338ca;
        color: white;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        background: white;
        border-radius: 12px;
        margin-top: 20px;
    }
    
    .empty-icon {
        font-size: 3rem;
        color: #d1d5db;
        margin-bottom: 16px;
    }
    
    .pagination-wrapper {
        margin-top: 30px;
        display: flex;
        justify-content: center;
    }
    
    .loyalty-progress {
        background: #e5e7eb;
        border-radius: 10px;
        height: 6px;
        overflow: hidden;
        margin-bottom: 8px;
    }
    
    .loyalty-progress-bar {
        height: 100%;
        border-radius: 10px;
        transition: width 0.3s ease;
    }
    
    .loyalty-progress-bar.gold {
        background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 100%);
    }
    
    .loyalty-progress-bar.silver {
        background: linear-gradient(90deg, #d1d5db 0%, #9ca3af 100%);
    }
    
    .loyalty-progress-bar.bronze {
        background: linear-gradient(90deg, #92400e 0%, #78350f 100%);
    }
    
    .loyalty-progress-bar.new {
        background: linear-gradient(90deg, #10b981 0%, #059669 100%);
    }
    
    @media (max-width: 768px) {
        .profiles-grid {
            grid-template-columns: 1fr;
        }
        
        .customer-card {
            margin: 0 10px;
        }
        
        .customer-header {
            flex-direction: column;
            text-align: center;
        }
        
        .customer-actions {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="customer-profiles-container">
    <div class="container-fluid">
        <!-- Header -->
        <div class="profiles-header">
            <h2 class="mb-3">
                <i class="fas fa-users"></i> Customer Profiles
            </h2>
            <p class="text-muted mb-4">Manage relationships with your customers and track their preferences.</p>
            
            <!-- Search Bar -->
            <form method="GET" class="search-bar">
                <div class="input-group">
                    <input type="text" 
                           class="form-control search-input" 
                           name="search" 
                           value="{{ search }}" 
                           placeholder="Search customers by name or email...">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
            
            <!-- Stats Summary -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-value">{{ total_customers }}</div>
                        <div class="stat-label">Total Customers</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-value">
                            {{ customer_profiles|length|add:0|floatformat:0 }}
                        </div>
                        <div class="stat-label">Active This Month</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-value">
                            {% for profile in customer_profiles %}
                                {% if profile.loyalty_score >= 7 %}{{ forloop.counter0|add:1 }}{% endif %}
                            {% empty %}0{% endfor %}
                        </div>
                        <div class="stat-label">VIP Customers</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-value">
                            {% for profile in customer_profiles %}
                                {% if profile.last_booking_date %}
                                    {% now "Y-m-d" as today %}
                                    {% if profile.last_booking_date|date:"Y-m-d" >= today|add_days:-30 %}
                                        {{ forloop.counter0|add:1 }}
                                    {% endif %}
                                {% endif %}
                            {% empty %}0{% endfor %}
                        </div>
                        <div class="stat-label">Recent Visitors</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Customer Profiles Grid -->
        {% if customer_profiles %}
        <div class="profiles-grid">
            {% for profile in customer_profiles %}
            <div class="customer-card">
                <!-- Loyalty Badge -->
                <div class="loyalty-badge {% if profile.loyalty_score >= 8 %}gold{% elif profile.loyalty_score >= 6 %}silver{% elif profile.loyalty_score >= 3 %}bronze{% else %}new{% endif %}">
                    {% if profile.loyalty_score >= 8 %}VIP{% elif profile.loyalty_score >= 6 %}Loyal{% elif profile.loyalty_score >= 3 %}Regular{% else %}New{% endif %}
                </div>
                
                <!-- Customer Header -->
                <div class="customer-header">
                    <div class="customer-avatar">
                        {{ profile.customer.first_name|first|default:profile.customer.email|first|upper }}
                    </div>
                    <div class="customer-info">
                        <h5>
                            {% if profile.customer.first_name or profile.customer.last_name %}
                                {{ profile.customer.get_full_name }}
                            {% else %}
                                {{ profile.customer.email|truncatechars:20 }}
                            {% endif %}
                        </h5>
                        <p class="customer-email">{{ profile.customer.email }}</p>
                    </div>
                </div>
                
                <!-- Loyalty Progress -->
                <div class="loyalty-progress">
                    <div class="loyalty-progress-bar {% if profile.loyalty_score >= 8 %}gold{% elif profile.loyalty_score >= 6 %}silver{% elif profile.loyalty_score >= 3 %}bronze{% else %}new{% endif %}" 
                         style="width: {{ profile.loyalty_score|mul:10 }}%"></div>
                </div>
                <div class="text-center mb-3">
                    <small class="text-muted">Loyalty Score: {{ profile.loyalty_score }}/10</small>
                </div>
                
                <!-- Customer Stats -->
                <div class="customer-stats">
                    <div class="stat-item">
                        <div class="stat-value">{{ profile.total_bookings }}</div>
                        <div class="stat-label">Bookings</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${{ profile.total_spent|floatformat:0 }}</div>
                        <div class="stat-label">Total Spent</div>
                    </div>
                </div>
                
                <!-- Customer Preferences -->
                <div class="customer-preferences">
                    <div class="preferences-title">Favorite Service</div>
                    <div class="favorite-service">
                        {{ profile.top_service|default:"None yet" }}
                    </div>
                </div>
                
                <!-- Last Visit -->
                <div class="last-visit">
                    <i class="fas fa-clock"></i> 
                    Last visit: 
                    {% if profile.last_booking_date %}
                        {{ profile.last_booking_date|naturalday|title }}
                    {% else %}
                        Never
                    {% endif %}
                </div>
                
                <!-- Average Booking Value -->
                {% if profile.avg_booking_value > 0 %}
                <div class="last-visit">
                    <i class="fas fa-dollar-sign"></i> 
                    Avg. booking: ${{ profile.avg_booking_value|floatformat:0 }}
                </div>
                {% endif %}
                
                <!-- Actions -->
                <div class="customer-actions">
                    <a href="#" class="action-btn primary" onclick="viewCustomerHistory({{ profile.customer.id }})">
                        <i class="fas fa-history"></i> History
                    </a>
                    <a href="mailto:{{ profile.customer.email }}" class="action-btn">
                        <i class="fas fa-envelope"></i> Email
                    </a>
                    <a href="#" class="action-btn" onclick="createBookingForCustomer({{ profile.customer.id }})">
                        <i class="fas fa-plus"></i> Book
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if customer_profiles.has_other_pages %}
        <div class="pagination-wrapper">
            <nav aria-label="Customer profiles pagination">
                <ul class="pagination">
                    {% if customer_profiles.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ customer_profiles.previous_page_number }}{% if search %}&search={{ search }}{% endif %}">Previous</a>
                        </li>
                    {% endif %}
                    
                    {% for num in customer_profiles.paginator.page_range %}
                        {% if customer_profiles.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% else %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if customer_profiles.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ customer_profiles.next_page_number }}{% if search %}&search={{ search }}{% endif %}">Next</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <!-- Empty State -->
        <div class="empty-state">
            {% if search %}
                <i class="fas fa-search empty-icon"></i>
                <h5>No customers found</h5>
                <p class="text-muted">No customers match your search criteria. Try a different search term.</p>
                <a href="{% url 'booking_cart_app:provider_customer_profiles' %}" class="btn btn-primary">
                    <i class="fas fa-users"></i> View All Customers
                </a>
            {% else %}
                <i class="fas fa-users empty-icon"></i>
                <h5>No customers yet</h5>
                <p class="text-muted">You haven't served any customers yet. Once you start accepting bookings, customer profiles will appear here.</p>
                <a href="{% url 'booking_cart_app:provider_booking_dashboard' %}" class="btn btn-primary">
                    <i class="fas fa-calendar"></i> Check Bookings
                </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Customer History Modal -->
<div class="modal fade" id="customerHistoryModal" tabindex="-1" aria-labelledby="customerHistoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customerHistoryModalLabel">Customer Booking History</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="customerHistoryContent">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <p class="mt-2">Loading customer history...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function viewCustomerHistory(customerId) {
    const modal = new bootstrap.Modal(document.getElementById('customerHistoryModal'));
    const content = document.getElementById('customerHistoryContent');
    
    // Show loading state
    content.innerHTML = `
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p class="mt-2">Loading customer history...</p>
        </div>
    `;
    
    modal.show();
    
    // Fetch customer history
    fetch(`/api/customer/${customerId}/history/`, {
        headers: {
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayCustomerHistory(data.customer, data.bookings);
        } else {
            content.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    Error loading customer history: ${data.error}
                </div>
            `;
        }
    })
    .catch(error => {
        content.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                Error loading customer history. Please try again.
            </div>
        `;
    });
}

function displayCustomerHistory(customer, bookings) {
    const content = document.getElementById('customerHistoryContent');
    
    let historyHtml = `
        <div class="customer-summary mb-4">
            <h6>${customer.name}</h6>
            <p class="text-muted">${customer.email}</p>
        </div>
        
        <div class="booking-history">
            <h6 class="mb-3">Recent Bookings</h6>
    `;
    
    if (bookings.length > 0) {
        bookings.forEach(booking => {
            historyHtml += `
                <div class="booking-item border-bottom py-3">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">${booking.service_title}</h6>
                            <p class="mb-1 text-muted">${booking.scheduled_date} at ${booking.scheduled_time}</p>
                            <span class="badge bg-${getStatusColor(booking.status)}">${booking.status}</span>
                        </div>
                        <div class="text-end">
                            <p class="mb-1 fw-bold">$${booking.total_price}</p>
                            <small class="text-muted">${booking.booking_date}</small>
                        </div>
                    </div>
                </div>
            `;
        });
    } else {
        historyHtml += '<p class="text-muted">No booking history available.</p>';
    }
    
    historyHtml += '</div>';
    content.innerHTML = historyHtml;
}

function getStatusColor(status) {
    const colors = {
        'pending': 'warning',
        'confirmed': 'success',
        'completed': 'primary',
        'cancelled': 'danger',
        'declined': 'secondary'
    };
    return colors[status] || 'secondary';
}

function createBookingForCustomer(customerId) {
    // Redirect to booking creation with pre-selected customer
    window.location.href = `/admin/bookings/create/?customer=${customerId}`;
}

function getCsrfToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '{{ csrf_token }}';
}

// Add CSRF token to page if not present
if (!document.querySelector('[name=csrfmiddlewaretoken]')) {
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrfmiddlewaretoken';
    csrfInput.value = '{{ csrf_token }}';
    document.body.appendChild(csrfInput);
}

// Auto-focus search on load
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('.search-input');
    if (searchInput && !searchInput.value) {
        searchInput.focus();
    }
});
</script>
{% endblock %} 