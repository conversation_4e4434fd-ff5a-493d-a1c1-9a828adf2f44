{% extends 'base.html' %}

{% block title %}Password Reset Email Sent - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
{% endblock %}

{% block content %}
<section class="password-reset-done-section">
    <div class="password-reset-done-container">
        <div class="password-reset-done-card">
            <div class="password-reset-done-header">
                <div class="content">
                    <div class="password-reset-done-icon">
                        <i class="fas fa-envelope-open-text"></i>
                    </div>
                    <h1 class="password-reset-done-title">Reset Link Sent!</h1>
                    <p class="password-reset-done-subtitle">We've emailed you instructions for resetting your password</p>
                </div>
            </div>

            <div class="password-reset-done-body">
                <!-- Main message -->
                <div class="text-center mb-4">
                  {% if reset_email %}
                  <div class="alert-cw alert-cw-success mb-3" role="alert">
                    <i class="fas fa-envelope me-2"></i>
                    <strong>Password reset link sent to:</strong><br>
                    <span class="reset-email">{{ reset_email }}</span>
                  </div>
                  <p class="mb-3 reset-instruction">
                    If this email address is associated with a CozyWish account, you should receive password reset instructions shortly.
                  </p>
                  {% else %}
                  <p class="mb-3 reset-instruction">
                    If an account exists with the email address you provided, you should receive password reset instructions shortly.
                  </p>
                  {% endif %}
                  <p class="mb-0 reset-instruction">
                    Please check your email inbox and spam folder.
                  </p>
                </div>

                <!-- Instructions -->
                <div class="alert-cw alert-cw-info mb-4" role="alert">
                  <div class="d-flex align-items-start">
                    <i class="fas fa-info-circle me-2 mt-1"></i>
                    <div>
                      <strong>Next Steps:</strong>
                      <ol class="instructions-list mt-2">
                        <li>Check your email inbox and spam folder</li>
                        <li>Click the reset link in the email</li>
                        <li>Create a new secure password</li>
                        <li>Sign in with your new password</li>
                      </ol>
                    </div>
                  </div>
                </div>

                <!-- Security note -->
                <div class="alert-cw alert-cw-warning mb-4" role="alert">
                  <i class="fas fa-clock me-2"></i>
                  <strong>Security Notice:</strong> The reset link will expire in 24 hours for your protection.
                </div>

                <!-- Action buttons -->
                <div class="d-flex flex-column gap-3 mb-4">
                  <a href="{% url 'accounts_app:customer_login' %}" class="btn-cw-primary">
                    <i class="fas fa-sign-in-alt"></i>Back to Login
                  </a>
                                      <a href="{% url 'home_app:home' %}" class="btn-cw-secondary">
                    <i class="fas fa-home"></i>Return to Homepage
                  </a>
                </div>

                <!-- Support contact -->
                <div class="text-center">
                  <p class="support-contact">
                    Didn't receive the email?
                    <a href="{% url 'accounts_app:customer_password_reset' %}">Try again</a>
                    or contact us at
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                  </p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
