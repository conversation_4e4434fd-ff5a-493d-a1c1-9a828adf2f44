"""
User role permission integration tests.

These tests cover role-based authentication and access control,
ensuring users can only access resources appropriate to their roles.
"""

import pytest
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.http import HttpResponse

from accounts_app.models import CustomerProfile, ServiceProviderProfile
from accounts_app.constants import UserRoles, UserStatus
from ..fixtures.test_data import CustomerTestData, ServiceProviderTestData, CommonTestData

User = get_user_model()


class RoleBasedAuthenticationTest(TestCase):
    """Test role-based authentication restrictions."""
    
    def setUp(self):
        """Set up test client and data."""
        self.client = Client()
        self.customer_data = CustomerTestData()
        self.provider_data = ServiceProviderTestData()
        self.common_data = CommonTestData()
        
    def test_customer_login_restrictions(self):
        """Test customer can only login through customer login."""
        customer = self.customer_data.create_active_customer()
        
        # Customer can login through customer login
        customer_login_url = reverse('accounts_app:customer_login')
        response = self.client.post(customer_login_url, {
            'email': customer.email,
            'password': 'TestPass123!'
        })
        
        self.assertEqual(response.status_code, 302)
        self.assertEqual(str(customer.pk), self.client.session['_auth_user_id'])
        
        # Logout for next test
        self.client.logout()
        
        # Customer cannot login through provider login
        provider_login_url = reverse('accounts_app:service_provider_login')
        response = self.client.post(provider_login_url, {
            'email': customer.email,
            'password': 'TestPass123!'
        })
        
        self.assertEqual(response.status_code, 200)  # Form redisplay
        self.assertNotIn('_auth_user_id', self.client.session)
        self.assertContains(response, 'Invalid')
        
    def test_provider_login_restrictions(self):
        """Test service provider can only login through provider login."""
        provider = self.provider_data.create_active_provider()
        
        # Provider can login through provider login
        provider_login_url = reverse('accounts_app:service_provider_login')
        response = self.client.post(provider_login_url, {
            'email': provider.email,
            'password': 'TestPass123!'
        })
        
        self.assertEqual(response.status_code, 302)
        self.assertEqual(str(provider.pk), self.client.session['_auth_user_id'])
        
        # Logout for next test
        self.client.logout()
        
        # Provider cannot login through customer login
        customer_login_url = reverse('accounts_app:customer_login')
        response = self.client.post(customer_login_url, {
            'email': provider.email,
            'password': 'TestPass123!'
        })
        
        self.assertEqual(response.status_code, 200)  # Form redisplay
        self.assertNotIn('_auth_user_id', self.client.session)
        
    def test_admin_login_restrictions(self):
        """Test admin users can access admin interface."""
        # Create admin user
        admin = User.objects.create_user(
            email='<EMAIL>',
            password='TestPass123!',
            role=UserRoles.ADMIN,
            is_staff=True,
            is_superuser=True,
            is_active=True
        )
        
        # Admin can login through admin interface
        admin_login_url = reverse('admin:login')
        response = self.client.post(admin_login_url, {
            'username': admin.email,
            'password': 'TestPass123!',
        })
        
        # Should redirect to admin dashboard
        self.assertEqual(response.status_code, 302)
        
        # Logout for next test
        self.client.logout()
        
        # Admin cannot login through customer login
        customer_login_url = reverse('accounts_app:customer_login')
        response = self.client.post(customer_login_url, {
            'email': admin.email,
            'password': 'TestPass123!'
        })
        
        self.assertEqual(response.status_code, 200)  # Form redisplay
        self.assertNotIn('_auth_user_id', self.client.session)


class RoleBasedAccessControlTest(TestCase):
    """Test role-based access control to different pages."""
    
    def setUp(self):
        """Set up test client and data."""
        self.client = Client()
        self.customer_data = CustomerTestData()
        self.provider_data = ServiceProviderTestData()
        
    def test_customer_page_access_control(self):
        """Test customer pages are only accessible to customers."""
        customer = self.customer_data.create_active_customer()
        provider = self.provider_data.create_active_provider()
        
        customer_pages = [
            reverse('accounts_app:customer_profile'),
            reverse('accounts_app:customer_change_password'),
        ]
        
        # Customer can access customer pages
        self.client.login(email=customer.email, password='TestPass123!')
        
        for page_url in customer_pages:
            response = self.client.get(page_url)
            self.assertEqual(response.status_code, 200)
            
        self.client.logout()
        
        # Provider cannot access customer pages
        self.client.login(email=provider.email, password='TestPass123!')
        
        for page_url in customer_pages:
            response = self.client.get(page_url)
            self.assertEqual(response.status_code, 302)  # Redirect (access denied)
            
    def test_provider_page_access_control(self):
        """Test provider pages are only accessible to providers."""
        customer = self.customer_data.create_active_customer()
        provider = self.provider_data.create_active_provider()
        
        provider_pages = [
            reverse('accounts_app:service_provider_profile'),
            reverse('accounts_app:service_provider_change_password'),
        ]
        
        # Provider can access provider pages
        self.client.login(email=provider.email, password='TestPass123!')
        
        for page_url in provider_pages:
            response = self.client.get(page_url)
            self.assertEqual(response.status_code, 200)
            
        self.client.logout()
        
        # Customer cannot access provider pages
        self.client.login(email=customer.email, password='TestPass123!')
        
        for page_url in provider_pages:
            response = self.client.get(page_url)
            self.assertEqual(response.status_code, 302)  # Redirect (access denied)
            
    def test_unauthenticated_access_restrictions(self):
        """Test unauthenticated users cannot access protected pages."""
        protected_pages = [
            reverse('accounts_app:customer_profile'),
            reverse('accounts_app:customer_change_password'),
            reverse('accounts_app:service_provider_profile'),
            reverse('accounts_app:service_provider_change_password'),
        ]
        
        for page_url in protected_pages:
            response = self.client.get(page_url)
            self.assertEqual(response.status_code, 302)  # Redirect to login
            self.assertIn('login', response.url)


class RoleBasedRegistrationTest(TestCase):
    """Test role-based registration workflows."""
    
    def setUp(self):
        """Set up test client and data."""
        self.client = Client()
        self.customer_data = CustomerTestData()
        self.provider_data = ServiceProviderTestData()
        
    def test_customer_registration_creates_customer_profile(self):
        """Test customer registration creates appropriate profile."""
        signup_url = reverse('accounts_app:customer_signup')
        registration_data = self.customer_data.get_valid_customer_registration_data()
        
        response = self.client.post(signup_url, data=registration_data)
        
        self.assertEqual(response.status_code, 302)
        
        # Verify user created with correct role
        user = User.objects.get(email=registration_data['email'])
        self.assertEqual(user.role, UserRoles.CUSTOMER)
        
        # Verify customer profile created
        profile = CustomerProfile.objects.get(user=user)
        self.assertIsNotNone(profile)
        
        # Verify no provider profile created
        self.assertFalse(ServiceProviderProfile.objects.filter(user=user).exists())
        
    def test_provider_registration_creates_provider_profile(self):
        """Test provider registration creates appropriate profile."""
        signup_url = reverse('accounts_app:service_provider_signup')
        registration_data = self.provider_data.get_valid_provider_registration_data()
        
        response = self.client.post(signup_url, data=registration_data)
        
        self.assertEqual(response.status_code, 302)
        
        # Verify user created with correct role
        user = User.objects.get(email=registration_data['email'])
        self.assertEqual(user.role, UserRoles.SERVICE_PROVIDER)
        
        # Verify provider profile created
        profile = ServiceProviderProfile.objects.get(user=user)
        self.assertIsNotNone(profile)
        self.assertEqual(profile.business_name, registration_data['business_name'])
        
        # Verify no customer profile created
        self.assertFalse(CustomerProfile.objects.filter(user=user).exists())
        
    def test_authenticated_user_registration_restrictions(self):
        """Test authenticated users cannot access registration pages."""
        customer = self.customer_data.create_active_customer()
        
        # Login as customer
        self.client.login(email=customer.email, password='TestPass123!')
        
        # Should be redirected from registration pages
        registration_pages = [
            reverse('accounts_app:customer_signup'),
            reverse('accounts_app:service_provider_signup'),
        ]
        
        for page_url in registration_pages:
            response = self.client.get(page_url)
            self.assertEqual(response.status_code, 302)  # Redirect away from registration


class RoleBasedRedirectTest(TestCase):
    """Test role-based redirects after authentication."""
    
    def setUp(self):
        """Set up test client and data."""
        self.client = Client()
        self.customer_data = CustomerTestData()
        self.provider_data = ServiceProviderTestData()
        
    def test_customer_login_redirect(self):
        """Test customer login redirects to customer profile."""
        customer = self.customer_data.create_active_customer()
        
        login_url = reverse('accounts_app:customer_login')
        response = self.client.post(login_url, {
            'email': customer.email,
            'password': 'TestPass123!'
        })
        
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('accounts_app:customer_profile'))
        
    def test_provider_login_redirect(self):
        """Test provider login redirects to provider profile."""
        provider = self.provider_data.create_active_provider()
        
        login_url = reverse('accounts_app:service_provider_login')
        response = self.client.post(login_url, {
            'email': provider.email,
            'password': 'TestPass123!'
        })
        
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('accounts_app:service_provider_profile'))
        
    def test_customer_registration_redirect(self):
        """Test customer registration redirects to customer profile."""
        signup_url = reverse('accounts_app:customer_signup')
        registration_data = self.customer_data.get_valid_customer_registration_data()
        
        response = self.client.post(signup_url, data=registration_data)
        
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('accounts_app:customer_profile'))
        
    def test_provider_registration_redirect(self):
        """Test provider registration redirects to done page."""
        signup_url = reverse('accounts_app:service_provider_signup')
        registration_data = self.provider_data.get_valid_provider_registration_data()
        
        response = self.client.post(signup_url, data=registration_data)
        
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('accounts_app:provider_signup_done'))


class RoleBasedLogoutTest(TestCase):
    """Test role-based logout behavior."""
    
    def setUp(self):
        """Set up test client and data."""
        self.client = Client()
        self.customer_data = CustomerTestData()
        self.provider_data = ServiceProviderTestData()
        
    def test_customer_logout_redirect(self):
        """Test customer logout redirects to customer login."""
        customer = self.customer_data.create_active_customer()
        
        # Login and logout
        self.client.login(email=customer.email, password='TestPass123!')
        response = self.client.get(reverse('accounts_app:logout'))
        
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('accounts_app:customer_login'))
        
    def test_provider_logout_redirect(self):
        """Test provider logout redirects to provider login."""
        provider = self.provider_data.create_active_provider()
        
        # Login and logout
        self.client.login(email=provider.email, password='TestPass123!')
        response = self.client.get(reverse('accounts_app:logout'))
        
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('accounts_app:service_provider_login'))


class CrossRoleInteractionTest(TestCase):
    """Test cross-role interaction restrictions."""
    
    def setUp(self):
        """Set up test client and data."""
        self.client = Client()
        self.customer_data = CustomerTestData()
        self.provider_data = ServiceProviderTestData()
        
    def test_role_switching_prevention(self):
        """Test users cannot switch roles after authentication."""
        customer = self.customer_data.create_active_customer()
        provider = self.provider_data.create_active_provider()
        
        # Login as customer
        self.client.login(email=customer.email, password='TestPass123!')
        
        # Try to access provider pages (should be blocked)
        provider_pages = [
            reverse('accounts_app:service_provider_profile'),
        ]
        
        for page_url in provider_pages:
            response = self.client.get(page_url)
            self.assertEqual(response.status_code, 302)  # Redirect (access denied)
            
        # Logout and login as provider
        self.client.logout()
        self.client.login(email=provider.email, password='TestPass123!')
        
        # Try to access customer pages (should be blocked)
        customer_pages = [
            reverse('accounts_app:customer_profile'),
        ]
        
        for page_url in customer_pages:
            response = self.client.get(page_url)
            self.assertEqual(response.status_code, 302)  # Redirect (access denied)
            
    def test_session_isolation_between_roles(self):
        """Test session data is isolated between different roles."""
        customer = self.customer_data.create_active_customer()
        provider = self.provider_data.create_active_provider()
        
        # Login as customer and set session data
        self.client.login(email=customer.email, password='TestPass123!')
        session = self.client.session
        session['customer_data'] = 'customer_specific_data'
        session.save()
        
        # Logout and login as provider
        self.client.logout()
        self.client.login(email=provider.email, password='TestPass123!')
        
        # Session should be clean
        self.assertNotIn('customer_data', self.client.session)
        
        # Set provider session data
        session = self.client.session
        session['provider_data'] = 'provider_specific_data'
        session.save()
        
        # Switch back to customer
        self.client.logout()
        self.client.login(email=customer.email, password='TestPass123!')
        
        # Should not have provider data
        self.assertNotIn('provider_data', self.client.session)


@pytest.mark.django_db
class RolePermissionsPytestTest:
    """Pytest-style role permissions tests."""
    
    def test_role_based_authentication_flow(self):
        """Test complete authentication flow for different roles."""
        customer_data = CustomerTestData()
        provider_data = ServiceProviderTestData()
        
        # Test customer flow
        customer = customer_data.create_active_customer()
        client = Client()
        
        # Customer login
        login_url = reverse('accounts_app:customer_login')
        response = client.post(login_url, {
            'email': customer.email,
            'password': 'TestPass123!'
        })
        
        assert response.status_code == 302
        assert str(customer.pk) == client.session['_auth_user_id']
        
        # Customer can access customer profile
        profile_url = reverse('accounts_app:customer_profile')
        response = client.get(profile_url)
        assert response.status_code == 200
        
        # Customer cannot access provider profile
        provider_profile_url = reverse('accounts_app:service_provider_profile')
        response = client.get(provider_profile_url)
        assert response.status_code == 302
        
        client.logout()
        
        # Test provider flow
        provider = provider_data.create_active_provider()
        
        # Provider login
        login_url = reverse('accounts_app:service_provider_login')
        response = client.post(login_url, {
            'email': provider.email,
            'password': 'TestPass123!'
        })
        
        assert response.status_code == 302
        assert str(provider.pk) == client.session['_auth_user_id']
        
        # Provider can access provider profile
        response = client.get(provider_profile_url)
        assert response.status_code == 200
        
        # Provider cannot access customer profile
        response = client.get(profile_url)
        assert response.status_code == 302
        
    def test_role_based_registration_flow(self):
        """Test registration creates appropriate profiles for each role."""
        customer_data = CustomerTestData()
        provider_data = ServiceProviderTestData()
        
        # Test customer registration
        client = Client()
        signup_url = reverse('accounts_app:customer_signup')
        registration_data = customer_data.get_valid_customer_registration_data()
        
        response = client.post(signup_url, data=registration_data)
        assert response.status_code == 302
        
        customer = User.objects.get(email=registration_data['email'])
        assert customer.role == UserRoles.CUSTOMER
        assert CustomerProfile.objects.filter(user=customer).exists()
        assert not ServiceProviderProfile.objects.filter(user=customer).exists()
        
        # Test provider registration
        client = Client()
        signup_url = reverse('accounts_app:service_provider_signup')
        registration_data = provider_data.get_valid_provider_registration_data()
        
        response = client.post(signup_url, data=registration_data)
        assert response.status_code == 302
        
        provider = User.objects.get(email=registration_data['email'])
        assert provider.role == UserRoles.SERVICE_PROVIDER
        assert ServiceProviderProfile.objects.filter(user=provider).exists()
        assert not CustomerProfile.objects.filter(user=provider).exists()
        
    def test_comprehensive_role_security(self):
        """Test comprehensive role-based security across the system."""
        customer_data = CustomerTestData()
        provider_data = ServiceProviderTestData()
        
        customer = customer_data.create_active_customer()
        provider = provider_data.create_active_provider()
        
        # Test all combinations of role/URL access
        test_cases = [
            # (user, url, expected_accessible)
            (customer, reverse('accounts_app:customer_profile'), True),
            (customer, reverse('accounts_app:customer_change_password'), True),
            (customer, reverse('accounts_app:service_provider_profile'), False),
            (customer, reverse('accounts_app:service_provider_change_password'), False),
            
            (provider, reverse('accounts_app:service_provider_profile'), True),
            (provider, reverse('accounts_app:service_provider_change_password'), True),
            (provider, reverse('accounts_app:customer_profile'), False),
            (provider, reverse('accounts_app:customer_change_password'), False),
        ]
        
        for user, url, should_have_access in test_cases:
            client = Client()
            client.login(email=user.email, password='TestPass123!')
            
            response = client.get(url)
            
            if should_have_access:
                assert response.status_code == 200
            else:
                assert response.status_code == 302  # Redirect (access denied)
                
            client.logout() 