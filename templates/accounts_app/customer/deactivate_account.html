{% extends 'base.html' %}

{% block title %}Deactivate Account - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

{% endblock %}

{% block content %}
<section class="deactivate-section">
    <div class="deactivate-container">
        <div class="deactivate-card">
            <div class="deactivate-header">
                <div class="content">
                    <div class="deactivate-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h1 class="deactivate-title">Deactivate Account</h1>
                    <p class="deactivate-subtitle">This action will temporarily disable your account</p>
                </div>
            </div>

            <div class="deactivate-body">
                <div class="warning-info">
                    <div class="content">
                        <h5><i class="fas fa-info-circle"></i>Important Information</h5>
                        <p>This is a permanent action that cannot be undone. Deactivating your account will:</p>
                        <ul>
                            <li>You will lose access to your account</li>
                            <li>Your bookings history will be preserved</li>
                            <li>Hide your profile from other users</li>
                            <li>Preserve your data securely</li>
                            <li>Allow reactivation by contacting support</li>
                        </ul>
                    </div>
                </div>

                <form method="post" novalidate>
                  {% csrf_token %}

                  {% if form.non_field_errors %}
                  <div class="alert-cw alert-cw-error mb-4">
                    {% for error in form.non_field_errors %}
                    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}

                  <div class="mb-4">
                    <label for="{{ form.confirm_email.id_for_label }}" class="form-label">
                      Type your email to confirm deactivation
                    </label>
                    {% if form.confirm_email.errors %}
                      {{ form.confirm_email|add_class:"form-control-cw is-invalid"|attr:"placeholder:Enter your email to confirm" }}
                    {% else %}
                      {{ form.confirm_email|add_class:"form-control-cw"|attr:"placeholder:Enter your email to confirm" }}
                    {% endif %}
                    {% if form.confirm_email.errors %}
                    <div class="invalid-feedback" role="alert" aria-live="polite">
                      {% for error in form.confirm_email.errors %}
                      <i class="fas fa-exclamation-circle"></i>{{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                    {% if form.confirm_email.help_text %}
                    <div class="form-text">{{ form.confirm_email.help_text }}</div>
                    {% endif %}
                  </div>

                  <div class="d-flex flex-column flex-sm-row gap-3 justify-content-between">
                    <a href="{% url 'accounts_app:customer_profile' %}" class="btn-cw-secondary">
                      <i class="fas fa-arrow-left"></i>Cancel
                    </a>
                    <button type="submit" class="btn-cw-danger">
                      <i class="fas fa-user-slash"></i>Deactivate Account
                    </button>
                  </div>
                </form>
            </div>
        </div>
    </div>
</section>
{% endblock %}
