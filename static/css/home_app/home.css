/* Home App - Home Page Styles */

/* Search Label */
.search-label {
    opacity: 0;
    pointer-events: none;
}

/* Venue Card Image Overlay */
.venue-image-overlay {
    background: linear-gradient(45deg, rgba(47, 22, 15, 0.1) 0%, transparent 50%);
}

/* Search Form */
.search-form-group {
    position: relative;
}

.search-btn {
    width: 100%;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 0.75rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Booking Counter */
.booking-count {
    text-align: center;
    margin-top: 1rem;
    color: #6c757d;
    font-size: 0.9rem;
}

.booking-count strong {
    color: #667eea;
    font-weight: 700;
}

/* Section Titles */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2F160F;
    margin-bottom: 1rem;
}

.section-subtitle {
    text-align: center;
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 3rem;
}

/* Category Cards */
.category-card {
    text-align: center;
    padding: 2rem 1rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
}

.category-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: inherit;
}

.category-icon {
    font-size: 2.5rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.category-name {
    font-weight: 600;
    color: #2F160F;
    margin-bottom: 0.5rem;
}

.category-count {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Venue Cards */
.card-venue {
    border: none;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.card-venue:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.card-img-container {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.card-img-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-img-placeholder {
    width: 100%;
    height: 100%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 2rem;
}

/* Service Cards */
.service-card-highlight {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
}

.service-card-highlight:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: inherit;
}

.service-icon {
    font-size: 2.5rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.service-name {
    font-weight: 600;
    color: #2F160F;
    margin-bottom: 0.5rem;
}

.service-description {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Business Section */
.business-section {
    background: linear-gradient(135deg, #fae1d7 0%, #f8f9fa 100%);
    padding: 4rem 0;
}

.business-title {
    font-size: 3rem;
    font-weight: 700;
    color: #2F160F;
    margin-bottom: 1.5rem;
}

.business-description {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.btn-cw-primary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-cw-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    color: white;
    text-decoration: none;
}

.btn-cw-secondary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    background: white;
    color: #667eea;
    border: 2px solid #667eea;
    border-radius: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-cw-secondary:hover {
    background: #667eea;
    color: white;
    text-decoration: none;
}

/* Rating Container */
.rating-container {
    margin-top: 2rem;
}

.rating-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2F160F;
    margin-bottom: 0.5rem;
}

.star-rating {
    color: #ffc107;
    font-size: 1.5rem;
}

.review-text {
    color: #6c757d;
    font-size: 0.9rem;
}

/* App Preview */
.app-preview {
    text-align: center;
}

.app-interface {
    max-width: 100%;
    height: auto;
    border-radius: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* View More Button */
.btn-view-more {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 0.75rem;
    padding: 1rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-view-more:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .section-title {
        font-size: 2rem;
    }
    
    .business-title {
        font-size: 2.5rem;
    }
    
    .category-card,
    .service-card-highlight {
        padding: 1.5rem 1rem;
    }
    
    .category-icon,
    .service-icon {
        font-size: 2rem;
    }
} 