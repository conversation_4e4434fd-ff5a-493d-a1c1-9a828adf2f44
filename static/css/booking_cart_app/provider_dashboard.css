/* Booking Cart App - Provider Dashboard Styles */

/* Dashboard Cards */
.dashboard-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.2s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

/* Metric Icons */
.metric-icon {
    width: 56px;
    height: 56px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.metric-icon-total {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-icon-pending {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #333;
}

.metric-icon-today {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
}

.metric-icon-revenue {
    background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
}

/* Booking Cards */
.booking-card {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    transition: all 0.2s ease;
}

.booking-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 10px rgba(0,123,255,0.15);
}

/* Status Badges */
.status-badge {
    font-weight: 500;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.875rem;
}

/* Timeline Items */
.timeline-item {
    border-left: 3px solid #e9ecef;
    padding-left: 1rem;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
}

.timeline-item:last-child {
    border-left-color: transparent;
    margin-bottom: 0;
}

/* Violation Alert */
.violation-alert {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border: none;
    border-radius: 12px;
}

/* Quick Action Buttons */
.quick-action-btn {
    border-radius: 25px;
    border: 2px solid #e9ecef;
    background: white;
    color: #6c757d;
    transition: all 0.2s ease;
    text-decoration: none;
    padding: 0.75rem 1.5rem;
    display: inline-block;
}

.quick-action-btn:hover {
    border-color: #007bff;
    color: #007bff;
    text-decoration: none;
    transform: translateY(-1px);
} 