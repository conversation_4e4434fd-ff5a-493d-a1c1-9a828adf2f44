"""
View mixins for accounts_app.

This module provides reusable mixins for common access control patterns
across the accounts_app views. These mixins promote DRY principles and ensure
consistent behavior for user authentication and profile requirements.
"""

# --- Django Imports ---
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin
from django.shortcuts import redirect
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ..constants import UserRoles


class ProfileRequiredMixin(LoginRequiredMixin):
    """
    Mixin to require user to have a complete profile.
    
    This mixin ensures that the authenticated user has a profile record
    based on their role (customer or service provider). If no profile exists,
    the user is redirected to create one.
    
    Attributes:
        profile_missing_message (str): Message displayed when profile is missing
        profile_missing_redirect_url (str): URL to redirect to when profile is missing
    """
    
    profile_missing_message = _("Please complete your profile before accessing this page.")
    profile_missing_redirect_url = None
    
    def dispatch(self, request, *args, **kwargs):
        """Check if user has required profile after login verification."""
        response = super().dispatch(request, *args, **kwargs)
        
        # If parent dispatch didn't return a response, check profile
        if hasattr(response, 'status_code'):
            return response
            
        if not self._user_has_profile(request.user):
            messages.warning(request, self.profile_missing_message)
            return redirect(self._get_profile_redirect_url(request.user))
        
        return super().dispatch(request, *args, **kwargs)
    
    def _user_has_profile(self, user):
        """
        Check if user has a profile based on their role.
        
        Args:
            user (CustomUser): The user to check
            
        Returns:
            bool: True if user has appropriate profile, False otherwise
        """
        if not user.is_authenticated:
            return False
            
        try:
            if user.role == UserRoles.CUSTOMER:
                return hasattr(user, 'customer_profile') and user.customer_profile is not None
            elif user.role == UserRoles.SERVICE_PROVIDER:
                return hasattr(user, 'service_provider_profile') and user.service_provider_profile is not None
            elif user.role == UserRoles.ADMIN:
                # Admin users don't need profiles
                return True
            else:
                return False
        except Exception:
            # If there's any error checking profile, assume it doesn't exist
            return False
    
    def _get_profile_redirect_url(self, user):
        """
        Get the appropriate URL to redirect to for profile creation.
        
        Args:
            user (CustomUser): The user to get redirect URL for
            
        Returns:
            str: URL to redirect to
        """
        if self.profile_missing_redirect_url:
            return self.profile_missing_redirect_url
            
        # Default redirects based on user role
        if user.role == UserRoles.CUSTOMER:
            return reverse_lazy('accounts_app:customer_profile_edit')
        elif user.role == UserRoles.SERVICE_PROVIDER:
            return reverse_lazy('accounts_app:service_provider_profile_edit')
        else:
            # Fallback to home page
            return reverse_lazy('home')


class EmailVerifiedMixin(LoginRequiredMixin):
    """
    Mixin to require user to have verified their email address.
    
    This mixin ensures that the authenticated user has verified their email
    address before accessing the protected view. If email is not verified,
    the user is redirected to a verification page.
    
    Attributes:
        email_verification_message (str): Message displayed when email is not verified
        email_verification_redirect_url (str): URL to redirect to when email is not verified
        skip_verification_for_admin (bool): Whether to skip verification check for admin users
    """
    
    email_verification_message = _("Please verify your email address before accessing this page.")
    email_verification_redirect_url = None
    skip_verification_for_admin = True
    
    def dispatch(self, request, *args, **kwargs):
        """Check if user has verified their email after login verification."""
        response = super().dispatch(request, *args, **kwargs)
        
        # If parent dispatch didn't return a response, check email verification
        if hasattr(response, 'status_code'):
            return response
            
        if not self._user_email_verified(request.user):
            messages.warning(request, self.email_verification_message)
            return redirect(self._get_verification_redirect_url(request.user))
        
        return super().dispatch(request, *args, **kwargs)
    
    def _user_email_verified(self, user):
        """
        Check if user has verified their email address.
        
        Args:
            user (CustomUser): The user to check
            
        Returns:
            bool: True if email is verified, False otherwise
        """
        if not user.is_authenticated:
            return False
            
        # Skip verification for admin users if configured
        if self.skip_verification_for_admin and user.role == UserRoles.ADMIN:
            return True
            
        # Check the email_verified field
        return getattr(user, 'email_verified', False)
    
    def _get_verification_redirect_url(self, user):
        """
        Get the appropriate URL to redirect to for email verification.
        
        Args:
            user (CustomUser): The user to get redirect URL for
            
        Returns:
            str: URL to redirect to
        """
        if self.email_verification_redirect_url:
            return self.email_verification_redirect_url
            
        # Default redirects based on user role
        if user.role == UserRoles.CUSTOMER:
            return reverse_lazy('accounts_app:customer_login')
        elif user.role == UserRoles.SERVICE_PROVIDER:
            return reverse_lazy('accounts_app:service_provider_login')
        else:
            # Fallback to home page
            return reverse_lazy('home')


class ProfileAndEmailRequiredMixin(ProfileRequiredMixin, EmailVerifiedMixin):
    """
    Composite mixin that requires both a complete profile and verified email.
    
    This mixin combines the functionality of ProfileRequiredMixin and
    EmailVerifiedMixin to ensure users have both a complete profile and
    verified email address.
    
    Order of checks:
    1. Login required (from LoginRequiredMixin)
    2. Email verified (from EmailVerifiedMixin)
    3. Profile complete (from ProfileRequiredMixin)
    """
    
    def dispatch(self, request, *args, **kwargs):
        """Check email verification first, then profile completion."""
        # First check email verification
        if not self._user_email_verified(request.user):
            messages.warning(request, self.email_verification_message)
            return redirect(self._get_verification_redirect_url(request.user))
        
        # Then check profile completion
        if not self._user_has_profile(request.user):
            messages.warning(request, self.profile_missing_message)
            return redirect(self._get_profile_redirect_url(request.user))
        
        return super(ProfileRequiredMixin, self).dispatch(request, *args, **kwargs)


class CustomerProfileRequiredMixin(ProfileRequiredMixin):
    """
    Mixin specifically for customer profile requirements.
    
    This mixin extends ProfileRequiredMixin to specifically handle customer
    profile requirements and provides customer-specific error messages and
    redirect URLs.
    """
    
    profile_missing_message = _("Please complete your customer profile before accessing this page.")
    profile_missing_redirect_url = reverse_lazy('accounts_app:customer_profile_edit')
    
    def dispatch(self, request, *args, **kwargs):
        """Ensure user is a customer before checking profile."""
        if not request.user.is_authenticated:
            return self.handle_no_permission()
            
        if not request.user.is_customer:
            messages.error(request, _("This page is only available to customers."))
            return redirect('home')
        
        return super().dispatch(request, *args, **kwargs)


class ServiceProviderProfileRequiredMixin(ProfileRequiredMixin):
    """
    Mixin specifically for service provider profile requirements.
    
    This mixin extends ProfileRequiredMixin to specifically handle service
    provider profile requirements and provides provider-specific error messages
    and redirect URLs.
    """
    
    profile_missing_message = _("Please complete your business profile before accessing this page.")
    profile_missing_redirect_url = reverse_lazy('accounts_app:service_provider_profile_edit')
    
    def dispatch(self, request, *args, **kwargs):
        """Ensure user is a service provider before checking profile."""
        if not request.user.is_authenticated:
            return self.handle_no_permission()
            
        if not request.user.is_service_provider:
            messages.error(request, _("This page is only available to service providers."))
            return redirect('home')
        
        return super().dispatch(request, *args, **kwargs) 