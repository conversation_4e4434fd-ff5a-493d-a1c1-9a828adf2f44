# This file maintains backward compatibility by importing all models from the new modular structure
# All models have been reorganized into logical modules under accounts_app/models/

# Import all models from the reorganized modules
from .models.auth import (
    CustomUser,
    CustomUserManager,
    ActiveUserManager,
    CustomerManager,
    ProviderManager,
    AdminManager,
)

from .models.profiles import (
    UserProfile,
    CustomerProfile,
    ServiceProviderProfile,
)

from .models.security import (
    UserSecurity,
    LoginHistory,
    LoginAlert,
    AccountLockout,
    PasswordHistory,
)

from .models.preferences import (
    UserPreferences,
    EmailPreferences,
    ProfilePrivacySettings,
)

from .models.verification import (
    EmailVerificationToken,
    ProfileVerification,
)

from .models.team import (
    TeamMember,
)

# Export all models for backward compatibility
__all__ = [
    # Auth models
    'CustomUser',
    'CustomUserManager',
    'ActiveUserManager',
    'CustomerManager',
    'ProviderManager',
    'AdminManager',
    
    # Profile models
    'UserProfile',
    'CustomerProfile',
    'ServiceProviderProfile',
    
    # Security models
    'UserSecurity',
    'LoginHistory',
    'LoginAlert',
    'AccountLockout',
    'PasswordHistory',
    
    # Preference models
    'UserPreferences',
    'EmailPreferences',
    'ProfilePrivacySettings',
    
    # Verification models
    'EmailVerificationToken',
    'ProfileVerification',
    
    # Team models
    'TeamMember',
]

