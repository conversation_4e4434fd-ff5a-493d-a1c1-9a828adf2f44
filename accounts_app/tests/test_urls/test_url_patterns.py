"""
Comprehensive URL pattern tests for accounts_app.
Tests all URLs defined in the current urls.py implementation.
"""

from django.test import TestCase
from django.urls import reverse, resolve

from accounts_app.views import (
    CustomerLoginView, CustomerLogoutView, CustomerProfileView, CustomerProfileEditView,
    CustomerPasswordChangeView, CustomerDeactivateAccountView,
    ServiceProviderProfileView, ServiceProviderProfileEditView,
    UnifiedLogoutView, business_landing_view, email_verification_view,
    resend_verification_email_view, email_verification_success_view,
    email_verification_sent_view, service_provider_change_password_view,
    service_provider_deactivate_account_view
)
from accounts_app.views import ajax, privacy


class URLPatternsTests(TestCase):
    """Test cases for all URL patterns defined in accounts_app/urls.py"""

    def test_business_landing_url(self):
        """Test business landing page URL"""
        url = reverse('accounts_app:for_business')
        self.assertEqual(url, '/accounts/for-business/')
        
        # Test URL resolution
        resolved = resolve('/accounts/for-business/')
        self.assertEqual(resolved.func, business_landing_view)

    def test_customer_login_url(self):
        """Test customer login URL"""
        url = reverse('accounts_app:customer_login')
        self.assertEqual(url, '/accounts/customer/login/')
        
        # Test URL resolution
        resolved = resolve('/accounts/customer/login/')
        self.assertEqual(resolved.func.view_class, CustomerLoginView)

    def test_customer_logout_url(self):
        """Test customer logout URL"""
        url = reverse('accounts_app:customer_logout')
        self.assertEqual(url, '/accounts/customer/logout/')
        
        # Test URL resolution
        resolved = resolve('/accounts/customer/logout/')
        self.assertEqual(resolved.func.view_class, CustomerLogoutView)

    def test_unified_logout_url(self):
        """Test unified logout URL"""
        url = reverse('accounts_app:unified_logout')
        self.assertEqual(url, '/accounts/logout/')
        
        # Test URL resolution
        resolved = resolve('/accounts/logout/')
        self.assertEqual(resolved.func.view_class, UnifiedLogoutView)

    def test_customer_profile_url(self):
        """Test customer profile URL"""
        url = reverse('accounts_app:customer_profile')
        self.assertEqual(url, '/accounts/customer/profile/')
        
        # Test URL resolution
        resolved = resolve('/accounts/customer/profile/')
        self.assertEqual(resolved.func.view_class, CustomerProfileView)

    def test_customer_profile_edit_url(self):
        """Test customer profile edit URL"""
        url = reverse('accounts_app:customer_profile_edit')
        self.assertEqual(url, '/accounts/customer/profile/edit/')
        
        # Test URL resolution
        resolved = resolve('/accounts/customer/profile/edit/')
        self.assertEqual(resolved.func.view_class, CustomerProfileEditView)

    def test_customer_change_password_url(self):
        """Test customer change password URL"""
        url = reverse('accounts_app:customer_change_password')
        self.assertEqual(url, '/accounts/customer/change-password/')
        
        # Test URL resolution
        resolved = resolve('/accounts/customer/change-password/')
        self.assertEqual(resolved.func.view_class, CustomerPasswordChangeView)

    def test_customer_deactivate_url(self):
        """Test customer deactivate account URL"""
        url = reverse('accounts_app:customer_deactivate')
        self.assertEqual(url, '/accounts/customer/deactivate/')
        
        # Test URL resolution
        resolved = resolve('/accounts/customer/deactivate/')
        self.assertEqual(resolved.func.view_class, CustomerDeactivateAccountView)

    def test_provider_profile_url(self):
        """Test provider profile URL"""
        url = reverse('accounts_app:service_provider_profile')
        self.assertEqual(url, '/accounts/provider/profile/')
        
        # Test URL resolution
        resolved = resolve('/accounts/provider/profile/')
        self.assertEqual(resolved.func.view_class, ServiceProviderProfileView)

    def test_provider_profile_edit_url(self):
        """Test provider profile edit URL"""
        url = reverse('accounts_app:service_provider_profile_edit')
        self.assertEqual(url, '/accounts/provider/profile/edit/')
        
        # Test URL resolution
        resolved = resolve('/accounts/provider/profile/edit/')
        self.assertEqual(resolved.func.view_class, ServiceProviderProfileEditView)

    def test_provider_change_password_url(self):
        """Test provider change password URL"""
        url = reverse('accounts_app:service_provider_change_password')
        self.assertEqual(url, '/accounts/provider/change-password/')
        
        # Test URL resolution
        resolved = resolve('/accounts/provider/change-password/')
        self.assertEqual(resolved.func, service_provider_change_password_view)

    def test_provider_deactivate_url(self):
        """Test provider deactivate account URL"""
        url = reverse('accounts_app:service_provider_deactivate')
        self.assertEqual(url, '/accounts/provider/deactivate/')
        
        # Test URL resolution
        resolved = resolve('/accounts/provider/deactivate/')
        self.assertEqual(resolved.func, service_provider_deactivate_account_view)

    def test_customer_profile_ajax_url(self):
        """Test customer profile AJAX URL"""
        url = reverse('accounts_app:customer_profile_ajax')
        self.assertEqual(url, '/accounts/ajax/customer/profile/update/')
        
        # Test URL resolution
        resolved = resolve('/accounts/ajax/customer/profile/update/')
        self.assertEqual(resolved.func.view_class, ajax.CustomerProfileAjaxView)

    def test_provider_profile_ajax_url(self):
        """Test provider profile AJAX URL"""
        url = reverse('accounts_app:provider_profile_ajax')
        self.assertEqual(url, '/accounts/ajax/provider/profile/update/')
        
        # Test URL resolution
        resolved = resolve('/accounts/ajax/provider/profile/update/')
        self.assertEqual(resolved.func.view_class, ajax.ServiceProviderProfileAjaxView)

    def test_validate_field_ajax_url(self):
        """Test validate field AJAX URL"""
        url = reverse('accounts_app:validate_field_ajax')
        self.assertEqual(url, '/accounts/ajax/validate-field/')
        
        # Test URL resolution
        resolved = resolve('/accounts/ajax/validate-field/')
        self.assertEqual(resolved.func, ajax.validate_field_ajax)

    def test_email_verification_url(self):
        """Test email verification URL"""
        url = reverse('accounts_app:email_verification', kwargs={'token': 'test-token'})
        self.assertEqual(url, '/accounts/verify-email/test-token/')
        
        # Test URL resolution
        resolved = resolve('/accounts/verify-email/test-token/')
        self.assertEqual(resolved.func, email_verification_view)

    def test_resend_verification_url(self):
        """Test resend verification email URL"""
        url = reverse('accounts_app:resend_verification')
        self.assertEqual(url, '/accounts/resend-verification/')
        
        # Test URL resolution
        resolved = resolve('/accounts/resend-verification/')
        self.assertEqual(resolved.func, resend_verification_email_view)

    def test_email_verification_success_url(self):
        """Test email verification success URL"""
        url = reverse('accounts_app:email_verification_success')
        self.assertEqual(url, '/accounts/email-verification-success/')
        
        # Test URL resolution
        resolved = resolve('/accounts/email-verification-success/')
        self.assertEqual(resolved.func, email_verification_success_view)

    def test_email_verification_sent_url(self):
        """Test email verification sent URL"""
        url = reverse('accounts_app:email_verification_sent')
        self.assertEqual(url, '/accounts/email-verification-sent/')
        
        # Test URL resolution
        resolved = resolve('/accounts/email-verification-sent/')
        self.assertEqual(resolved.func, email_verification_sent_view)

    def test_privacy_settings_url(self):
        """Test privacy settings URL"""
        url = reverse('accounts_app:privacy_settings')
        self.assertEqual(url, '/accounts/privacy/settings/')
        
        # Test URL resolution
        resolved = resolve('/accounts/privacy/settings/')
        self.assertEqual(resolved.func.view_class, privacy.PrivacySettingsView)

    def test_privacy_settings_update_url(self):
        """Test privacy settings update URL"""
        url = reverse('accounts_app:privacy_settings_update')
        self.assertEqual(url, '/accounts/privacy/settings/update/')
        
        # Test URL resolution
        resolved = resolve('/accounts/privacy/settings/update/')
        self.assertEqual(resolved.func.view_class, privacy.PrivacySettingsUpdateView)

    def test_privacy_quick_toggle_url(self):
        """Test privacy quick toggle URL"""
        url = reverse('accounts_app:privacy_quick_toggle')
        self.assertEqual(url, '/accounts/privacy/quick-toggle/')
        
        # Test URL resolution
        resolved = resolve('/accounts/privacy/quick-toggle/')
        self.assertEqual(resolved.func, privacy.privacy_settings_quick_toggle)

    def test_data_export_url(self):
        """Test data export URL"""
        url = reverse('accounts_app:data_export')
        self.assertEqual(url, '/accounts/data/export/')
        
        # Test URL resolution
        resolved = resolve('/accounts/data/export/')
        self.assertEqual(resolved.func.view_class, privacy.DataExportView)

    def test_data_export_download_url(self):
        """Test data export download URL"""
        url = reverse('accounts_app:data_export_download', kwargs={'filename': 'test-file.json'})
        self.assertEqual(url, '/accounts/data/export/download/test-file.json/')
        
        # Test URL resolution
        resolved = resolve('/accounts/data/export/download/test-file.json/')
        self.assertEqual(resolved.func.view_class, privacy.DataExportDownloadView)

    def test_data_deletion_request_url(self):
        """Test data deletion request URL"""
        url = reverse('accounts_app:data_deletion_request')
        self.assertEqual(url, '/accounts/data/deletion/request/')
        
        # Test URL resolution
        resolved = resolve('/accounts/data/deletion/request/')
        self.assertEqual(resolved.func.view_class, privacy.DataDeletionRequestView)

    def test_verification_status_url(self):
        """Test verification status URL"""
        url = reverse('accounts_app:verification_status')
        self.assertEqual(url, '/accounts/verification/status/')
        
        # Test URL resolution
        resolved = resolve('/accounts/verification/status/')
        self.assertEqual(resolved.func.view_class, privacy.VerificationBadgeView)

    def test_request_verification_url(self):
        """Test request verification URL"""
        url = reverse('accounts_app:request_verification')
        self.assertEqual(url, '/accounts/verification/request/')
        
        # Test URL resolution
        resolved = resolve('/accounts/verification/request/')
        self.assertEqual(resolved.func.view_class, privacy.RequestVerificationView)

    def test_verification_status_ajax_url(self):
        """Test verification status AJAX URL"""
        url = reverse('accounts_app:verification_status_ajax')
        self.assertEqual(url, '/accounts/verification/status/ajax/')
        
        # Test URL resolution
        resolved = resolve('/accounts/verification/status/ajax/')
        self.assertEqual(resolved.func.view_class, privacy.VerificationStatusView)

    def test_verification_summary_url(self):
        """Test verification summary URL"""
        url = reverse('accounts_app:verification_summary')
        self.assertEqual(url, '/accounts/verification/summary/')
        
        # Test URL resolution
        resolved = resolve('/accounts/verification/summary/')
        self.assertEqual(resolved.func, privacy.user_verification_summary) 