"""
Form Validation Demonstration

This script demonstrates the new form validation mixins and error handling
system implemented in the accounts_app forms.

Run this script to see examples of:
- Validation mixins in action
- Custom error messages
- Error handling patterns
- Form field validation
"""

import os
import sys
import django

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project_root.settings.development')
django.setup()

from django.core.exceptions import ValidationError
from accounts_app.mixins import (
    EmailValidationMixin,
    PasswordValidationMixin,
    PhoneValidationMixin,
    ImageValidationMixin,
    URLValidationMixin,
    BusinessValidationMixin,
    TermsValidationMixin
)
from accounts_app.forms.errors import get_error_message, get_success_message
from accounts_app.forms.customer import CustomerSignupForm, CustomerProfileForm
from accounts_app.forms.provider import ServiceProviderSignupForm, ServiceProviderProfileForm


def demonstrate_error_messages():
    """Demonstrate the error message system."""
    print("=== Error Message System Demo ===")
    
    # Email errors
    print("\n1. Email Validation Errors:")
    print(f"   - Invalid format: {get_error_message('email', 'invalid_format')}")
    print(f"   - Email exists: {get_error_message('email', 'email_exists')}")
    print(f"   - Email mismatch: {get_error_message('email', 'email_mismatch')}")
    
    # Password errors
    print("\n2. Password Validation Errors:")
    print(f"   - Too short: {get_error_message('password', 'too_short')}")
    print(f"   - Too common: {get_error_message('password', 'too_common')}")
    print(f"   - Password mismatch: {get_error_message('password', 'password_mismatch')}")
    print(f"   - Weak password: {get_error_message('password', 'weak_password')}")
    
    # Phone errors
    print("\n3. Phone Validation Errors:")
    print(f"   - Invalid format: {get_error_message('phone', 'invalid_format')}")
    print(f"   - Too short: {get_error_message('phone', 'too_short')}")
    print(f"   - Invalid US format: {get_error_message('phone', 'invalid_us_format')}")
    
    # Image errors with formatting
    print("\n4. Image Validation Errors (with formatting):")
    print(f"   - File too large: {get_error_message('image', 'file_too_large', max_size=5)}")
    print(f"   - Image too small: {get_error_message('image', 'image_too_small', min_width=100, min_height=100)}")
    print(f"   - Invalid format: {get_error_message('image', 'invalid_format')}")
    
    # Success messages
    print("\n5. Success Messages:")
    print(f"   - Profile updated: {get_success_message('profile_updated')}")
    print(f"   - Password changed: {get_success_message('password_changed')}")
    print(f"   - Account created: {get_success_message('account_created')}")


def demonstrate_form_validation():
    """Demonstrate form validation with mixins."""
    print("\n\n=== Form Validation Demo ===")
    
    # Test customer signup form
    print("\n1. Customer Signup Form Validation:")
    
    # Valid data
    valid_data = {
        'email': '<EMAIL>',
        'password1': 'StrongPass123!',
        'password2': 'StrongPass123!',
        'agree_to_terms': True
    }
    
    form = CustomerSignupForm(data=valid_data)
    print(f"   - Valid form: {form.is_valid()}")
    
    # Invalid email
    invalid_email_data = valid_data.copy()
    invalid_email_data['email'] = 'invalid-email'
    form = CustomerSignupForm(data=invalid_email_data)
    print(f"   - Invalid email: {form.is_valid()}")
    if not form.is_valid():
        print(f"     Errors: {form.errors}")
    
    # Password mismatch
    password_mismatch_data = valid_data.copy()
    password_mismatch_data['password2'] = 'DifferentPass123!'
    form = CustomerSignupForm(data=password_mismatch_data)
    print(f"   - Password mismatch: {form.is_valid()}")
    if not form.is_valid():
        print(f"     Errors: {form.errors}")
    
    # Weak password
    weak_password_data = valid_data.copy()
    weak_password_data['password1'] = 'weak'
    weak_password_data['password2'] = 'weak'
    form = CustomerSignupForm(data=weak_password_data)
    print(f"   - Weak password: {form.is_valid()}")
    if not form.is_valid():
        print(f"     Errors: {form.errors}")


def demonstrate_mixin_classes():
    """Demonstrate individual validation mixins."""
    print("\n\n=== Validation Mixin Classes Demo ===")
    
    # Create a simple test form using mixins
    class TestForm(EmailValidationMixin, PasswordValidationMixin, PhoneValidationMixin):
        def __init__(self, data=None):
            self.cleaned_data = data or {}
    
    print("\n1. Email Validation Mixin:")
    form = TestForm({'email': '<EMAIL>'})
    try:
        result = form.clean_email()
        print(f"   - Valid email: {result}")
    except ValidationError as e:
        print(f"   - Error: {e}")
    
    form = TestForm({'email': 'invalid-email'})
    try:
        result = form.clean_email()
        print(f"   - Invalid email: {result}")
    except ValidationError as e:
        print(f"   - Error: {e}")
    
    print("\n2. Phone Validation Mixin:")
    form = TestForm({'phone_number': '+****************'})
    try:
        result = form._clean_phone_field('+****************')
        print(f"   - Valid phone: {result}")
    except ValidationError as e:
        print(f"   - Error: {e}")
    
    form = TestForm({'phone_number': '123'})
    try:
        result = form._clean_phone_field('123')
        print(f"   - Invalid phone: {result}")
    except ValidationError as e:
        print(f"   - Error: {e}")
    
    print("\n3. Password Validation Mixin:")
    form = TestForm({'password1': 'StrongPass123!'})
    try:
        result = form._check_password_strength('StrongPass123!')
        print(f"   - Strong password: {result}")
    except ValidationError as e:
        print(f"   - Error: {e}")
    
    form = TestForm({'password1': 'weak'})
    try:
        result = form._check_password_strength('weak')
        print(f"   - Weak password: {result}")
    except ValidationError as e:
        print(f"   - Error: {e}")


def demonstrate_business_validation():
    """Demonstrate business validation mixins."""
    print("\n\n=== Business Validation Demo ===")
    
    class TestBusinessForm(BusinessValidationMixin, URLValidationMixin):
        def __init__(self, data=None):
            self.cleaned_data = data or {}
    
    print("\n1. EIN Validation:")
    form = TestBusinessForm({'ein': '12-3456789'})
    try:
        result = form.clean_ein()
        print(f"   - Valid EIN: {result}")
    except ValidationError as e:
        print(f"   - Error: {e}")
    
    form = TestBusinessForm({'ein': '123'})
    try:
        result = form.clean_ein()
        print(f"   - Invalid EIN: {result}")
    except ValidationError as e:
        print(f"   - Error: {e}")
    
    print("\n2. ZIP Code Validation:")
    form = TestBusinessForm({'zip_code': '12345'})
    try:
        result = form.clean_zip_code()
        print(f"   - Valid ZIP: {result}")
    except ValidationError as e:
        print(f"   - Error: {e}")
    
    form = TestBusinessForm({'zip_code': '123'})
    try:
        result = form.clean_zip_code()
        print(f"   - Invalid ZIP: {result}")
    except ValidationError as e:
        print(f"   - Error: {e}")
    
    print("\n3. Website URL Validation:")
    form = TestBusinessForm({'website': 'https://example.com'})
    try:
        result = form.clean_website()
        print(f"   - Valid website: {result}")
    except ValidationError as e:
        print(f"   - Error: {e}")
    
    form = TestBusinessForm({'website': 'not-a-url'})
    try:
        result = form._clean_url_field('not-a-url')
        print(f"   - Invalid website: {result}")
    except ValidationError as e:
        print(f"   - Error: {e}")


def demonstrate_form_integration():
    """Demonstrate integration with existing forms."""
    print("\n\n=== Form Integration Demo ===")
    
    print("\n1. Service Provider Signup Form:")
    
    # Valid service provider data
    valid_provider_data = {
        'email': '<EMAIL>',
        'password1': 'StrongPass123!',
        'password2': 'StrongPass123!',
        'legal_name': 'Example Business LLC',
        'display_name': 'Example Business',
        'contact_name': 'John Doe',
        'phone': '+****************',
        'address': '123 Main St',
        'city': 'Example City',
        'state': 'CA',
        'zip_code': '12345',
        'ein': '12-3456789',
        'agree_to_terms': True
    }
    
    form = ServiceProviderSignupForm(data=valid_provider_data)
    print(f"   - Valid form: {form.is_valid()}")
    
    # Invalid EIN
    invalid_ein_data = valid_provider_data.copy()
    invalid_ein_data['ein'] = '123'
    form = ServiceProviderSignupForm(data=invalid_ein_data)
    print(f"   - Invalid EIN: {form.is_valid()}")
    if not form.is_valid():
        print(f"     Errors: {form.errors}")


def main():
    """Run all demonstrations."""
    print("🔍 Form Validation System Demonstration")
    print("=" * 50)
    
    try:
        demonstrate_error_messages()
        demonstrate_form_validation()
        demonstrate_mixin_classes()
        demonstrate_business_validation()
        demonstrate_form_integration()
        
        print("\n\n✅ All demonstrations completed successfully!")
        print("\nKey Features Demonstrated:")
        print("- ✅ Standardized error messages across all forms")
        print("- ✅ Reusable validation mixins for common patterns")
        print("- ✅ Consistent error handling with custom messages")
        print("- ✅ Form field validation with proper error codes")
        print("- ✅ Business-specific validation (EIN, ZIP, etc.)")
        print("- ✅ Integration with existing form classes")
        
    except Exception as e:
        print(f"\n❌ Error during demonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main() 