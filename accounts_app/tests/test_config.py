"""
Test configuration and utilities for the accounts_app comprehensive test suite.
"""

import os
import pytest
from django.test.utils import override_settings
from django.contrib.auth import get_user_model

User = get_user_model()

# Test settings
TEST_SETTINGS = {
    'DATABASES': {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': ':memory:',
        }
    },
    'PASSWORD_HASHERS': [
        'django.contrib.auth.hashers.MD5PasswordHasher',
    ],
    'EMAIL_BACKEND': 'django.core.mail.backends.locmem.EmailBackend',
    'CELERY_TASK_ALWAYS_EAGER': True,
    'CELERY_TASK_EAGER_PROPAGATES': True,
    'CACHES': {
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        }
    },
    'MEDIA_ROOT': '/tmp/test_media',
    'STATIC_ROOT': '/tmp/test_static',
    'USE_TZ': True,
}

# Test data factories
class TestDataFactory:
    """Factory for creating test data"""
    
    @staticmethod
    def create_customer_user(email='<EMAIL>', password='testpass123'):
        """Create a customer user for testing"""
        return User.objects.create_user(
            email=email,
            password=password,
            role='customer',
            status='active',
            email_verified=True
        )
    
    @staticmethod
    def create_provider_user(email='<EMAIL>', password='testpass123'):
        """Create a provider user for testing"""
        return User.objects.create_user(
            email=email,
            password=password,
            role='service_provider',
            status='active',
            email_verified=True
        )
    
    @staticmethod
    def create_admin_user(email='<EMAIL>', password='testpass123'):
        """Create an admin user for testing"""
        return User.objects.create_superuser(
            email=email,
            password=password
        )

# Test mixins
class BaseTestMixin:
    """Base mixin for common test functionality"""
    
    def setUp(self):
        """Set up common test data"""
        self.factory = TestDataFactory()
        super().setUp()

# Test decorators
def skip_if_no_database(func):
    """Skip test if database is not available"""
    def wrapper(*args, **kwargs):
        try:
            from django.db import connection
            connection.ensure_connection()
            return func(*args, **kwargs)
        except Exception:
            pytest.skip("Database not available")
    return wrapper

# Test utilities
class TestUtils:
    """Utilities for testing"""
    
    @staticmethod
    def create_test_file(content='test content', filename='test.txt'):
        """Create a test file"""
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix=filename, delete=False) as f:
            f.write(content)
            return f.name
    
    @staticmethod
    def create_test_image(width=100, height=100):
        """Create a test image"""
        from PIL import Image
        import io
        
        image = Image.new('RGB', (width, height), color='red')
        image_file = io.BytesIO()
        image.save(image_file, 'JPEG')
        image_file.seek(0)
        return image_file
    
    @staticmethod
    def assert_redirects_to_login(response, login_url='/accounts/login/'):
        """Assert that response redirects to login"""
        assert response.status_code == 302
        assert login_url in response.url

# Test configuration for pytest
@pytest.fixture(scope='session')
def django_db_setup():
    """Set up database for pytest"""
    pass

@pytest.fixture
def user_factory():
    """Fixture for user factory"""
    return TestDataFactory()

@pytest.fixture
def test_utils():
    """Fixture for test utilities"""
    return TestUtils()

# Test categories
TEST_CATEGORIES = {
    'unit': [
        'accounts_app.tests.test_models',
        'accounts_app.tests.test_forms',
        'accounts_app.tests.test_utils',
        'accounts_app.tests.test_services',
    ],
    'integration': [
        'accounts_app.tests.test_views',
        'accounts_app.tests.test_integration',
    ],
    'functional': [
        'accounts_app.tests.test_urls',
        'accounts_app.tests.test_templates',
    ],
    'security': [
        'accounts_app.tests.test_security',
        'accounts_app.tests.test_middleware',
    ],
    'performance': [
        'accounts_app.tests.test_performance',
    ],
}

# Test commands
def run_tests(category='all', verbosity=2):
    """Run tests for a specific category"""
    import subprocess
    
    if category == 'all':
        cmd = ['python', 'manage.py', 'test', 'accounts_app.tests', f'--verbosity={verbosity}']
    elif category in TEST_CATEGORIES:
        modules = ' '.join(TEST_CATEGORIES[category])
        cmd = ['python', 'manage.py', 'test'] + TEST_CATEGORIES[category] + [f'--verbosity={verbosity}']
    else:
        raise ValueError(f"Unknown test category: {category}")
    
    return subprocess.run(cmd, capture_output=True, text=True)

def run_coverage():
    """Run tests with coverage"""
    import subprocess
    
    cmd = [
        'coverage', 'run', '--source=accounts_app', 
        'manage.py', 'test', 'accounts_app.tests'
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        subprocess.run(['coverage', 'report'])
        subprocess.run(['coverage', 'html'])
    
    return result

# Test documentation
TESTING_GUIDE = """
# Accounts App Testing Guide

## Test Structure

The accounts_app test suite is organized into the following directories:

- `test_models/`: Tests for Django models
- `test_views/`: Tests for Django views
- `test_forms/`: Tests for Django forms
- `test_utils/`: Tests for utility functions and decorators
- `test_services/`: Tests for service layer
- `test_security/`: Tests for security features
- `test_middleware/`: Tests for custom middleware
- `test_urls/`: Tests for URL patterns
- `test_templates/`: Tests for template rendering
- `test_integration/`: Integration tests

## Running Tests

### All Tests
```bash
python manage.py test accounts_app.tests
```

### Specific Category
```bash
python manage.py test accounts_app.tests.test_models
python manage.py test accounts_app.tests.test_views
```

### With Coverage
```bash
coverage run --source=accounts_app manage.py test accounts_app.tests
coverage report
coverage html
```

### Using pytest
```bash
pytest accounts_app/tests/
```

## Test Data

Use the `TestDataFactory` class to create test data:

```python
from accounts_app.tests.test_config import TestDataFactory

factory = TestDataFactory()
customer = factory.create_customer_user()
provider = factory.create_provider_user()
admin = factory.create_admin_user()
```

## Test Utilities

The `TestUtils` class provides helpful utilities:

```python
from accounts_app.tests.test_config import TestUtils

utils = TestUtils()
test_file = utils.create_test_file()
test_image = utils.create_test_image()
utils.assert_redirects_to_login(response)
```

## Best Practices

1. Use descriptive test names
2. Follow the AAA pattern (Arrange, Act, Assert)
3. Use factories for test data creation
4. Mock external dependencies
5. Test both success and failure scenarios
6. Keep tests independent and isolated
7. Use appropriate test categories
8. Document complex test scenarios
"""

if __name__ == '__main__':
    print(TESTING_GUIDE) 