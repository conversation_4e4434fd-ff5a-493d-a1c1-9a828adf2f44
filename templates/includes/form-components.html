{% comment %}
CozyWish Form Components Template
This template contains reusable form components for consistent use across the project.
Include this template in your forms to ensure consistency.

Usage:
{% include 'includes/form-components.html' %}
{% endcomment %}

{% load static %}

<!-- Booking Form Component -->
{% block booking_form %}
<div class="form-booking">
    <h3 class="mb-4">Book a Service</h3>
    <form method="post" class="booking-form">
        {% csrf_token %}
        
        <div class="mb-3">
            <label for="service" class="form-label">Service Type</label>
            <select class="form-select" id="service" name="service" required>
                <option value="" selected disabled>Choose a service...</option>
                <option value="massage">Massage</option>
                <option value="facial">Facial</option>
                <option value="spa_package">Spa Package</option>
                <option value="manicure">Manicure</option>
                <option value="pedicure">Pedicure</option>
            </select>
        </div>
        
        <div class="mb-3">
            <label for="date" class="form-label">Preferred Date</label>
            <input type="date" class="form-control" id="date" name="date" required>
        </div>
        
        <div class="mb-3">
            <label for="time" class="form-label">Preferred Time</label>
            <select class="form-select" id="time" name="time" required>
                <option value="" selected disabled>Choose a time...</option>
                <option value="09:00">9:00 AM</option>
                <option value="10:00">10:00 AM</option>
                <option value="11:00">11:00 AM</option>
                <option value="12:00">12:00 PM</option>
                <option value="13:00">1:00 PM</option>
                <option value="14:00">2:00 PM</option>
                <option value="15:00">3:00 PM</option>
                <option value="16:00">4:00 PM</option>
                <option value="17:00">5:00 PM</option>
            </select>
        </div>
        
        <div class="mb-3">
            <label for="name" class="form-label">Your Name</label>
            <input type="text" class="form-control" id="name" name="name" placeholder="Enter your name" required>
        </div>
        
        <div class="mb-3">
            <label for="email" class="form-label">Email Address</label>
            <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email" required>
        </div>
        
        <div class="mb-3">
            <label for="phone" class="form-label">Phone Number</label>
            <input type="tel" class="form-control" id="phone" name="phone" placeholder="Enter your phone number" required>
        </div>
        
        <div class="mb-3">
            <label for="requests" class="form-label">Special Requests</label>
            <textarea class="form-control" id="requests" name="requests" rows="3" placeholder="Any special requests or notes..."></textarea>
        </div>
        
        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
            <label class="form-check-label" for="terms">
                I agree to the <a href="#" class="text-brand-primary">terms and conditions</a>
            </label>
        </div>
        
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-calendar-plus me-2"></i>
            Book Now
        </button>
    </form>
</div>
{% endblock %}

<!-- Contact Form Component -->
{% block contact_form %}
<div class="form-contact">
    <h3 class="mb-4">Get in Touch</h3>
    <form method="post" class="contact-form">
        {% csrf_token %}
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="firstName" class="form-label">First Name</label>
                    <input type="text" class="form-control" id="firstName" name="first_name" placeholder="John" required>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="lastName" class="form-label">Last Name</label>
                    <input type="text" class="form-control" id="lastName" name="last_name" placeholder="Doe" required>
                </div>
            </div>
        </div>
        
        <div class="mb-3">
            <label for="contactEmail" class="form-label">Email Address</label>
            <input type="email" class="form-control" id="contactEmail" name="email" placeholder="<EMAIL>" required>
        </div>
        
        <div class="mb-3">
            <label for="subject" class="form-label">Subject</label>
            <select class="form-select" id="subject" name="subject" required>
                <option value="" selected disabled>Choose a subject...</option>
                <option value="general">General Inquiry</option>
                <option value="booking">Booking Support</option>
                <option value="technical">Technical Issue</option>
                <option value="feedback">Feedback</option>
                <option value="partnership">Partnership</option>
            </select>
        </div>
        
        <div class="mb-3">
            <label for="message" class="form-label">Message</label>
            <textarea class="form-control" id="message" name="message" rows="4" placeholder="Tell us how we can help..." required></textarea>
        </div>
        
        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="newsletter" name="newsletter">
            <label class="form-check-label" for="newsletter">
                Subscribe to our newsletter for updates and special offers
            </label>
        </div>
        
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-paper-plane me-2"></i>
            Send Message
        </button>
    </form>
</div>
{% endblock %}

<!-- Search Form Component -->
{% block search_form %}
<div class="input-group-search">
    <input type="text" class="form-control" placeholder="Search for services, locations, or venues..." name="search" aria-label="Search">
    <span class="input-group-text">
        <i class="fas fa-search"></i>
    </span>
</div>
{% endblock %}

<!-- Location Search Form Component -->
{% block location_search_form %}
<div class="input-group-search">
    <input type="text" class="form-control" placeholder="Enter your location..." name="location" aria-label="Location">
    <span class="input-group-text">
        <i class="fas fa-map-marker-alt"></i>
    </span>
</div>
{% endblock %}

<!-- Review Form Component -->
{% block review_form %}
<div class="form-contact">
    <h3 class="mb-4">Write a Review</h3>
    <form method="post" class="review-form">
        {% csrf_token %}
        
        <div class="mb-3">
            <label for="rating" class="form-label">Rating</label>
            <select class="form-select" id="rating" name="rating" required>
                <option value="" selected disabled>Select your rating...</option>
                <option value="5">⭐⭐⭐⭐⭐ Excellent (5 stars)</option>
                <option value="4">⭐⭐⭐⭐ Very Good (4 stars)</option>
                <option value="3">⭐⭐⭐ Good (3 stars)</option>
                <option value="2">⭐⭐ Fair (2 stars)</option>
                <option value="1">⭐ Poor (1 star)</option>
            </select>
        </div>
        
        <div class="mb-3">
            <label for="reviewTitle" class="form-label">Review Title</label>
            <input type="text" class="form-control" id="reviewTitle" name="title" placeholder="Summarize your experience..." required>
        </div>
        
        <div class="mb-3">
            <label for="reviewContent" class="form-label">Your Review</label>
            <textarea class="form-control" id="reviewContent" name="content" rows="4" placeholder="Share your experience in detail..." required></textarea>
        </div>
        
        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="anonymous" name="anonymous">
            <label class="form-check-label" for="anonymous">
                Submit anonymously
            </label>
        </div>
        
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-star me-2"></i>
            Submit Review
        </button>
    </form>
</div>
{% endblock %}

<!-- Login Form Component -->
{% block login_form %}
<div class="form-contact">
    <h3 class="mb-4">Sign In</h3>
    <form method="post" class="login-form">
        {% csrf_token %}
        
        <div class="mb-3">
            <label for="loginEmail" class="form-label">Email Address</label>
            <input type="email" class="form-control" id="loginEmail" name="email" placeholder="Enter your email" required>
        </div>
        
        <div class="mb-3">
            <label for="loginPassword" class="form-label">Password</label>
            <input type="password" class="form-control" id="loginPassword" name="password" placeholder="Enter your password" required>
        </div>
        
        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="rememberMe" name="remember_me">
            <label class="form-check-label" for="rememberMe">
                Remember me
            </label>
        </div>
        
        <button type="submit" class="btn btn-primary w-100">
            <i class="fas fa-sign-in-alt me-2"></i>
            Sign In
        </button>
        
        <div class="text-center mt-3">
            <a href="#" class="text-brand-primary">Forgot your password?</a>
        </div>
    </form>
</div>
{% endblock %}

<!-- Registration Form Component -->
{% block registration_form %}
<div class="form-contact">
    <h3 class="mb-4">Create Account</h3>
    <form method="post" class="registration-form">
        {% csrf_token %}
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="regFirstName" class="form-label">First Name</label>
                    <input type="text" class="form-control" id="regFirstName" name="first_name" placeholder="John" required>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="regLastName" class="form-label">Last Name</label>
                    <input type="text" class="form-control" id="regLastName" name="last_name" placeholder="Doe" required>
                </div>
            </div>
        </div>
        
        <div class="mb-3">
            <label for="regEmail" class="form-label">Email Address</label>
            <input type="email" class="form-control" id="regEmail" name="email" placeholder="<EMAIL>" required>
        </div>
        
        <div class="mb-3">
            <label for="regPassword" class="form-label">Password</label>
            <input type="password" class="form-control" id="regPassword" name="password1" placeholder="Create a strong password" required>
            <div class="form-text">Password must be at least 8 characters long.</div>
        </div>
        
        <div class="mb-3">
            <label for="regPasswordConfirm" class="form-label">Confirm Password</label>
            <input type="password" class="form-control" id="regPasswordConfirm" name="password2" placeholder="Confirm your password" required>
        </div>
        
        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="agreeTerms" name="agree_terms" required>
            <label class="form-check-label" for="agreeTerms">
                I agree to the <a href="#" class="text-brand-primary">terms and conditions</a>
            </label>
        </div>
        
        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="newsletterSignup" name="newsletter">
            <label class="form-check-label" for="newsletterSignup">
                Subscribe to our newsletter for updates and special offers
            </label>
        </div>
        
        <button type="submit" class="btn btn-primary w-100">
            <i class="fas fa-user-plus me-2"></i>
            Create Account
        </button>
    </form>
</div>
{% endblock %}

<!-- Filter Form Component -->
{% block filter_form %}
<div class="form-contact">
    <h3 class="mb-4">Filter Results</h3>
    <form method="get" class="filter-form">
        
        <div class="mb-3">
            <label for="category" class="form-label">Category</label>
            <select class="form-select" id="category" name="category">
                <option value="">All Categories</option>
                <option value="spa">Spa & Wellness</option>
                <option value="massage">Massage</option>
                <option value="facial">Facial</option>
                <option value="nail">Nail Care</option>
                <option value="hair">Hair Care</option>
            </select>
        </div>
        
        <div class="mb-3">
            <label for="priceRange" class="form-label">Price Range</label>
            <select class="form-select" id="priceRange" name="price_range">
                <option value="">Any Price</option>
                <option value="0-50">$0 - $50</option>
                <option value="51-100">$51 - $100</option>
                <option value="101-200">$101 - $200</option>
                <option value="201+">$201+</option>
            </select>
        </div>
        
        <div class="mb-3">
            <label for="rating" class="form-label">Minimum Rating</label>
            <select class="form-select" id="rating" name="rating">
                <option value="">Any Rating</option>
                <option value="4">4+ Stars</option>
                <option value="3">3+ Stars</option>
                <option value="2">2+ Stars</option>
            </select>
        </div>
        
        <div class="mb-3">
            <label for="distance" class="form-label">Distance</label>
            <select class="form-select" id="distance" name="distance">
                <option value="">Any Distance</option>
                <option value="5">Within 5 miles</option>
                <option value="10">Within 10 miles</option>
                <option value="25">Within 25 miles</option>
                <option value="50">Within 50 miles</option>
            </select>
        </div>
        
        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="available" name="available">
            <label class="form-check-label" for="available">
                Available today
            </label>
        </div>
        
        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="discount" name="discount">
            <label class="form-check-label" for="discount">
                Has discounts
            </label>
        </div>
        
        <button type="submit" class="btn btn-primary w-100">
            <i class="fas fa-filter me-2"></i>
            Apply Filters
        </button>
        
        <button type="reset" class="btn btn-outline-secondary w-100 mt-2">
            <i class="fas fa-undo me-2"></i>
            Clear Filters
        </button>
    </form>
</div>
{% endblock %}

<!-- Newsletter Signup Form Component -->
{% block newsletter_form %}
<div class="form-contact">
    <h3 class="mb-4">Stay Updated</h3>
    <p class="text-muted mb-3">Get the latest deals and wellness tips delivered to your inbox.</p>
    <form method="post" class="newsletter-form">
        {% csrf_token %}
        
        <div class="mb-3">
            <label for="newsletterEmail" class="form-label">Email Address</label>
            <input type="email" class="form-control" id="newsletterEmail" name="email" placeholder="Enter your email address" required>
        </div>
        
        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="newsletterConsent" name="consent" required>
            <label class="form-check-label" for="newsletterConsent">
                I consent to receive marketing emails from CozyWish
            </label>
        </div>
        
        <button type="submit" class="btn btn-primary w-100">
            <i class="fas fa-envelope me-2"></i>
            Subscribe
        </button>
    </form>
</div>
{% endblock %} 