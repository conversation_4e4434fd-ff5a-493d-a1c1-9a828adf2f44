/* Booking Cart App - Daily Schedule Styles */

/* Schedule Container */
.schedule-container {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
}

/* Schedule Header */
.schedule-header {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

/* Date Navigation */
.date-navigation {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.date-nav-btn {
    background: #4f46e5;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.date-nav-btn:hover {
    background: #4338ca;
    transform: translateY(-1px);
}

.current-date {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
}

.stat-card.revenue {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.availability {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.conflicts {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Schedule Grid */
.schedule-grid {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

.schedule-hour {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
    min-height: 80px;
}

.schedule-hour:last-child {
    border-bottom: none;
}

.hour-label {
    width: 80px;
    background: #f9fafb;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #6b7280;
    border-right: 1px solid #e5e7eb;
}

.hour-content {
    flex: 1;
    padding: 12px;
    position: relative;
}

/* Booking Items */
.booking-item {
    background: #dbeafe;
    border: 2px solid #3b82f6;
    border-radius: 8px;
    padding: 8px 12px;
    margin: 4px 0;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
}

.booking-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.booking-item.confirmed {
    background: #d1fae5;
    border-color: #10b981;
}

.booking-item.pending {
    background: #fef3c7;
    border-color: #f59e0b;
}

.booking-item.completed {
    background: #e0e7ff;
    border-color: #8b5cf6;
}

.booking-item.conflict {
    background: #fee2e2;
    border-color: #ef4444;
    animation: pulse 2s infinite;
}

.booking-customer {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 2px;
}

.booking-service {
    font-size: 0.8rem;
    color: #6b7280;
    margin-bottom: 2px;
}

.booking-time {
    font-size: 0.75rem;
    opacity: 0.8;
}

.booking-status {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #3b82f6;
}

.booking-status.confirmed {
    background: #10b981;
}

.booking-status.pending {
    background: #f59e0b;
}

.booking-status.completed {
    background: #8b5cf6;
}

/* Availability Indicator */
.availability-indicator {
    font-size: 0.8rem;
    color: #6b7280;
    margin: 4px 0;
    padding: 4px 8px;
    background: #f3f4f6;
    border-radius: 4px;
}

.availability-indicator.no-availability {
    opacity: 0.5;
}

/* Conflict Alerts */
.conflict-alert {
    background: #fee2e2;
    border: 1px solid #ef4444;
    color: #991b1b;
    padding: 8px 12px;
    border-radius: 6px;
    margin: 4px 0;
    font-size: 0.8rem;
}

/* Bulk Actions */
.bulk-actions {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    transform: translateY(100px);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 1000;
}

.bulk-actions.show {
    transform: translateY(0);
    opacity: 1;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    background: white;
    padding: 40px;
    border-radius: 12px;
    text-align: center;
}

/* Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .schedule-hour {
        flex-direction: column;
    }
    
    .hour-label {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #e5e7eb;
    }
} 