"""
Settings validation for CozyWish project.
Validates that all required settings are properly configured.
"""

import os
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured


def validate_settings():
    """Validate that all required settings are properly configured."""
    errors = []
    
    # Required settings for all environments
    required_settings = [
        'SECRET_KEY',
        'DATABASES',
        'INSTALLED_APPS',
        'MIDDLEWARE',
        'TEMPLATES',
        'STATIC_URL',
        'STATIC_ROOT',
    ]
    
    for setting in required_settings:
        if not hasattr(settings, setting):
            errors.append(f"Missing required setting: {setting}")
    
    # Environment-specific validations
    if not settings.DEBUG:
        # Production validations
        production_required = [
            'ALLOWED_HOSTS',
            'CSRF_TRUSTED_ORIGINS',
        ]
        
        for setting in production_required:
            if not hasattr(settings, setting) or not getattr(settings, setting):
                errors.append(f"Missing production setting: {setting}")
        
        # Security validations for production
        if not settings.SECURE_SSL_REDIRECT:
            errors.append("SECURE_SSL_REDIRECT should be True in production")
        
        if not settings.SESSION_COOKIE_SECURE:
            errors.append("SESSION_COOKIE_SECURE should be True in production")
    
    # Database validation
    if 'default' not in settings.DATABASES:
        errors.append("DATABASES must contain a 'default' configuration")
    
    # Custom user model validation
    if not hasattr(settings, 'AUTH_USER_MODEL'):
        errors.append("AUTH_USER_MODEL must be defined")
    
    if errors:
        raise ImproperlyConfigured(
            f"Settings validation failed:\n" + "\n".join(f"- {error}" for error in errors)
        )


def validate_environment_variables():
    """Validate that required environment variables are set."""
    required_env_vars = []
    
    if not settings.DEBUG:
        # Production environment variables
        required_env_vars.extend([
            'SECRET_KEY',
            'DATABASE_URL',
        ])
        
        # Optional but recommended for production
        recommended_env_vars = [
            'AWS_ACCESS_KEY_ID',
            'AWS_SECRET_ACCESS_KEY',
            'AWS_STORAGE_BUCKET_NAME',
            'EMAIL_HOST_PASSWORD',
        ]
        
        missing_recommended = []
        for var in recommended_env_vars:
            if not os.environ.get(var):
                missing_recommended.append(var)
        
        if missing_recommended:
            print(f"WARNING: Recommended environment variables not set: {', '.join(missing_recommended)}")
    
    missing_required = []
    for var in required_env_vars:
        if not os.environ.get(var):
            missing_required.append(var)
    
    if missing_required:
        raise ImproperlyConfigured(
            f"Required environment variables not set: {', '.join(missing_required)}"
        ) 