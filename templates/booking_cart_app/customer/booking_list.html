{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}My Bookings - CozyWish{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4>My Bookings</h4>
                    <p class="mb-0 text-muted">View and manage your service bookings</p>
                </div>
                <div class="card-body">
                    {% if bookings %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Booking ID</th>
                                        <th>Venue</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Total</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for booking in bookings %}
                                    <tr>
                                        <td>
                                            <strong>{{ booking.booking_id|truncatechars:8 }}</strong>
                                            <br>
                                            <small class="text-muted">{{ booking.created_at|date:"M d, Y" }}</small>
                                        </td>
                                        <td>
                                            <strong>{{ booking.venue.venue_name }}</strong>
                                            <br>
                                            <small class="text-muted">{{ booking.venue.city }}, {{ booking.venue.state }}</small>
                                            <br>
                                            <small class="text-info">
                                                {% for item in booking.items.all|slice:":2" %}
                                                    {{ item.service_title }}{% if not forloop.last %}, {% endif %}
                                                {% endfor %}
                                                {% if booking.items.count > 2 %}
                                                    <span class="badge badge-secondary">+{{ booking.items.count|add:"-2" }} more</span>
                                                {% endif %}
                                            </small>
                                        </td>
                                        <td>
                                            {{ booking.booking_date|date:"F d, Y" }}
                                            <br>
                                            <small class="text-muted">
                                                {% for item in booking.items.all|slice:":1" %}
                                                    {{ item.selected_time_slot }}
                                                {% endfor %}
                                                {% if booking.items.count > 1 %}
                                                    <span class="badge badge-secondary">+{{ booking.items.count|add:"-1" }} more</span>
                                                {% endif %}
                                            </small>
                                        </td>
                                        <td>
                                            <span class="badge badge-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% elif booking.status == 'cancelled' %}danger{% else %}secondary{% endif %}">
                                                {{ booking.get_status_display }}
                                            </span>
                                        </td>
                                        <td>
                                            <strong>${{ booking.total_price }}</strong>
                                        </td>
                                        <td>
                                            <a href="{% url 'booking_cart_app:booking_detail' booking.slug %}" 
                                               class="btn btn-sm btn-outline-primary">
                                                View Details
                                            </a>
                                            {% if booking.status == 'pending' or booking.status == 'confirmed' %}
                                            <a href="{% url 'booking_cart_app:cancel_booking' booking.slug %}" 
                                               class="btn btn-sm btn-outline-danger ml-1"
                                               onclick="return confirm('Are you sure you want to cancel this booking?')">
                                                Cancel
                                            </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        {% if is_paginated %}
                        <nav aria-label="Bookings pagination">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1">First</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                                </li>
                                {% endif %}
                                
                                <li class="page-item active">
                                    <span class="page-link">
                                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>
                                
                                {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last</a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                            <h5>No bookings yet</h5>
                            <p class="text-muted">You haven't made any bookings yet. Browse our services to get started.</p>
                            <a href="{% url 'home_app:home' %}" class="btn btn-primary">
                                Browse Services
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
