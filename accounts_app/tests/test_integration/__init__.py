"""
Integration tests for accounts_app authentication workflows.

This package contains comprehensive integration tests that cover the complete
authentication and user management workflows, organized by functionality.

Test Structure:
- auth_workflows/: Basic authentication flows (login, logout, registration)
- security_features/: Security-related tests (lockout, failed attempts, etc.)
- email_verification/: Email verification workflows
- password_management/: Password reset, change, and validation
- user_roles/: Role-specific integration tests
- cross_app/: Cross-app integration tests
- fixtures/: Reusable test fixtures and utilities

Each test module focuses on complete user journeys and real-world scenarios,
ensuring the authentication system works correctly end-to-end.
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from accounts_app.models import CustomerProfile, ServiceProviderProfile
from accounts_app.constants import UserRoles, UserStatus

User = get_user_model()

# Common test mixins and utilities
class BaseIntegrationTest(TestCase):
    """Base class for integration tests with common setup and utilities."""
    
    def setUp(self):
        """Set up common test data."""
        self.client.logout()  # Ensure clean state
        
    def create_test_user(self, role=UserRoles.CUSTOMER, **kwargs):
        """Create a test user with the specified role."""
        defaults = {
            'email': f'test_{role}@example.com',
            'password': 'TestPass123!',
            'role': role,
            'status': UserStatus.ACTIVE,
            'email_verified': True,
        }
        defaults.update(kwargs)
        
        user = User.objects.create_user(**defaults)
        
        # Create appropriate profile
        if role == UserRoles.CUSTOMER:
            CustomerProfile.objects.create(user=user)
        elif role == UserRoles.SERVICE_PROVIDER:
            ServiceProviderProfile.objects.create(
                user=user,
                legal_name='Test Business',
                contact_name='Test Contact',
                address='123 Test St',
                city='Test City',
                state='CA',
                zip_code='90210',
                phone='+***********'
            )
        
        return user
    
    def login_user(self, user, password='TestPass123!'):
        """Helper method to login a user."""
        return self.client.login(email=user.email, password=password)
    
    def assert_user_logged_in(self, user):
        """Assert that a user is logged in."""
        self.assertEqual(str(user.pk), self.client.session['_auth_user_id'])
    
    def assert_user_not_logged_in(self):
        """Assert that no user is logged in."""
        self.assertNotIn('_auth_user_id', self.client.session)
