# Django Settings Documentation

## Overview

CozyWish uses a modular settings structure with environment-specific configurations:

- `base.py` - Shared settings across all environments
- `development.py` - Development-specific settings
- `production.py` - Production-specific settings  
- `testing.py` - Test-specific settings
- `validation.py` - Settings validation utilities

## Environment Configuration

### Environment Variables

Set the `DJANGO_ENVIRONMENT` variable to control which settings are loaded:

```bash
# Development (default)
export DJANGO_ENVIRONMENT=development

# Production
export DJANGO_ENVIRONMENT=production

# Testing
export DJANGO_ENVIRONMENT=testing
```

### Required Environment Variables

#### All Environments
- `SECRET_KEY` - Django secret key for cryptographic signing

#### Production
- `DATABASE_URL` - Database connection string
- `DEBUG=false` - Disable debug mode

#### Optional (Production Recommended)
- `AWS_ACCESS_KEY_ID` - AWS S3 access key
- `AWS_SECRET_ACCESS_KEY` - AWS S3 secret key
- `AWS_STORAGE_BUCKET_NAME` - S3 bucket name
- `EMAIL_HOST_PASSWORD` - SendGrid API key

## Settings Structure

### Base Settings (`base.py`)

Contains shared configuration used across all environments:

- **Core Configuration**: Secret key, app metadata
- **Installed Apps**: Django and custom applications
- **Middleware**: Security and functionality middleware
- **Templates**: Template engine configuration
- **Database**: Default database configuration
- **Authentication**: User model and password validation
- **Internationalization**: Language and timezone settings
- **Static Files**: Static file serving configuration
- **Cache**: Caching backend configuration
- **Forms**: Crispy forms configuration

### Development Settings (`development.py`)

Development-specific overrides:

- `DEBUG = True`
- Local database (SQLite)
- Console email backend
- Disabled security headers
- Debug toolbar integration
- Local file storage

### Production Settings (`production.py`)

Production-specific overrides:

- `DEBUG = False`
- PostgreSQL database
- SendGrid email backend
- AWS S3 file storage
- Security headers enabled
- SSL/HTTPS enforcement
- Performance optimizations

### Testing Settings (`testing.py`)

Test-specific overrides:

- In-memory SQLite database
- Disabled logging
- Dummy cache backend
- Dummy email backend
- Fast password hashing (MD5)

## Security Configuration

### Development
- Security headers disabled for development tools
- HTTP allowed
- Debug mode enabled

### Production
- HSTS headers enabled
- SSL redirect enforced
- Secure cookies enabled
- XSS protection enabled
- Content type sniffing disabled

## Database Configuration

### Development
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}
```

### Production
```python
DATABASES = {
    'default': dj_database_url.parse(DATABASE_URL, conn_max_age=600)
}
```

### Testing
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }
}
```

## File Storage

### Development
- Local file system storage
- Media files served by Django

### Production
- AWS S3 storage for media files
- WhiteNoise for static files
- CDN-ready configuration

## Email Configuration

### Development
- Console backend (prints to console)
- Optional SendGrid integration for testing

### Production
- SendGrid SMTP backend
- Proper error handling
- Timeout configuration

## Caching

### Development
- Local memory cache
- 5-minute timeout
- 1000 max entries

### Production
- Redis recommended (not configured)
- Longer timeouts for performance

## Logging

Comprehensive logging configuration with:

- **Formatters**: Verbose, simple, security, audit, performance, error
- **Handlers**: Console, file, security-specific
- **Loggers**: App-specific loggers with different levels
- **Environment-specific**: Different configurations per environment

## Validation

The `validation.py` module provides:

- Settings validation on startup
- Environment variable validation
- Production security checks
- Required configuration validation

## Best Practices

1. **Never commit secrets** - Use environment variables
2. **Use different settings per environment** - Separate dev/prod configs
3. **Validate settings** - Use validation utilities
4. **Document configuration** - Keep this documentation updated
5. **Use secure defaults** - Production-ready defaults
6. **Test all environments** - Ensure settings work in all contexts

## Troubleshooting

### Common Issues

1. **Missing SECRET_KEY**: Set environment variable
2. **Database connection errors**: Check DATABASE_URL format
3. **Static files not loading**: Run `collectstatic`
4. **Email not sending**: Check SendGrid credentials
5. **S3 upload failures**: Verify AWS credentials

### Debug Commands

```bash
# Validate settings
python manage.py check --deploy

# Check environment variables
python -c "from django.conf import settings; print(settings.DEBUG)"

# Test database connection
python manage.py dbshell

# Test email configuration
python manage.py shell -c "from django.core.mail import send_mail; send_mail('Test', 'Test message', '<EMAIL>', ['<EMAIL>'])"
``` 