# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Crispy Forms Imports ---
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field, Div, HTML, Submit, Row, Column
from crispy_forms.bootstrap import FormActions

# --- Third-Party Imports (Optional Utils) ---
try:
    from utils.forms import ImageUploadForm
    from utils.image_service import ImageService
except ImportError:
    ImageUploadForm = None  # type: ignore
    ImageService = None     # type: ignore

# --- Local App Imports ---
from ..utils.logging import log_error
from ..models import TeamMember
from .common import AccessibleFormMixin
from ..mixins import ImageValidationMixin


# --- Team Member Form ---
class TeamMemberForm(AccessibleFormMixin, ImageValidationMixin, forms.ModelForm):
    """
    Form for adding or editing a service provider's team member.

    Features:
    - Staff name and position fields
    - Optional profile picture upload with processing
    - Active status toggle for team members
    """
    
    class Meta:
        model = TeamMember
        fields = ['name', 'position', 'photo', 'is_active']
        widgets = {
            'name': forms.TextInput(
                attrs={
                    'placeholder': _('Enter team member name'),
                    'required': True,
                    'maxlength': '100',
                    'minlength': '2',
                }
            ),
            'position': forms.TextInput(
                attrs={
                    'placeholder': _('e.g., Massage Therapist, Esthetician'),
                    'required': True,
                    'maxlength': '100',
                    'minlength': '2',
                }
            ),
            'photo': forms.FileInput(
                attrs={
                    'accept': 'image/jpeg,image/png,image/webp',
                }
            ),
            'is_active': forms.CheckboxInput(),
        }
        labels = {
            'name': _('Team Member Name'),
            'position': _('Position/Title'),
            'photo': _('Profile Picture'),
            'is_active': _('Active'),
        }
        help_texts = {
            'photo': _(
                'Upload team member photo (JPG, PNG, or WebP). '
                'Recommended size: 400x400 pixels. Max size: 5MB.'
            ),
            'is_active': _('Uncheck to deactivate this team member'),
            'name': _('Full name of the team member'),
            'position': _('Job title or role within your business'),
        }
        error_messages = {
            'name': {
                'required': _('Please provide the team member\'s name.'),
                'max_length': _('Name is too long. Please keep it under 100 characters.'),
            },
            'position': {
                'required': _('Please specify the team member\'s position.'),
                'max_length': _('Position is too long. Please keep it under 100 characters.'),
            },
            'photo': {
                'invalid_image': _('Please upload a valid image file.'),
            },
        }

    def __init__(self, *args, **kwargs):
        """Initialize form with enhanced validation and styling."""
        super().__init__(*args, **kwargs)
        
        # Set initial value for is_active if creating a new team member
        if not self.instance.pk:
            self.fields['is_active'].initial = True
        
        # Configure crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        self.helper.form_enctype = 'multipart/form-data'
        self.helper.layout = Layout(
            Row(
                Column(
                    Field('name', css_class='form-control'),
                    css_class='col-md-6'
                ),
                Column(
                    Field('position', css_class='form-control'),
                    css_class='col-md-6'
                ),
                css_class='mb-3'
            ),
            Div(
                Field('photo', css_class='form-control'),
                css_class='mb-3'
            ),
            Div(
                Field('is_active', css_class='form-check-input'),
                css_class='form-check mb-3'
            ),
            FormActions(
                Submit('submit', _('Save Team Member'), css_class='btn-primary'),
                css_class='d-grid gap-2'
            )
        )

    def clean_name(self):
        """Validate team member name."""
        name = self.cleaned_data.get('name', '').strip()
        
        if not name:
            raise ValidationError(
                _('Team member name is required.'),
                code='required'
            )
        
        if len(name) < 2:
            raise ValidationError(
                _('Name must be at least 2 characters long.'),
                code='too_short'
            )
        
        # Check for potentially harmful characters
        if any(char in name for char in ['<', '>', '&', '"', "'"]):
            raise ValidationError(
                _('Name contains invalid characters. Please use only letters, numbers, spaces, and basic punctuation.'),
                code='invalid_characters'
            )
        
        return name

    def clean_position(self):
        """Validate team member position."""
        position = self.cleaned_data.get('position', '').strip()
        
        if not position:
            raise ValidationError(
                _('Position/title is required.'),
                code='required'
            )
        
        if len(position) < 2:
            raise ValidationError(
                _('Position must be at least 2 characters long.'),
                code='too_short'
            )
        
        # Check for potentially harmful characters
        if any(char in position for char in ['<', '>', '&', '"', "'"]):
            raise ValidationError(
                _('Position contains invalid characters. Please use only letters, numbers, spaces, and basic punctuation.'),
                code='invalid_characters'
            )
        
        return position

    def clean_photo(self):
        """
        Validate and optionally process team member profile picture upload.

        Returns:
            The validated image file or None if not provided.

        Raises:
            ValidationError: On invalid or unsupported image.
        """
        photo = self.cleaned_data.get('photo')
        if not photo:
            return None

        # Skip validation for existing images (when editing)
        if hasattr(photo, 'url') and not hasattr(photo, 'content_type'):
            return photo

        # Validate file size
        if hasattr(photo, 'size') and photo.size > 5 * 1024 * 1024:  # 5MB
            raise ValidationError(
                _('Image file too large. Maximum size is 5MB.'),
                code='file_too_large'
            )

        # Validate file format
        if hasattr(photo, 'content_type'):
            allowed_types = ['image/jpeg', 'image/png', 'image/webp']
            if photo.content_type not in allowed_types:
                raise ValidationError(
                    _('Invalid image format. Please use JPG, PNG, or WebP only.'),
                    code='invalid_format'
                )

        # Validate file name extension
        if hasattr(photo, 'name'):
            allowed_extensions = ['.jpg', '.jpeg', '.png', '.webp']
            if not any(photo.name.lower().endswith(ext) for ext in allowed_extensions):
                raise ValidationError(
                    _('Invalid file extension. Please use .jpg, .png, or .webp files only.'),
                    code='invalid_extension'
                )

        return photo

    def clean(self):
        """Perform cross-field validation."""
        cleaned_data = super().clean()
        
        # Check if this team member name already exists for this service provider
        # (only when creating new or changing name)
        name = cleaned_data.get('name')
        if name and hasattr(self, 'instance') and hasattr(self.instance, 'service_provider'):
            service_provider = self.instance.service_provider
            existing_members = TeamMember.objects.filter(
                service_provider=service_provider,
                name__iexact=name.strip()
            )
            
            # Exclude current instance when editing
            if self.instance.pk:
                existing_members = existing_members.exclude(pk=self.instance.pk)
            
            if existing_members.exists():
                raise ValidationError(
                    _('A team member with this name already exists. Please choose a different name.'),
                    code='duplicate_name'
                )
        
        return cleaned_data

    def save(self, commit: bool = True) -> TeamMember:
        """
        Save the team member instance.

        Returns:
            The saved TeamMember instance.
        """
        team_member = super().save(commit=commit)
        return team_member
