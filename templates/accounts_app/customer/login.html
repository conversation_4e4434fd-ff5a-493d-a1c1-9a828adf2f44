{% extends 'base.html' %}

{% block title %}Welcome Back - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Custom Login Page Styles -->
<style>
    /* Login page specific styles */
    .login-gradient {
        background: linear-gradient(135deg, #F8F9FA 0%, #FFF9F4 50%, #FAE1D7 100%);
        min-height: 100vh;
    }
    
    .login-card {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(250, 225, 215, 0.3);
    }
    
    .brand-icon {
        background: linear-gradient(135deg, #43251B 0%, #2d1912 100%);
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }
    
    .form-control-enhanced {
        transition: all 0.3s ease;
    }
    
    .form-control-enhanced:focus {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(67, 37, 27, 0.15);
    }
    
    .social-btn {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.3s ease;
    }
    
    .social-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(67, 37, 27, 0.15);
    }
    
    .divider-enhanced {
        position: relative;
        text-align: center;
        margin: 2rem 0;
    }
    
    .divider-enhanced::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(to right, transparent, #FAE1D7, transparent);
    }
    
    .divider-enhanced span {
        background: white;
        padding: 0 1rem;
        color: #43251B;
        font-weight: 500;
    }
</style>
{% endblock %}

{% block content %}
<div class="login-gradient flex items-center justify-center px-4 py-8">
    <div class="w-full max-w-md">
        <!-- Login Card -->
        <div class="login-card card shadow-xl">
            <div class="card-body">
                <!-- Brand Header -->
                <div class="text-center mb-8">
                    <div class="brand-icon w-20 h-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                        <i class="fas fa-user text-2xl text-white"></i>
                    </div>
                    <h1 class="text-3xl font-bold text-primary font-poppins">Welcome Back</h1>
                    <p class="text-neutral/70 mt-2">Sign in to your CozyWish account</p>
                </div>

                <!-- Messages -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags|default:'info' }} mb-4">
                            <i class="fas fa-info-circle mr-2"></i>
                            <span>{{ message }}</span>
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- Form Errors -->
                {% if form.non_field_errors %}
                    <div class="alert alert-error mb-4">
                        {% for error in form.non_field_errors %}
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            <span>{{ error }}</span>
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Login Form -->
                <form method="post" class="space-y-6">
                    {% csrf_token %}

                    <!-- Email Field -->
                    <div class="form-control">
                        <label class="label" for="{{ form.email.id_for_label }}">
                            <span class="label-text font-medium">
                                <i class="fas fa-envelope mr-2 text-primary"></i>
                                {{ form.email.label }}
                            </span>
                        </label>
                        {% if form.email.errors %}
                            {{ form.email|add_class:"input input-bordered input-error w-full form-control-enhanced" }}
                        {% else %}
                            {{ form.email|add_class:"input input-bordered w-full form-control-enhanced" }}
                        {% endif %}
                        {% if form.email.errors %}
                            <label class="label">
                                <span class="label-text-alt text-error">
                                    {% for error in form.email.errors %}
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                    {% endfor %}
                                </span>
                            </label>
                        {% endif %}
                    </div>

                    <!-- Password Field -->
                    <div class="form-control">
                        <label class="label" for="{{ form.password.id_for_label }}">
                            <span class="label-text font-medium">
                                <i class="fas fa-lock mr-2 text-primary"></i>
                                {{ form.password.label }}
                            </span>
                        </label>
                        <div class="relative">
                            {% if form.password.errors %}
                                {{ form.password|add_class:"input input-bordered input-error w-full form-control-enhanced pr-12" }}
                            {% else %}
                                {{ form.password|add_class:"input input-bordered w-full form-control-enhanced pr-12" }}
                            {% endif %}
                            <button type="button" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral/50 hover:text-primary transition-colors" onclick="togglePassword('{{ form.password.id_for_label }}')">
                                <i class="fas fa-eye" id="toggleIcon"></i>
                            </button>
                        </div>
                        {% if form.password.errors %}
                            <label class="label">
                                <span class="label-text-alt text-error">
                                    {% for error in form.password.errors %}
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                    {% endfor %}
                                </span>
                            </label>
                        {% endif %}
                    </div>

                    <!-- Remember Me & Forgot Password -->
                    <div class="flex items-center justify-between">
                        <div class="form-control">
                            <label class="label cursor-pointer">
                                <input type="checkbox" class="checkbox checkbox-primary checkbox-sm mr-2" name="remember" id="remember">
                                <span class="label-text text-sm">Remember me</span>
                            </label>
                        </div>
                        <a href="{% url 'accounts_app:customer_password_reset' %}" class="link link-primary text-sm hover:text-primary/80">
                            Forgot password?
                        </a>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" class="btn btn-primary w-full">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Sign In
                    </button>
                </form>

                <!-- Divider -->
                <div class="divider-enhanced">
                    <span>or continue with</span>
                </div>

                <!-- Social Login Buttons -->
                <div class="flex justify-center space-x-4 mb-6">
                    <button class="social-btn btn btn-outline btn-circle" disabled>
                        <i class="fab fa-google text-red-500"></i>
                    </button>
                    <button class="social-btn btn btn-outline btn-circle" disabled>
                        <i class="fab fa-apple text-gray-800"></i>
                    </button>
                    <button class="social-btn btn btn-outline btn-circle" disabled>
                        <i class="fab fa-facebook-f text-blue-600"></i>
                    </button>
                </div>
                
                <div class="text-center text-sm text-neutral/60 mb-6">
                    <i class="fas fa-clock mr-1"></i>
                    Social login coming soon
                </div>

                <!-- Action Links -->
                <div class="space-y-4">
                    <div class="text-center">
                        <p class="text-sm text-neutral/70 mb-2">Don't have an account?</p>
                        <a href="{% url 'accounts_app:customer_signup' %}" class="btn btn-outline btn-secondary w-full">
                            <i class="fas fa-user-plus mr-2"></i>
                            Create Account
                        </a>
                    </div>
                    
                    <div class="divider">OR</div>
                    
                    <div class="text-center">
                        <p class="text-sm text-neutral/70 mb-2">Are you a service provider?</p>
                        <a href="{% url 'accounts_app:for_business' %}" class="btn btn-outline btn-primary w-full">
                            <i class="fas fa-store mr-2"></i>
                            For Business
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Text -->
        <div class="text-center mt-8 text-sm text-neutral/60">
            <p>© 2024 CozyWish. All rights reserved.</p>
        </div>
    </div>
</div>

<!-- JavaScript for Password Toggle -->
<script>
function togglePassword(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const toggleIcon = document.getElementById('toggleIcon');
    
    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Form validation feedback
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input[required]');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value.trim() === '') {
                this.classList.add('input-error');
            } else {
                this.classList.remove('input-error');
            }
        });
    });
});
</script>
{% endblock %}
