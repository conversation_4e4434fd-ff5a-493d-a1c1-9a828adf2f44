{% extends 'base.html' %}

{% block title %}Join <PERSON> as a Service Provider{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<link rel="stylesheet" href="{% static 'css/accounts_app/provider_signup.css' %}">
{% endblock %}

{% block content %}
<section class="signup-section">
    <div class="signup-container">
        <div class="signup-card">
            <div class="signup-header">
                <div class="content">
                    <div class="signup-icon">
                        <i class="fas fa-store"></i>
                    </div>
                    <h1 class="signup-title">Join <PERSON><PERSON>Wish for Business</h1>
                    <p class="signup-subtitle">Create your business account and start reaching new customers</p>
                </div>
            </div>

            <div class="signup-body">
            <!-- Display messages -->
            {% if messages %}
              {% for message in messages %}
                <div class="alert-cw alert-cw-{{ message.tags }} mb-4" role="alert">
                  <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
                  {{ message }}
                </div>
              {% endfor %}
            {% endif %}

            <form method="post">
              {% csrf_token %}

              {% if form.non_field_errors %}
              <div class="alert-cw alert-cw-error mb-4">
                {% for error in form.non_field_errors %}
                <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                {% endfor %}
              </div>
              {% endif %}

              <!-- Account Information Section -->
              <div class="form-section">
                <h5 class="form-section-title">
                  <i class="fas fa-user-circle"></i>Account Information
                </h5>

                <!-- Email field -->
                <div class="form-floating mb-4">
                  {% if form.email.errors %}
                    {{ form.email|add_class:"form-control-cw is-invalid"|attr:"placeholder:<EMAIL>"|attr:"aria-describedby:email_help" }}
                  {% else %}
                    {{ form.email|add_class:"form-control-cw"|attr:"placeholder:<EMAIL>"|attr:"aria-describedby:email_help" }}
                  {% endif %}
                  <label for="{{ form.email.id_for_label }}">
                    <i class="fas fa-envelope"></i>{{ form.email.label }}
                  </label>
                  {% if form.email.errors %}
                  <div class="invalid-feedback" role="alert" aria-live="polite">
                    {% for error in form.email.errors %}
                    <i class="fas fa-exclamation-circle"></i>{{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                  {% if form.email.help_text %}
                  <div class="form-text" id="email_help">{{ form.email.help_text }}</div>
                  {% endif %}
                </div>

                <!-- Password Row -->
                <div class="form-row">
                  <!-- Password field -->
                  <div>
                    <label for="{{ form.password1.id_for_label }}" class="form-label">
                      <i class="fas fa-lock"></i>{{ form.password1.label }}
                    </label>
                    <div class="input-group">
                      {% if form.password1.errors %}
                        {{ form.password1|add_class:"form-control-cw is-invalid"|attr:"placeholder:Create a strong password" }}
                      {% else %}
                        {{ form.password1|add_class:"form-control-cw"|attr:"placeholder:Create a strong password" }}
                      {% endif %}
                      <button type="button" class="toggle-password" data-target="#{{ form.password1.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                        <i class="fas fa-eye" aria-hidden="true"></i>
                      </button>
                    </div>
                    {% if form.password1.errors %}
                    <div class="invalid-feedback" role="alert" aria-live="polite">
                      {% for error in form.password1.errors %}
                      <i class="fas fa-exclamation-circle"></i>{{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                    {% if form.password1.help_text %}
                    <div class="form-text">{{ form.password1.help_text }}</div>
                    {% endif %}
                  </div>

                  <!-- Confirm Password field -->
                  <div>
                    <label for="{{ form.password2.id_for_label }}" class="form-label">
                      <i class="fas fa-lock"></i>{{ form.password2.label }}
                    </label>
                    <div class="input-group">
                      {% if form.password2.errors %}
                        {{ form.password2|add_class:"form-control-cw is-invalid"|attr:"placeholder:Confirm your password" }}
                      {% else %}
                        {{ form.password2|add_class:"form-control-cw"|attr:"placeholder:Confirm your password" }}
                      {% endif %}
                      <button type="button" class="toggle-password" data-target="#{{ form.password2.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                        <i class="fas fa-eye" aria-hidden="true"></i>
                      </button>
                    </div>
                    {% if form.password2.errors %}
                    <div class="invalid-feedback" role="alert" aria-live="polite">
                      {% for error in form.password2.errors %}
                      <i class="fas fa-exclamation-circle"></i>{{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                    {% if form.password2.help_text %}
                    <div class="form-text">{{ form.password2.help_text }}</div>
                    {% endif %}
                  </div>
                </div>
              </div>

              <!-- Business Information Section -->
              <div class="form-section">
                <h5 class="form-section-title">
                  <i class="fas fa-building"></i>Business Information
                </h5>

                <!-- Business Name -->
                <div class="form-floating mb-4">
                  {% if form.business_name.errors %}
                    {{ form.business_name|add_class:"form-control-cw is-invalid"|attr:"placeholder:Business Name" }}
                  {% else %}
                    {{ form.business_name|add_class:"form-control-cw"|attr:"placeholder:Business Name" }}
                  {% endif %}
                  <label for="{{ form.business_name.id_for_label }}">
                    <i class="fas fa-building"></i>{{ form.business_name.label }}
                  </label>
                  {% if form.business_name.errors %}
                  <div class="invalid-feedback" role="alert" aria-live="polite">
                    {% for error in form.business_name.errors %}
                    <i class="fas fa-exclamation-circle"></i>{{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                  {% if form.business_name.help_text %}
                  <div class="form-text">{{ form.business_name.help_text }}</div>
                  {% endif %}
                </div>

                <!-- Business Contact Row -->
                <div class="form-row">
                  <!-- Business Phone -->
                  <div class="form-floating">
                    {% if form.business_phone_number.errors %}
                      {{ form.business_phone_number|add_class:"form-control-cw is-invalid"|attr:"placeholder:Phone Number" }}
                    {% else %}
                      {{ form.business_phone_number|add_class:"form-control-cw"|attr:"placeholder:Phone Number" }}
                    {% endif %}
                    <label for="{{ form.business_phone_number.id_for_label }}">
                      <i class="fas fa-phone"></i>{{ form.business_phone_number.label }}
                    </label>
                    {% if form.business_phone_number.errors %}
                    <div class="invalid-feedback">
                      {% for error in form.business_phone_number.errors %}
                      <i class="fas fa-exclamation-circle"></i>{{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                    {% if form.business_phone_number.help_text %}
                    <div class="form-text">{{ form.business_phone_number.help_text }}</div>
                    {% endif %}
                  </div>

                  <!-- Contact Person Name -->
                  <div class="form-floating">
                    {% if form.contact_person_name.errors %}
                      {{ form.contact_person_name|add_class:"form-control-cw is-invalid"|attr:"placeholder:Contact Person Name" }}
                    {% else %}
                      {{ form.contact_person_name|add_class:"form-control-cw"|attr:"placeholder:Contact Person Name" }}
                    {% endif %}
                    <label for="{{ form.contact_person_name.id_for_label }}">
                      <i class="fas fa-user-tie"></i>{{ form.contact_person_name.label }}
                    </label>
                    {% if form.contact_person_name.errors %}
                    <div class="invalid-feedback">
                      {% for error in form.contact_person_name.errors %}
                      <i class="fas fa-exclamation-circle"></i>{{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                    {% if form.contact_person_name.help_text %}
                    <div class="form-text">{{ form.contact_person_name.help_text }}</div>
                    {% endif %}
                  </div>
                </div>
              </div>

              <!-- Business Address Section -->
              <div class="form-section">
                <h5 class="form-section-title">
                  <i class="fas fa-map-marker-alt"></i>Business Address
                </h5>

                <!-- Street Address -->
                <div class="form-floating mb-4">
                  {% if form.business_address.errors %}
                    {{ form.business_address|add_class:"form-control-cw is-invalid"|attr:"placeholder:Street Address" }}
                  {% else %}
                    {{ form.business_address|add_class:"form-control-cw"|attr:"placeholder:Street Address" }}
                  {% endif %}
                  <label for="{{ form.business_address.id_for_label }}">
                    <i class="fas fa-map-marker-alt"></i>{{ form.business_address.label }}
                  </label>
                  {% if form.business_address.errors %}
                  <div class="invalid-feedback">
                    {% for error in form.business_address.errors %}
                    <i class="fas fa-exclamation-circle"></i>{{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                </div>

                <!-- City, State, ZIP Row -->
                <div class="row mb-4">
                  <div class="col-md-4">
                    <div class="form-floating">
                      {% if form.city.errors %}
                        {{ form.city|add_class:"form-control-cw is-invalid"|attr:"placeholder:City" }}
                      {% else %}
                        {{ form.city|add_class:"form-control-cw"|attr:"placeholder:City" }}
                      {% endif %}
                      <label for="{{ form.city.id_for_label }}">{{ form.city.label }}</label>
                      {% if form.city.errors %}
                      <div class="invalid-feedback">
                        {% for error in form.city.errors %}
                        <i class="fas fa-exclamation-circle"></i>{{ error }}
                        {% endfor %}
                      </div>
                      {% endif %}
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-floating">
                      {% if form.state.errors %}
                        {{ form.state|add_class:"form-control-cw is-invalid" }}
                      {% else %}
                        {{ form.state|add_class:"form-control-cw" }}
                      {% endif %}
                      <label for="{{ form.state.id_for_label }}">{{ form.state.label }}</label>
                      {% if form.state.errors %}
                      <div class="invalid-feedback">
                        {% for error in form.state.errors %}
                        <i class="fas fa-exclamation-circle"></i>{{ error }}
                        {% endfor %}
                      </div>
                      {% endif %}
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-floating">
                      {% if form.zip_code.errors %}
                        {{ form.zip_code|add_class:"form-control-cw is-invalid"|attr:"placeholder:ZIP Code" }}
                      {% else %}
                        {{ form.zip_code|add_class:"form-control-cw"|attr:"placeholder:ZIP Code" }}
                      {% endif %}
                      <label for="{{ form.zip_code.id_for_label }}">{{ form.zip_code.label }}</label>
                      {% if form.zip_code.errors %}
                      <div class="invalid-feedback">
                        {% for error in form.zip_code.errors %}
                        <i class="fas fa-exclamation-circle"></i>{{ error }}
                        {% endfor %}
                      </div>
                      {% endif %}
                    </div>
                  </div>
                </div>

                <!-- EIN -->
                <div class="form-floating">
                  {% if form.ein.errors %}
                    {{ form.ein|add_class:"form-control-cw is-invalid"|attr:"placeholder:EIN" }}
                  {% else %}
                    {{ form.ein|add_class:"form-control-cw"|attr:"placeholder:EIN" }}
                  {% endif %}
                  <label for="{{ form.ein.id_for_label }}">
                    <i class="fas fa-id-card"></i>{{ form.ein.label }}
                  </label>
                  {% if form.ein.errors %}
                  <div class="invalid-feedback">
                    {% for error in form.ein.errors %}
                    <i class="fas fa-exclamation-circle"></i>{{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                  {% if form.ein.help_text %}
                  <div class="form-text">{{ form.ein.help_text }}</div>
                  {% endif %}
                </div>
              </div>

              <!-- Submit Section -->
              <div class="form-section">
                <div class="d-grid mb-4">
                  <button type="submit" class="btn-cw-primary">
                    <i class="fas fa-user-plus me-2"></i>Create Business Account
                  </button>
                </div>

                <!-- Login link -->
                <div class="text-center">
                  <p class="mb-0 text-muted-cw">Already have an account?
                    <a href="{% url 'accounts_app:service_provider_login' %}">Sign in here</a>
                  </p>
                </div>
              </div>
            </form>
            </div>
        </div>
    </div>
</section>

<!-- Password Toggle JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    // Password toggle functionality
    function togglePassword(toggleBtn) {
        const targetSelector = toggleBtn.getAttribute('data-target');
        if (!targetSelector) {
            console.error('No data-target attribute found on toggle button');
            return;
        }

        const input = document.querySelector(targetSelector);
        if (!input) {
            console.error('Target input not found:', targetSelector);
            return;
        }

        // Toggle password visibility
        const isPassword = input.type === 'password';
        input.type = isPassword ? 'text' : 'password';

        // Update icon
        const icon = toggleBtn.querySelector('i');
        if (icon) {
            if (isPassword) {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Update accessibility attributes
        const newTitle = isPassword ? 'Hide password' : 'Show password';
        toggleBtn.title = newTitle;
        toggleBtn.setAttribute('aria-label', newTitle);
    }

    // Click event handler
    document.addEventListener('click', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn) {
            e.preventDefault();
            e.stopPropagation();
            togglePassword(toggleBtn);
        }
    });

    // Keyboard event handler for accessibility
    document.addEventListener('keydown', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn && (e.key === 'Enter' || e.key === ' ' || e.key === 'Spacebar')) {
            e.preventDefault();
            e.stopPropagation();
            togglePassword(toggleBtn);
        }
    });

    // Initialize toggle buttons
    const toggleButtons = document.querySelectorAll('.toggle-password');
    toggleButtons.forEach(function(btn) {
        // Ensure button has proper attributes
        if (!btn.hasAttribute('tabindex')) {
            btn.setAttribute('tabindex', '0');
        }
        
        // Add role for screen readers
        btn.setAttribute('role', 'button');
        
        // Ensure aria-label is set
        if (!btn.hasAttribute('aria-label')) {
            btn.setAttribute('aria-label', 'Toggle password visibility');
        }
    });
});
</script>
{% endblock %}
