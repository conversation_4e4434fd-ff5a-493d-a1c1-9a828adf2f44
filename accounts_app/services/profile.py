"""
Profile service for accounts_app.

This module contains the ProfileService class for handling
user profile operations.
"""

import logging
from typing import Dict, <PERSON><PERSON>, Any
from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils.translation import gettext_lazy as _

# Local imports
from ..models import CustomUser, CustomerProfile, ServiceProviderProfile
from ..utils.logging import log_profile_change


logger = logging.getLogger(__name__)


class ProfileService:
    """
    Service for handling user profile operations.
    
    Provides methods for profile creation, updates, and management
    with proper validation and logging.
    """
    
    @staticmethod
    def update_customer_profile(user: CustomUser, profile_data: Dict[str, Any], request=None) -> Tuple[bool, str]:
        """
        Update customer profile information.
        
        Args:
            user: User object
            profile_data: Dictionary containing profile update data
            request: HTTP request object for logging
            
        Returns:
            Tuple of (success, message)
        """
        try:
            with transaction.atomic():
                profile, created = CustomerProfile.objects.get_or_create(user=user)
                
                # Track changes for logging
                changed_fields = {}
                
                # Update profile fields
                for field, value in profile_data.items():
                    if hasattr(profile, field):
                        old_value = getattr(profile, field)
                        if old_value != value:
                            setattr(profile, field, value)
                            changed_fields[field] = {'old': old_value, 'new': value}
                
                # Validate and save
                profile.full_clean()
                profile.save()
                
                # Log profile changes
                if changed_fields:
                    log_profile_change(
                        user=user,
                        profile_type='customer',
                        changed_fields=changed_fields,
                        request=request
                    )
                
                logger.info(f"Customer profile updated: {user.email}, fields: {list(changed_fields.keys())}")
                return True, _("Profile updated successfully")
                
        except ValidationError as e:
            logger.error(f"Customer profile update validation error: {str(e)}")
            return False, _("Profile data is invalid")
        except Exception as e:
            logger.error(f"Customer profile update error: {str(e)}", exc_info=True)
            return False, _("Profile update failed. Please try again.")
    
    @staticmethod
    def update_service_provider_profile(user: CustomUser, profile_data: Dict[str, Any], request=None) -> Tuple[bool, str]:
        """
        Update service provider profile information.
        
        Args:
            user: User object
            profile_data: Dictionary containing profile update data
            request: HTTP request object for logging
            
        Returns:
            Tuple of (success, message)
        """
        try:
            with transaction.atomic():
                profile, created = ServiceProviderProfile.objects.get_or_create(user=user)
                
                # Track changes for logging
                changed_fields = {}
                
                # Update profile fields
                for field, value in profile_data.items():
                    if hasattr(profile, field):
                        old_value = getattr(profile, field)
                        if old_value != value:
                            setattr(profile, field, value)
                            changed_fields[field] = {'old': old_value, 'new': value}
                
                # Validate and save
                profile.full_clean()
                profile.save()
                
                # Log profile changes
                if changed_fields:
                    log_profile_change(
                        user=user,
                        profile_type='service_provider',
                        changed_fields=changed_fields,
                        request=request
                    )
                
                logger.info(f"Service provider profile updated: {user.email}, fields: {list(changed_fields.keys())}")
                return True, _("Profile updated successfully")
                
        except ValidationError as e:
            logger.error(f"Service provider profile update validation error: {str(e)}")
            return False, _("Profile data is invalid")
        except Exception as e:
            logger.error(f"Service provider profile update error: {str(e)}", exc_info=True)
            return False, _("Profile update failed. Please try again.") 