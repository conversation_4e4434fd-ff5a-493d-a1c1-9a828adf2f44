"""
AJAX Views for accounts_app.

This module provides AJAX endpoints for profile updates with proper
validation, error handling, and JSON responses.
"""

import json
import logging
from django.contrib.auth.decorators import login_required
from django.core.exceptions import ValidationError
from django.db import transaction
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.views.generic import View

# Local imports
from ..decorators import CustomerRequiredMixin, ServiceProviderRequiredMixin
from ..forms import CustomerProfileForm, ServiceProviderProfileForm
from ..utils.logging import log_profile_change, log_error
from ..models import CustomUser, CustomerProfile, ServiceProviderProfile

logger = logging.getLogger(__name__)


class AjaxResponseMixin:
    """
    Mixin for standardized AJAX JSON responses.
    """
    
    def json_response(self, data, status=200):
        """Return standardized JSON response."""
        return JsonResponse(data, status=status)
    
    def success_response(self, data=None, message=None):
        """Return success response with optional data and message."""
        response_data = {
            'success': True,
            'message': message or _('Operation completed successfully')
        }
        if data:
            response_data['data'] = data
        return self.json_response(response_data)
    
    def error_response(self, message=None, errors=None, status=400):
        """Return error response with optional field errors."""
        response_data = {
            'success': False,
            'message': message or _('An error occurred')
        }
        if errors:
            response_data['errors'] = errors
        return self.json_response(response_data, status=status)


class CustomerProfileAjaxView(CustomerRequiredMixin, AjaxResponseMixin, View):
    """
    AJAX endpoint for customer profile updates.
    
    Features:
    - Partial form validation
    - Field-specific error handling
    - Activity logging
    - Real-time validation feedback
    """
    
    def get_customer_profile(self):
        """Get or create customer profile."""
        try:
            return CustomerProfile.objects.get(user=self.request.user)
        except CustomerProfile.DoesNotExist:
            return CustomerProfile.objects.create(user=self.request.user)
    
    def post(self, request):
        """Handle AJAX profile update requests."""
        try:
            with transaction.atomic():
                profile = self.get_customer_profile()
                
                # Create form instance with current profile
                form = CustomerProfileForm(
                    data=request.POST,
                    files=request.FILES,
                    instance=profile
                )
                
                if form.is_valid():
                    # Track changed fields for logging
                    changed_fields = {}
                    if form.changed_data:
                        for field in form.changed_data:
                            changed_fields[field] = form.cleaned_data[field]
                    
                    # Save the profile
                    updated_profile = form.save()
                    
                    # Log the changes
                    if changed_fields:
                        log_profile_change(
                            user=request.user,
                            profile_type='customer',
                            changed_fields=changed_fields,
                            request=request
                        )
                    
                    # Log success
                    logger.info(
                        f"Customer profile updated via AJAX: {request.user.email}",
                        extra={
                            'user_id': request.user.id,
                            'changed_fields': list(changed_fields.keys())
                        }
                    )
                    
                    return self.success_response(
                        message=_('Profile updated successfully'),
                        data={
                            'changed_fields': list(changed_fields.keys()),
                            'profile_picture_url': updated_profile.profile_picture.url if updated_profile.profile_picture else None
                        }
                    )
                
                else:
                    # Form validation failed
                    return self.error_response(
                        message=_('Please correct the errors below'),
                        errors=form.errors
                    )
                
        except ValidationError as e:
            return self.error_response(
                message=_('Validation error'),
                errors={'__all__': [str(e)]}
            )
        except Exception as e:
            # Log the error
            log_error(
                error_type='ajax_profile_update',
                error_message="Customer profile AJAX update failed",
                user=request.user,
                request=request,
                exception=e
            )
            
            return self.error_response(
                message=_('An unexpected error occurred. Please try again.'),
                status=500
            )


class ServiceProviderProfileAjaxView(ServiceProviderRequiredMixin, AjaxResponseMixin, View):
    """
    AJAX endpoint for service provider profile updates.
    
    Features:
    - Business profile validation
    - Logo upload handling
    - Activity logging
    - Real-time validation feedback
    """
    
    def get_provider_profile(self):
        """Get or create service provider profile."""
        try:
            return ServiceProviderProfile.objects.get(user=self.request.user)
        except ServiceProviderProfile.DoesNotExist:
            return ServiceProviderProfile.objects.create(user=self.request.user)
    
    def post(self, request):
        """Handle AJAX profile update requests."""
        try:
            with transaction.atomic():
                profile = self.get_provider_profile()
                
                # Create form instance with current profile
                form = ServiceProviderProfileForm(
                    data=request.POST,
                    files=request.FILES,
                    instance=profile
                )
                
                if form.is_valid():
                    # Track changed fields for logging
                    changed_fields = {}
                    if form.changed_data:
                        for field in form.changed_data:
                            changed_fields[field] = form.cleaned_data[field]
                    
                    # Save the profile
                    updated_profile = form.save()
                    
                    # Log the changes
                    if changed_fields:
                        log_profile_change(
                            user=request.user,
                            profile_type='service_provider',
                            changed_fields=changed_fields,
                            request=request
                        )
                    
                    # Log success
                    logger.info(
                        f"Service provider profile updated via AJAX: {request.user.email}",
                        extra={
                            'user_id': request.user.id,
                            'changed_fields': list(changed_fields.keys())
                        }
                    )
                    
                    return self.success_response(
                        message=_('Profile updated successfully'),
                        data={
                            'changed_fields': list(changed_fields.keys()),
                            'logo_url': updated_profile.logo.url if updated_profile.logo else None
                        }
                    )
                
                else:
                    # Form validation failed
                    return self.error_response(
                        message=_('Please correct the errors below'),
                        errors=form.errors
                    )
                
        except ValidationError as e:
            return self.error_response(
                message=_('Validation error'),
                errors={'__all__': [str(e)]}
            )
        except Exception as e:
            # Log the error
            log_error(
                error_type='ajax_profile_update',
                error_message="Service provider profile AJAX update failed",
                user=request.user,
                request=request,
                exception=e
            )
            
            return self.error_response(
                message=_('An unexpected error occurred. Please try again.'),
                status=500
            )


@login_required
@require_http_methods(["POST"])
def validate_field_ajax(request):
    """
    AJAX endpoint for real-time field validation.
    
    This endpoint allows frontend to validate individual fields
    without submitting the entire form.
    """
    try:
        field_name = request.POST.get('field_name')
        field_value = request.POST.get('field_value')
        profile_type = request.POST.get('profile_type')  # 'customer' or 'service_provider'
        
        if not field_name or profile_type not in ['customer', 'service_provider']:
            return JsonResponse({
                'success': False,
                'message': _('Invalid request parameters')
            }, status=400)
        
        # Determine the appropriate form class
        if profile_type == 'customer':
            if not request.user.is_customer:
                return JsonResponse({
                    'success': False,
                    'message': _('Access denied')
                }, status=403)
            form_class = CustomerProfileForm
            try:
                profile = CustomerProfile.objects.get(user=request.user)
            except CustomerProfile.DoesNotExist:
                profile = CustomerProfile.objects.create(user=request.user)
        else:
            if not request.user.is_service_provider:
                return JsonResponse({
                    'success': False,
                    'message': _('Access denied')
                }, status=403)
            form_class = ServiceProviderProfileForm
            try:
                profile = ServiceProviderProfile.objects.get(user=request.user)
            except ServiceProviderProfile.DoesNotExist:
                profile = ServiceProviderProfile.objects.create(user=request.user)
        
        # Create form with only the field we want to validate
        form_data = {field_name: field_value}
        form = form_class(data=form_data, instance=profile)
        
        # Validate only the specific field
        if hasattr(form, f'clean_{field_name}'):
            try:
                # Run full clean to populate cleaned_data
                form.full_clean()
                
                # Check if there are errors for this specific field
                if field_name in form.errors:
                    return JsonResponse({
                        'success': False,
                        'errors': form.errors[field_name]
                    })
                else:
                    return JsonResponse({
                        'success': True,
                        'message': _('Field is valid')
                    })
            except ValidationError as e:
                return JsonResponse({
                    'success': False,
                    'errors': [str(e)]
                })
        else:
            # Field doesn't have specific validation, just check if it's in form errors
            form.full_clean()
            if field_name in form.errors:
                return JsonResponse({
                    'success': False,
                    'errors': form.errors[field_name]
                })
            else:
                return JsonResponse({
                    'success': True,
                    'message': _('Field is valid')
                })
    
    except Exception as e:
        logger.error(f"Field validation error: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': _('Validation error occurred')
        }, status=500) 