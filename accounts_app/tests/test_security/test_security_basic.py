"""
Basic security tests for accounts_app.
"""

import pytest
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.http import HttpResponse
from django.utils import timezone
from datetime import timedelta

from accounts_app.constants import UserRoles

User = get_user_model()


class SecurityBasicTests(TestCase):
    """Basic security functionality tests"""

    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER
        )

    def test_user_creation(self):
        """Test that users can be created"""
        self.assertIsNotNone(self.user)
        self.assertEqual(self.user.email, '<EMAIL>')
        self.assertEqual(self.user.role, UserRoles.CUSTOMER)

    def test_user_authentication(self):
        """Test user authentication"""
        self.assertTrue(self.user.check_password('testpass123'))
        self.assertFalse(self.user.check_password('wrongpassword'))

    def test_user_role_assignment(self):
        """Test user role assignment"""
        self.assertEqual(self.user.role, UserRoles.CUSTOMER)
        
        # Test role change
        self.user.role = UserRoles.SERVICE_PROVIDER
        self.user.save()
        self.assertEqual(self.user.role, UserRoles.SERVICE_PROVIDER)

    def test_user_email_uniqueness(self):
        """Test that email addresses are unique"""
        # This should raise an exception
        with self.assertRaises(Exception):
            User.objects.create_user(
                email='<EMAIL>',
                password='testpass123',
                role=UserRoles.CUSTOMER
            )


class SecurityMiddlewareBasicTests(TestCase):
    """Basic middleware tests"""

    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()

    def test_request_factory(self):
        """Test that RequestFactory works"""
        request = self.factory.get('/')
        self.assertIsNotNone(request)

    def test_http_response(self):
        """Test HTTP response creation"""
        response = HttpResponse(b"OK")
        self.assertEqual(response.status_code, 200)

    def test_ip_address_extraction(self):
        """Test IP address extraction from request"""
        request = self.factory.get('/')
        request.META['REMOTE_ADDR'] = '*************'
        
        ip = request.META.get('REMOTE_ADDR')
        self.assertEqual(ip, '*************')

    def test_forwarded_ip_extraction(self):
        """Test forwarded IP address extraction"""
        request = self.factory.get('/')
        request.META['HTTP_X_FORWARDED_FOR'] = '***********, ***********'
        request.META['REMOTE_ADDR'] = '*************'
        
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        
        self.assertEqual(ip, '***********')


class SecurityModelBasicTests(TestCase):
    """Basic security model tests"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER
        )

    def test_user_security_creation(self):
        """Test basic user security functionality"""
        # Test that user has basic security attributes
        self.assertIsNotNone(self.user.email)
        self.assertIsNotNone(self.user.password)
        self.assertIsNotNone(self.user.role)

    def test_user_str_representation(self):
        """Test user string representation"""
        self.assertIn(self.user.email, str(self.user))

    def test_user_is_active(self):
        """Test user active status"""
        self.assertTrue(self.user.is_active)

    def test_user_is_authenticated(self):
        """Test user authentication status"""
        self.assertTrue(self.user.is_authenticated)


@pytest.mark.django_db
class SecurityPytestBasicTests:
    """Basic pytest-style security tests"""

    def test_user_creation_pytest(self):
        """Test user creation with pytest"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER
        )
        assert user.email == '<EMAIL>'
        assert user.role == UserRoles.CUSTOMER

    def test_user_authentication_pytest(self):
        """Test user authentication with pytest"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER
        )
        assert user.check_password('testpass123') is True
        assert user.check_password('wrongpass') is False

    def test_request_factory_pytest(self, rf):
        """Test request factory with pytest"""
        request = rf.get('/')
        assert request is not None
        assert request.method == 'GET'

    def test_http_response_pytest(self):
        """Test HTTP response with pytest"""
        response = HttpResponse(b"OK")
        assert response.status_code == 200
        assert response.content == b"OK" 