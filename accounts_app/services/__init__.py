"""
Service layer for accounts_app.

This module provides a unified interface to all account-related services,
including authentication, registration, profile management, and more.
"""

# Import all services to make them available from the services package
from .auth import AuthenticationService, AuthenticationError
from .registration import UserRegistrationService
from .profile import ProfileService
from .password import PasswordService
from .account import AccountService
from .email import EmailService, EmailVerificationService
from .team import TeamService
from .security import SecurityService
from .gdpr import GDPRService

# Import utility functions and exceptions
from .auth import get_client_ip

# Import base exception classes that might be used across the codebase
class ServiceError(Exception):
    """Base exception for service layer errors"""
    pass

class ProfileError(ServiceError):
    """Profile-related errors"""
    pass

class SecurityError(ServiceError):
    """Security-related errors"""
    pass

__all__ = [
    'AuthenticationService',
    'AuthenticationError',
    'UserRegistrationService',
    'ProfileService',
    'PasswordService',
    'AccountService',
    'EmailService',
    'EmailVerificationService',
    'TeamService',
    'SecurityService',
    'GDPRService',
    'get_client_ip',
    'ServiceError',
    'ProfileError',
    'SecurityError',
] 