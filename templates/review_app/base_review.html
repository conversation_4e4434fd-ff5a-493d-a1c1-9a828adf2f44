{% extends 'base.html' %}

{% block extra_css %}
    {% load static %}
    {% load widget_tweaks %}

    <!-- Preconnect for Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Review App CSS -->
    <link href="{% static 'css/review_app/base_review.css' %}" rel="stylesheet">
{% endblock %}

    {% block review_extra_css %}{% endblock %}
{% endblock %}

{% block content %}
<div class="review-wrapper">
    <div class="d-flex flex-column min-vh-100">
        <main class="flex-grow-1 d-flex align-items-center justify-content-center p-3">
            <div class="card w-100 review-card">
                <div class="card-body p-4">
                    {% block review_content %}{% endblock %}
                </div>
            </div>
        </main>
        {% block below_card %}{% endblock %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
    {% block review_extra_js %}{% endblock %}
{% endblock %}
