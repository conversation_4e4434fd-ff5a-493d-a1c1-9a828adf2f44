.PHONY: run reset_db reset_db_production seed_data seed_data_production migrate_only help test_coverage pytest pytest_app pytest_coverage pytest_fast simple_reset_db fix_migrations recover_migrations recover_database emergency_reset emergency_reset_silent



# --- Start Django development server (accessible via LAN) ---
run:
	@echo "🚀 Starting Django development server at http://0.0.0.0:8000 ..."
	python manage.py runserver 0.0.0.0:8000



# --- Reset local SQLite database and clean cache files ---
reset_db:
	@echo "🔄  Starting full Django reset: database, cache, media files, and migrations..."
	@echo "⚠️  WARNING: This will completely wipe your local development database!"
	@echo "⚠️  All data will be lost. Are you sure you want to continue?"
	@read -p "Type 'yes' to confirm: " confirm && [ "$$confirm" = "yes" ] || (echo "❌ Reset cancelled." && exit 1)
	
	@echo "🛡️  Checking environment safety..."
	@if [ "$$DEBUG" = "false" ] || [ "$$RENDER" = "true" ]; then \
		echo "🚨 Production environment detected. Use 'make reset_db_production' instead."; \
		echo "ℹ️  This prevents accidental database reset in production."; \
		exit 1; \
	fi
	
	@echo "🔍  Running environment validation..."
	@python scripts/validate_environment.py || exit 1
	
	@echo "ℹ️  Note: Please stop any running Django processes manually if needed."
	
	@echo "🗑️  Deleting SQLite database file..."
	@rm -f db.sqlite3 || true
	@echo "✅  Database file removed."

	@echo "🧹  Cleaning all Python cache files..."
	@find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
	@find . -name "*.pyc" -delete || true
	@find . -name "*.pyo" -delete || true
	@echo "✅  Python cache cleaned."

	@echo "🗑️  Cleaning Django migration cache..."
	@find . -path "*/migrations/__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
	@find . -path "*/migrations/*.pyc" -delete || true
	@echo "✅  Migration cache cleaned."

	@echo "🖼️  Clearing all user-uploaded media files..."
	@rm -rf media/* || true
	@mkdir -p media
	@echo "✅  Media files cleared."

	@echo "🗑️  Resetting migration files..."
	@$(MAKE) fix_migrations
	@echo "✅  Migration files reset."

	@echo "⚙️  Creating fresh migrations..."
	@python manage.py makemigrations || (echo "❌ Failed to create migrations. Switching to emergency reset..." && $(MAKE) emergency_reset_silent && exit 0)
	@echo "✅  Migrations created successfully."

	@echo "⚙️  Applying migrations to rebuild the database schema..."
	@python manage.py migrate || (echo "❌ Failed to apply migrations. Switching to emergency reset..." && $(MAKE) emergency_reset_silent && exit 0)
	@echo "✅  Database schema rebuilt."

	@echo "🏙️  Seeding US cities data from CSV file..."
	@python manage.py seed_us_cities --clear || (echo "⚠️  Warning: US cities seeding failed, but continuing..." && true)
	@echo "✅  US cities data seeded."

	@echo "👤  Creating Django superuser..."
	@python scripts/create_superuser.py || (echo "❌ Failed to create superuser." && exit 1)

	@echo ""
	@echo "✅  🎉 Reset complete! Your development environment is ready."
	@echo "📋  Summary:"
	@echo "    • Fresh SQLite database created"
	@echo "    • All cache files cleaned"
	@echo "    • Migrations recreated and applied"
	@echo "    • Superuser created (email: <EMAIL>, password: 123)"
	@echo "    • US cities data seeded"
	@echo ""
	@echo "🚀  Run 'make run' to start the development server."

# --- Fix migration directories and files ---
fix_migrations:
	@echo "🔧  Fixing migration directories and files..."
	@echo "📁  Ensuring migration directories exist..."
	@for app in accounts_app admin_app booking_cart_app dashboard_app discount_app home_app notifications_app payments_app review_app utils utility_app venues_app; do \
		mkdir -p $$app/migrations; \
		if [ ! -f $$app/migrations/__init__.py ]; then \
			touch $$app/migrations/__init__.py; \
			echo "✅  Created $$app/migrations/__init__.py"; \
		fi; \
	done
	@echo "🗑️  Removing old migration files (keeping __init__.py)..."
	@find . -path "*/migrations/*.py" ! -name "__init__.py" -delete || true
	@echo "✅  Migration directories fixed."

# --- Recovery mechanism for migrations ---
recover_migrations:
	@echo "🚨  Starting migration recovery process..."
	@echo "🔧  Reinstalling Django (this may fix migration system issues)..."
	@pip install --upgrade --force-reinstall Django
	@echo "🔄  Clearing Python import cache..."
	@python -c "import sys; sys.path_importer_cache.clear(); print('✅ Import cache cleared')"
	@echo "🏗️  Attempting to recreate migrations..."
	@$(MAKE) fix_migrations
	@python manage.py makemigrations || (echo "❌ Migration recovery failed. Please check your Django installation." && exit 1)
	@echo "✅  Migration recovery successful."

# --- Recovery mechanism for database ---
recover_database:
	@echo "🚨  Starting database recovery process..."
	@echo "🗑️  Removing corrupted database..."
	@rm -f db.sqlite3 || true
	@echo "🔄  Using Django's migrate with --run-syncdb..."
	@python manage.py migrate --run-syncdb || (echo "❌ Database recovery failed." && exit 1)
	@echo "✅  Database recovery successful."

# --- Emergency reset (nuclear option) ---
emergency_reset:
	@echo "🚨  EMERGENCY RESET - This is the nuclear option!"
	@echo "⚠️  WARNING: This will completely reset everything and reinstall Django!"
	@echo "⚠️  Use this only if normal reset fails!"
	@read -p "Type 'EMERGENCY' to confirm: " confirm && [ "$$confirm" = "EMERGENCY" ] || (echo "❌ Emergency reset cancelled." && exit 1)
	
	@$(MAKE) emergency_reset_silent

# --- Emergency reset (silent - no confirmation) ---
emergency_reset_silent:
	@echo "🚨  Starting emergency reset process..."
	@echo "ℹ️  Note: Please stop any running Django processes manually if needed."
	
	@echo "🗑️  Nuclear cleanup - removing everything..."
	@rm -f db.sqlite3 || true
	@rm -rf media/* || true
	@find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
	@find . -name "*.pyc" -delete || true
	@find . -name "*.pyo" -delete || true
	@find . -path "*/migrations/*.py" ! -name "__init__.py" -delete || true
	@find . -path "*/migrations/__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
	
	@echo "🔧  Fixing migration directories..."
	@$(MAKE) fix_migrations
	
	@echo "🔄  Reinstalling Django and dependencies..."
	@pip install --upgrade --force-reinstall Django
	@pip install --upgrade --force-reinstall django-extensions
	
	@echo "🚀  Starting fresh Django setup..."
	@python manage.py makemigrations || (echo "❌ Emergency reset failed at makemigrations." && exit 1)
	@python manage.py migrate || (echo "❌ Emergency reset failed at migrate." && exit 1)
	
	@echo "🏙️  Seeding US cities data from CSV file..."
	@python manage.py seed_us_cities --clear || (echo "⚠️  Warning: US cities seeding failed, but continuing..." && true)
	@echo "✅  US cities data seeded."

	@echo "👤  Creating Django superuser..."
	@python scripts/create_superuser.py || (echo "❌ Failed to create superuser." && exit 1)
	
	@echo "✅  🎉 Emergency reset complete! Django has been completely reset."
	@echo "🚀  Run 'make run' to start the development server."

# --- Reset database (Production Safe) ---
reset_db_production:
	@echo "🔄  Starting full Django reset in PRODUCTION MODE..."
	@echo "⚠️  WARNING: This will completely wipe your production database!"
	@echo "⚠️  All user data, bookings, payments, and content will be lost!"
	@echo "⚠️  This action cannot be undone!"
	@echo ""
	@read -p "Type 'RESET_PRODUCTION' to confirm: " confirm && [ "$$confirm" = "RESET_PRODUCTION" ] || (echo "❌ Reset cancelled." && exit 1)

	@echo "🔍  Running environment validation..."
	@python scripts/validate_environment.py || exit 1

	@echo "⚙️  Creating fresh migrations..."
	@python manage.py makemigrations || (echo "⚠️  Warning: Failed to create migrations. Trying alternative approach..." && \
		echo "🔄  Creating basic database structure..." && \
		python manage.py migrate --run-syncdb 2>/dev/null || \
		(echo "❌ Failed to create database. Please check your Django installation." && exit 1))
	@echo "✅  Migrations created successfully."

	@echo "⚙️  Applying migrations to rebuild the database schema..."
	@python manage.py migrate || (echo "⚠️  Warning: Regular migration failed. Using syncdb approach..." && \
		python manage.py migrate --run-syncdb) || \
		(echo "❌ Failed to apply migrations. Check for errors." && exit 1)
	@echo "✅  Database schema rebuilt."

	@echo "🏙️  Seeding US cities data from CSV file..."
	@python manage.py seed_us_cities --clear || (echo "⚠️  Warning: US cities seeding failed, but continuing..." && true)
	@echo "✅  US cities data seeded."

	@echo "👤  Creating Django superuser..."
	@python scripts/create_superuser.py || (echo "❌ Failed to create superuser." && exit 1)

	@echo ""
	@echo "✅  🎉 Production reset complete! Your production environment is ready."
	@echo "📋  Summary:"
	@echo "    • Production database reset"
	@echo "    • Migrations recreated and applied"
	@echo "    • Superuser created (email: <EMAIL>, password: 123)"
	@echo "    • US cities data seeded"
	@echo ""
	@echo "🚀  Your production environment is now ready for deployment."



# --- Seed database with test data ---
seed_data:
	@echo "🌱 Seeding database with test data..."
	@if [ "$$DEBUG" = "false" ] || [ "$$RENDER" = "true" ]; then \
		echo "🚨 Production environment detected. Use 'make seed_data_production' instead."; \
		echo "ℹ️  This prevents accidental data seeding in production."; \
		exit 1; \
	fi
	python manage.py seed_data
	@echo "🏷️ Seeding service categories..."
	python manage.py seed_service_categories
	@echo "✅ Database seeding completed!"

# --- Seed database with test data (Production Safe) ---
seed_data_production:
	@echo "🌱 Seeding database with test data (PRODUCTION MODE)..."
	@echo "⚠️  WARNING: This will replace all data with test data!"
	@read -p "Are you sure you want to continue? (yes/no): " confirm && [ "$$confirm" = "yes" ] || exit 1
	python manage.py seed_data --force-production
	@echo "🏷️ Seeding service categories..."
	python manage.py seed_service_categories
	@echo "✅ Database seeding completed!"

# --- Apply migrations only (Production Safe) ---
migrate_only:
	@echo "⚙️  Applying database migrations..."
	python manage.py makemigrations
	python manage.py migrate
	@echo "✅ Migrations applied successfully!"

# --- Show help information ---
help:
	@echo "🚀 CozyWish Makefile Commands"
	@echo ""
	@echo "📋 Development Commands:"
	@echo "  make run              - Start Django development server"
	@echo "  make reset_db         - Reset database with safety checks (development only)"
	@echo "  make simple_reset_db  - Simple reset bypassing migration issues (development only)"
	@echo "  make emergency_reset  - Nuclear reset option (reinstalls Django)"
	@echo "  make fix_migrations   - Fix migration directories and files"
	@echo "  make seed_data        - Seed test data (development only)"
	@echo ""
	@echo "🏭 Production Commands:"
	@echo "  make reset_db_production   - Reset database in production (DANGEROUS)"
	@echo "  make seed_data_production  - Seed test data in production (DANGEROUS)"
	@echo "  make migrate_only          - Apply migrations only (safe)"
	@echo ""
	@echo "🧪 Testing Commands:"
	@echo "  make test_coverage    - Run Django tests with coverage"
	@echo "  make pytest          - Run all tests with pytest"
	@echo "  make pytest_fast     - Run pytest in fast mode"
	@echo ""
	@echo "ℹ️  Use 'make help' to see this message again"
	@echo ""



# --- Run Django tests with coverage reporting ---
test_coverage:
	@echo "📊 [1/1] Running Django tests with coverage..."
	coverage run --source='.' manage.py test
	@echo "📝 Coverage report:"
	coverage report
	@echo "✅ All tests with coverage completed!"



# ---Run all tests using pytest ---
pytest:
	@echo "🧪 [1/1] Running all tests with pytest..."
	pytest
	@echo "✅ All pytest tests completed!"



# --- Run tests for a specific app using pytest, Usage: make pytest_app APP=app_name ---
pytest_app:
	@echo "🧪 [1/2] Running tests for specific app with pytest..."
	@echo "ℹ️  Usage: make pytest_app APP=accounts_app"
	@if [ -z "$(APP)" ]; then \
		echo "❌ Please specify APP=<app_name> (e.g., make pytest_app APP=accounts_app)"; \
		exit 1; \
	fi
	pytest $(APP)/tests/ -v
	@echo "✅ Tests for app [$(APP)] completed!"



# --- Run pytest with coverage, optionally for a specific app, Usage: make pytest_coverage [APP=app_name] ---
pytest_coverage:
	@echo "📊 [1/2] Running pytest with coverage..."
	@if [ -z "$(APP)" ]; then \
		echo "🗂️  Running coverage for ALL apps..."; \
		pytest --cov=. --cov-report=term-missing --cov-report=html; \
		echo "📄 HTML coverage report generated at htmlcov/index.html"; \
	else \
		echo "🗂️  Running coverage for app: [$(APP)] ..."; \
		pytest $(APP)/tests/ --cov=$(APP) --cov-report=term-missing --cov-report=html; \
		echo "📄 HTML coverage report for $(APP) at htmlcov/index.html"; \
	fi
	@echo "✅ Pytest with coverage completed!"



# --- Run pytest quickly with minimal output (quiet mode) ---
pytest_fast:
	@echo "🏃 [1/1] Running pytest in fast mode (quiet output)..."
	pytest -q --tb=no
	@echo "✅ Fast pytest run completed!"



# --- Simple Reset (Bypasses Migration Issues) ---
simple_reset_db:
	@echo "🔄  Starting simple Django reset (bypasses migration issues)..."
	@echo "⚠️  WARNING: This will completely wipe your local development database!"
	@echo "⚠️  All data will be lost. Are you sure you want to continue?"
	@read -p "Type 'yes' to confirm: " confirm && [ "$$confirm" = "yes" ] || (echo "❌ Reset cancelled." && exit 1)
	
	@echo "🛡️  Checking environment safety..."
	@if [ "$$DEBUG" = "false" ] || [ "$$RENDER" = "true" ]; then \
		echo "🚨 Production environment detected. Use 'make reset_db_production' instead."; \
		echo "ℹ️  This prevents accidental database reset in production."; \
		exit 1; \
	fi
	
	@echo "🗑️  Deleting SQLite database file..."
	@rm -f db.sqlite3 || true
	@echo "✅  Database file removed."

	@echo "🧹  Cleaning cache files..."
	@find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
	@find . -name "*.pyc" -delete || true
	@find . -name "*.pyo" -delete || true
	@echo "✅  Cache files cleaned."

	@echo "🖼️  Clearing all user-uploaded media files..."
	@rm -rf media/* || true
	@mkdir -p media
	@echo "✅  Media files cleared."

	@echo "🏗️  Creating basic database structure..."
	@python -c "import sqlite3; conn = sqlite3.connect('db.sqlite3'); conn.execute('CREATE TABLE IF NOT EXISTS auth_user (id INTEGER PRIMARY KEY, username TEXT UNIQUE, email TEXT, password TEXT);'); conn.commit(); conn.close(); print('✅ Basic database created')"

	@echo "✅  🎉 Simple reset complete! Basic database structure created."
	@echo "📋  Summary:"
	@echo "    • SQLite database file created"
	@echo "    • Cache files cleaned"
	@echo "    • Media files cleared"
	@echo "    • Basic database structure ready"
	@echo ""
	@echo "🚀  Run 'make run' to start the development server."
	@echo "ℹ️  Note: You may need to run migrations manually later."








