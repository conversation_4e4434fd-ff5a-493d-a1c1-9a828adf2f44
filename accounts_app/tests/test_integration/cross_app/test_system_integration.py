"""
Cross-app integration tests for authentication system.

These tests verify that authentication works correctly across
different apps in the CozyWish system.
"""

import pytest
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model

from accounts_app.constants import UserRoles
from ..fixtures.test_data import CustomerTestData, ServiceProviderTestData

User = get_user_model()


@pytest.mark.skip(reason='Skip cross-app integration tests for MVP')
class AuthenticationSystemIntegrationTest(TestCase):
    """Test authentication system integration across apps."""
    
    def setUp(self):
        """Set up test client and data."""
        self.client = Client()
        self.customer_data = CustomerTestData()
        self.provider_data = ServiceProviderTestData()
        
    def test_customer_authentication_across_apps(self):
        """Test customer authentication works across different apps."""
        customer = self.customer_data.create_active_customer()
        
        # Login as customer
        self.client.login(email=customer.email, password='TestPass123!')
        
        # Test access to customer profile (accounts app)
        response = self.client.get(reverse('accounts_app:customer_profile'))
        self.assertEqual(response.status_code, 200)
        
        # Test that authentication state is maintained across apps
        self.assertEqual(str(customer.pk), self.client.session['_auth_user_id'])
        
    def test_provider_authentication_across_apps(self):
        """Test service provider authentication works across different apps."""
        provider = self.provider_data.create_active_provider()
        
        # Login as provider
        self.client.login(email=provider.email, password='TestPass123!')
        
        # Test access to provider profile (accounts app)
        response = self.client.get(reverse('accounts_app:service_provider_profile'))
        self.assertEqual(response.status_code, 200)
        
        # Test that authentication state is maintained across apps
        self.assertEqual(str(provider.pk), self.client.session['_auth_user_id'])
        
    def test_authentication_middleware_integration(self):
        """Test authentication middleware works across all apps."""
        customer = self.customer_data.create_active_customer()
        
        # Access protected resource without authentication
        response = self.client.get(reverse('accounts_app:customer_profile'))
        
        # Should redirect to login
        self.assertEqual(response.status_code, 302)
        self.assertIn('login', response.url)
        
        # Login and try again
        self.client.login(email=customer.email, password='TestPass123!')
        response = self.client.get(reverse('accounts_app:customer_profile'))
        
        # Should now have access
        self.assertEqual(response.status_code, 200)
        
    def test_session_consistency_across_apps(self):
        """Test session consistency across different apps."""
        customer = self.customer_data.create_active_customer()
        
        # Login
        self.client.login(email=customer.email, password='TestPass123!')
        
        # Set session data
        session = self.client.session
        session['test_data'] = 'test_value'
        session.save()
        
        # Access different apps - session should be consistent
        response = self.client.get(reverse('accounts_app:customer_profile'))
        self.assertEqual(response.status_code, 200)
        
        # Session data should still be there
        self.assertEqual(self.client.session['test_data'], 'test_value')
        self.assertEqual(str(customer.pk), self.client.session['_auth_user_id'])
        
    def test_logout_affects_all_apps(self):
        """Test logout affects authentication across all apps."""
        customer = self.customer_data.create_active_customer()
        
        # Login
        self.client.login(email=customer.email, password='TestPass123!')
        
        # Verify logged in
        self.assertEqual(str(customer.pk), self.client.session['_auth_user_id'])
        
        # Logout
        self.client.logout()
        
        # Verify logged out across apps
        self.assertNotIn('_auth_user_id', self.client.session)
        
        # Try to access protected resource
        response = self.client.get(reverse('accounts_app:customer_profile'))
        self.assertEqual(response.status_code, 302)
        
    def test_role_based_access_across_apps(self):
        """Test role-based access control works across apps."""
        customer = self.customer_data.create_active_customer()
        provider = self.provider_data.create_active_provider()
        
        # Login as customer
        self.client.login(email=customer.email, password='TestPass123!')
        
        # Customer can access customer resources
        response = self.client.get(reverse('accounts_app:customer_profile'))
        self.assertEqual(response.status_code, 200)
        
        # Customer cannot access provider resources
        response = self.client.get(reverse('accounts_app:service_provider_profile'))
        self.assertEqual(response.status_code, 302)
        
        # Switch to provider
        self.client.logout()
        self.client.login(email=provider.email, password='TestPass123!')
        
        # Provider can access provider resources
        response = self.client.get(reverse('accounts_app:service_provider_profile'))
        self.assertEqual(response.status_code, 200)
        
        # Provider cannot access customer resources
        response = self.client.get(reverse('accounts_app:customer_profile'))
        self.assertEqual(response.status_code, 302)


@pytest.mark.skip(reason='Skip cross-app integration tests for MVP')
class AuthenticationErrorHandlingTest(TestCase):
    """Test authentication error handling across apps."""
    
    def setUp(self):
        """Set up test client and data."""
        self.client = Client()
        self.customer_data = CustomerTestData()
        
    def test_authentication_error_handling(self):
        """Test authentication errors are handled gracefully across apps."""
        # Try to access protected resource without authentication
        response = self.client.get(reverse('accounts_app:customer_profile'))
        
        # Should redirect to login, not crash
        self.assertEqual(response.status_code, 302)
        self.assertIn('login', response.url)
        
    def test_invalid_user_handling(self):
        """Test handling of invalid user sessions."""
        customer = self.customer_data.create_active_customer()
        
        # Login
        self.client.login(email=customer.email, password='TestPass123!')
        
        # Delete user (simulate invalid session)
        customer.delete()
        
        # Try to access protected resource
        response = self.client.get(reverse('accounts_app:customer_profile'))
        
        # Should handle gracefully (redirect to login)
        self.assertEqual(response.status_code, 302)
        self.assertIn('login', response.url)
        
    def test_inactive_user_handling(self):
        """Test handling of inactive user sessions."""
        customer = self.customer_data.create_active_customer()
        
        # Login
        self.client.login(email=customer.email, password='TestPass123!')
        
        # Deactivate user
        customer.is_active = False
        customer.save()
        
        # Try to access protected resource
        response = self.client.get(reverse('accounts_app:customer_profile'))
        
        # Should handle gracefully (redirect to login)
        self.assertEqual(response.status_code, 302)
        self.assertIn('login', response.url)


@pytest.mark.django_db
@pytest.mark.skip(reason='Skip cross-app integration tests for MVP')
class CrossAppIntegrationPytestTest:
    """Pytest-style cross-app integration tests."""
    
    def test_authentication_system_integration(self):
        """Test complete authentication system integration."""
        customer_data = CustomerTestData()
        provider_data = ServiceProviderTestData()
        
        customer = customer_data.create_active_customer()
        provider = provider_data.create_active_provider()
        
        # Test customer authentication across system
        client = Client()
        client.login(email=customer.email, password='TestPass123!')
        
        # Should have access to customer resources
        response = client.get(reverse('accounts_app:customer_profile'))
        assert response.status_code == 200
        
        # Should not have access to provider resources
        response = client.get(reverse('accounts_app:service_provider_profile'))
        assert response.status_code == 302
        
        # Switch to provider
        client.logout()
        client.login(email=provider.email, password='TestPass123!')
        
        # Should have access to provider resources
        response = client.get(reverse('accounts_app:service_provider_profile'))
        assert response.status_code == 200
        
        # Should not have access to customer resources
        response = client.get(reverse('accounts_app:customer_profile'))
        assert response.status_code == 302
        
    def test_authentication_state_consistency(self):
        """Test authentication state is consistent across the system."""
        customer_data = CustomerTestData()
        customer = customer_data.create_active_customer()
        
        client = Client()
        
        # Login
        login_url = reverse('accounts_app:customer_login')
        response = client.post(login_url, {
            'email': customer.email,
            'password': 'TestPass123!'
        })
        
        assert response.status_code == 302
        assert str(customer.pk) == client.session['_auth_user_id']
        
        # Access multiple resources - state should be consistent
        resources = [
            reverse('accounts_app:customer_profile'),
            reverse('accounts_app:customer_change_password'),
        ]
        
        for resource in resources:
            response = client.get(resource)
            assert response.status_code == 200
            assert str(customer.pk) == client.session['_auth_user_id']
            
        # Logout
        client.logout()
        
        # State should be cleared
        assert '_auth_user_id' not in client.session
        
        # All resources should now be inaccessible
        for resource in resources:
            response = client.get(resource)
            assert response.status_code == 302  # Redirect to login
            
    def test_authentication_security_across_apps(self):
        """Test authentication security features work across apps."""
        customer_data = CustomerTestData()
        provider_data = ServiceProviderTestData()
        
        customer = customer_data.create_active_customer()
        provider = provider_data.create_active_provider()
        
        # Test session isolation
        client1 = Client()
        client2 = Client()
        
        # Login as customer in client1
        client1.login(email=customer.email, password='TestPass123!')
        
        # Login as provider in client2
        client2.login(email=provider.email, password='TestPass123!')
        
        # Each client should only have access to their role's resources
        response1 = client1.get(reverse('accounts_app:customer_profile'))
        assert response1.status_code == 200
        
        response1 = client1.get(reverse('accounts_app:service_provider_profile'))
        assert response1.status_code == 302
        
        response2 = client2.get(reverse('accounts_app:service_provider_profile'))
        assert response2.status_code == 200
        
        response2 = client2.get(reverse('accounts_app:customer_profile'))
        assert response2.status_code == 302 