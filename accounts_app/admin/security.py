# --- Standard Library Imports ---
import csv
from datetime import timedelta

# --- Django Imports ---
from django.contrib import admin, messages
from django.http import HttpResponse
from django.utils import timezone
from django.utils.html import format_html

# --- Local Imports ---
from ..models import LoginHistory, LoginAlert


@admin.register(LoginHistory)
class LoginHistoryAdmin(admin.ModelAdmin):
    """Monitor login history with risk indicators."""
    list_display = (
        'user', 'timestamp', 'ip_address', 'is_successful',
        'user_role', 'get_user_agent_short', 'is_suspicious'
    )
    list_filter = (
        'is_successful', 'timestamp', 'user__role',
        ('timestamp', admin.DateFieldListFilter)
    )
    search_fields = ('user__email', 'ip_address')
    readonly_fields = (
        'timestamp', 'user', 'ip_address',
        'user_agent', 'is_successful'
    )
    date_hierarchy = 'timestamp'
    actions = ['cleanup_old_records', 'export_security_report']

    def user_role(self, obj):
        return getattr(obj.user, 'get_role_display', lambda: obj.user.role)()
    user_role.short_description = 'Role'

    def get_user_agent_short(self, obj):
        ua = obj.user_agent or ''
        browser = next((b for b in ['Chrome', 'Firefox', 'Safari', 'Edge'] if b in ua), 'Other')
        os_ = next((o for o in ['Windows', 'Mac', 'Linux', 'Android', 'iOS'] if o in ua), 'Unknown')
        return f"{browser} on {os_}"
    get_user_agent_short.short_description = 'Browser/OS'

    def is_suspicious(self, obj):
        since = timezone.now() - timedelta(hours=1)
        fails = LoginHistory.objects.filter(
            ip_address=obj.ip_address,
            is_successful=False,
            timestamp__gte=since
        ).count()
        if fails >= 5:
            return format_html('<span style="color:red;">High Risk</span>')
        if fails >= 3:
            return format_html('<span style="color:orange;">Medium Risk</span>')
        return 'Failed' if not obj.is_successful else 'Normal'
    is_suspicious.short_description = 'Risk'

    def cleanup_old_records(self, request, queryset):
        cutoff = timezone.now() - timedelta(days=90)
        deleted, _ = LoginHistory.objects.filter(
            timestamp__lt=cutoff
        ).delete()
        self.message_user(request, f'Cleaned {deleted} records.', messages.SUCCESS)
    cleanup_old_records.short_description = 'Clean old records'

    def export_security_report(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = (
            f'attachment; filename="security_{timezone.now():%Y%m%d_%H%M%S}.csv"'
        )
        writer = csv.writer(response)
        writer.writerow(['Email', 'Role', 'Time', 'IP', 'Success', 'Risk'])
        for obj in queryset.order_by('-timestamp'):
            writer.writerow([
                obj.user.email,
                self.user_role(obj),
                obj.timestamp,
                obj.ip_address,
                obj.is_successful,
                self.is_suspicious(obj),
            ])
        return response
    export_security_report.short_description = 'Export report'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False


@admin.register(LoginAlert)
class LoginAlertAdmin(admin.ModelAdmin):
    """Manage login alerts and export stats."""
    list_display = (
        'alert_type', 'ip_address', 'user', 'severity',
        'attempt_count', 'is_resolved', 'created',
        'get_time_since_created'
    )
    list_filter = ('alert_type', 'severity', 'is_resolved', 'created')
    search_fields = ('ip_address', 'user__email')
    readonly_fields = ('created', 'updated')
    actions = ['mark_as_resolved', 'mark_as_unresolved', 'export_alert_report']

    def get_time_since_created(self, obj):
        delta = timezone.now() - obj.created
        if delta.days:
            return f"{delta.days}d ago"
        return f"{delta.seconds // 3600}h ago"
    get_time_since_created.short_description = 'Age'

    def mark_as_resolved(self, request, queryset):
        count = queryset.filter(is_resolved=False).update(is_resolved=True)
        self.message_user(request, f'{count} resolved.', messages.SUCCESS)
    mark_as_resolved.short_description = 'Resolve'

    def mark_as_unresolved(self, request, queryset):
        count = queryset.update(is_resolved=False)
        self.message_user(request, f'{count} unresolved.', messages.SUCCESS)
    mark_as_unresolved.short_description = 'Unresolve'

    def export_alert_report(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = (
            f'attachment; filename="alerts_{timezone.now():%Y%m%d_%H%M%S}.csv"'
        )
        writer = csv.writer(response)
        writer.writerow(['Type', 'IP', 'Email', 'Severity', 'Count', 'Resolved'])
        for obj in queryset.order_by('-created'):
            writer.writerow([
                obj.alert_type,
                obj.ip_address,
                obj.user.email,
                obj.severity,
                obj.attempt_count,
                obj.is_resolved,
            ])
        return response
    export_alert_report.short_description = 'Export alerts' 