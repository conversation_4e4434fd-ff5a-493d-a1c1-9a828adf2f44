{% extends 'base.html' %}

{% block title %}Notification Detail - CozyWish Admin{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Admin Notification Detail */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    body {
        font-family: var(--cw-font-primary);
        background: var(--cw-accent-light);
        color: var(--cw-neutral-800);
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
    }

    /* Custom Breadcrumb */
    .breadcrumb {
        background: white;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        box-shadow: var(--cw-shadow-sm);
        border: 1px solid #e9ecef;
    }

    .breadcrumb-item a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        color: var(--cw-brand-light);
    }

    /* Custom Cards */
    .card-cw {
        border: 1px solid #e9ecef;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
    }

    .card-cw-header {
        background: var(--cw-brand-primary);
        color: white;
        padding: 1.5rem;
        border-bottom: none;
    }

    .card-cw-body {
        padding: 2rem;
    }

    /* Custom Buttons */
    .btn-cw-primary {
        background: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
    }

    /* Custom Badges */
    .badge-cw {
        padding: 0.5rem 0.75rem;
        border-radius: 0.375rem;
        font-weight: 600;
        font-size: 0.75rem;
    }

    .badge-cw-primary {
        background: var(--cw-brand-primary);
        color: white;
    }

    .badge-cw-success {
        background: #059669;
        color: white;
    }

    .badge-cw-warning {
        background: #d97706;
        color: white;
    }

    .badge-cw-outline {
        background: white;
        color: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-primary);
    }

    /* Custom Alerts */
    .alert-cw-success {
        background: #f0fdf4;
        border: 1px solid #bbf7d0;
        color: #166534;
        border-radius: 0.5rem;
    }

    .alert-cw-info {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
    }

    /* Message Display */
    .message-display {
        background: var(--cw-accent-light);
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
        line-height: 1.6;
        color: var(--cw-neutral-700);
    }

    /* User Profile Card */
    .user-profile-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        overflow: hidden;
    }

    .user-profile-header {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        padding: 1.5rem;
        text-align: center;
    }

    .user-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--cw-brand-primary);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        margin: 0 auto 1rem;
        box-shadow: var(--cw-shadow-md);
    }

    .stats-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .stats-header {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        padding: 1rem 1.5rem;
        border-bottom: none;
        border-radius: 0.75rem 0.75rem 0 0;
    }

    .stats-body {
        padding: 1.5rem;
    }

    /* Info Sections */
    .info-section {
        background: var(--cw-accent-light);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        border-left: 4px solid var(--cw-brand-primary);
    }

    .info-label {
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .info-value {
        color: var(--cw-neutral-700);
        font-weight: 500;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Breadcrumb -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'notifications_app:admin_notification_dashboard' %}">
                            <i class="fas fa-bell me-1"></i>Notification Dashboard
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{% url 'notifications_app:admin_notification_list' %}">All Notifications</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Notification Detail</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Display messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert-cw-{{ message.tags }} alert mb-4" role="alert">
                <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
                {{ message }}
            </div>
        {% endfor %}
    {% endif %}

    <div class="row">
        <div class="col-lg-8">
            <div class="card-cw">
                <div class="card-cw-header">
                    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center gap-3">
                        <div>
                            <h5 class="mb-2">
                                {% if notification.read_status == 'unread' %}
                                    <span class="badge-cw badge-cw-primary me-2">NEW</span>
                                {% endif %}
                                <span class="badge-cw badge-cw-outline me-2">{{ notification.get_notification_type_display }}</span>
                            </h5>
                            <h4 class="mb-2 notification-title-white">{{ notification.title }}</h4>
                            <small class="notification-time-white">
                                <i class="fas fa-clock me-1"></i>{{ notification.created_at|date:"M d, Y, g:i a" }}
                            </small>
                        </div>
                    </div>
                </div>

                <div class="card-cw-body">
                    <!-- Message Section -->
                    <div class="mb-4">
                        <div class="info-label">
                            <i class="fas fa-envelope me-1"></i>Message Content
                        </div>
                        <div class="message-display">
                            {{ notification.message|linebreaks }}
                        </div>
                    </div>

                    <!-- Action URL Section -->
                    {% if notification.action_url %}
                        <div class="mb-4">
                            <div class="info-label">
                                <i class="fas fa-link me-1"></i>Action URL
                            </div>
                            <div class="info-section">
                                <a href="{{ notification.action_url }}" class="btn btn-cw-primary" target="_blank">
                                    <i class="fas fa-external-link-alt me-2"></i>{{ notification.action_url }}
                                </a>
                            </div>
                        </div>
                    {% endif %}

                    <!-- Related Information Section -->
                    {% if notification.related_object_id and notification.related_object_type %}
                        <div class="mb-4">
                            <div class="info-label">
                                <i class="fas fa-link me-1"></i>Related Information
                            </div>
                            <div class="alert-cw-info alert">
                                <i class="fas fa-info-circle me-2"></i>
                                This notification is related to <strong>{{ notification.related_object_type }}</strong> #{{ notification.related_object_id }}
                            </div>
                        </div>
                    {% endif %}

                    <!-- Status and Timing Information -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-section">
                                <div class="info-label">
                                    <i class="fas fa-eye me-1"></i>Read Status
                                </div>
                                <div class="info-value">
                                    {% if notification.read_status == 'read' %}
                                        <span class="badge-cw badge-cw-success">
                                            <i class="fas fa-check me-1"></i>Read
                                        </span>
                                        {% if notification.read_at %}
                                            <div class="mt-2">
                                                <small class="notification-meta-text">
                                                    Read on {{ notification.read_at|date:"M d, Y, g:i a" }}
                                                </small>
                                            </div>
                                        {% endif %}
                                    {% else %}
                                        <span class="badge-cw badge-cw-warning">
                                            <i class="fas fa-exclamation-circle me-1"></i>Unread
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-section">
                                <div class="info-label">
                                    <i class="fas fa-calendar me-1"></i>Created Date
                                </div>
                                <div class="info-value">
                                    <strong>{{ notification.created_at|date:"M d, Y" }}</strong>
                                    <div class="mt-1">
                                        <small class="notification-meta-text">
                                            at {{ notification.created_at|date:"g:i a" }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Information Sidebar -->
        <div class="col-lg-4">
            <div class="user-profile-card">
                <div class="user-profile-header">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <h5 class="mb-1">{{ notification.user.get_full_name|default:"No Name Provided" }}</h5>
                    <p class="mb-0 notification-meta-text">{{ notification.user.email }}</p>
                </div>
                <div class="card-cw-body">
                    <!-- Account Information -->
                    <div class="row text-center mb-4">
                        <div class="col-6">
                            <div class="info-section">
                                <div class="info-label">Account Type</div>
                                <div class="info-value">
                                    <strong>{{ notification.user.role|title }}</strong>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="info-section">
                                <div class="info-label">Member Since</div>
                                <div class="info-value">
                                    <strong>{{ notification.user.date_joined|date:"M Y" }}</strong>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Account Status -->
                    <div class="info-section mb-4">
                        <div class="info-label">
                            <i class="fas fa-shield-alt me-1"></i>Account Status
                        </div>
                        <div class="info-value">
                            {% if notification.user.is_active %}
                                <span class="badge-cw badge-cw-success">
                                    <i class="fas fa-check me-1"></i>Active
                                </span>
                            {% else %}
                                <span class="badge-cw badge-cw-warning">
                                    <i class="fas fa-times me-1"></i>Inactive
                                </span>
                            {% endif %}

                            {% if notification.user.last_login %}
                                <div class="mt-2">
                                    <small class="notification-meta-text">
                                        <i class="fas fa-clock me-1"></i>
                                        Last login: {{ notification.user.last_login|date:"M d, Y, g:i a" }}
                                    </small>
                                </div>
                            {% else %}
                                <div class="mt-2">
                                    <small class="notification-meta-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Never logged in
                                    </small>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Action Button -->
                    <div class="d-grid">
                        <button class="btn btn-cw-secondary btn-sm" onclick="alert('User profile view not implemented')">
                            <i class="fas fa-user me-2"></i>View User Profile
                        </button>
                    </div>
                </div>
            </div>

            <!-- Notification Statistics -->
            <div class="stats-card mt-4">
                <div class="stats-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>User Notification Stats
                    </h6>
                </div>
                <div class="stats-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="info-section">
                                <div class="info-label">Total</div>
                                <div class="info-value">
                                    <strong class="notification-stat-number">
                                        {{ user_notification_stats.total|default:0 }}
                                    </strong>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="info-section">
                                <div class="info-label">Unread</div>
                                <div class="info-value">
                                    <strong class="notification-stat-number">
                                        {{ user_notification_stats.unread|default:0 }}
                                    </strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card-cw">
                <div class="card-cw-body">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                        <a href="{% url 'notifications_app:admin_notification_list' %}" class="btn btn-cw-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to All Notifications
                        </a>
                        <div class="d-flex flex-column flex-sm-row gap-2">
                            <a href="{% url 'notifications_app:admin_notification_dashboard' %}" class="btn btn-cw-primary">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    // Auto-hide success messages after 4 seconds
    const successAlerts = document.querySelectorAll('.alert-cw-success');
    successAlerts.forEach(alert => {
        setTimeout(() => {
            if (alert.parentNode) {
                alert.style.transition = 'opacity 0.3s ease';
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.remove();
                    }
                }, 300);
            }
        }, 4000);
    });

    // Add loading state to action buttons
    const actionButtons = document.querySelectorAll('a[href*="notification"], button');
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            if (this.href && !this.onclick) {
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
                this.style.pointerEvents = 'none';

                // Restore button if navigation fails
                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.style.pointerEvents = 'auto';
                }, 5000);
            }
        });
    });

    // Add smooth scroll to top functionality
    const backButton = document.querySelector('a[href*="notification_list"]');
    if (backButton) {
        backButton.addEventListener('click', function() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    }

    // Add copy functionality for email addresses
    const emailElements = document.querySelectorAll('p:contains("@")');
    emailElements.forEach(element => {
        if (element.textContent.includes('@')) {
            element.style.cursor = 'pointer';
            element.title = 'Click to copy email address';
            element.addEventListener('click', function() {
                const email = this.textContent.trim();
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(email).then(() => {
                        // Show temporary feedback
                        const originalText = this.textContent;
                        this.textContent = 'Email copied!';
                        this.style.color = 'var(--cw-brand-primary)';
                        setTimeout(() => {
                            this.textContent = originalText;
                            this.style.color = '';
                        }, 2000);
                    });
                }
            });
        }
    });
});
</script>
{% endblock %}
