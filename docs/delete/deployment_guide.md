# CozyWish Deployment Guide

## Table of Contents
1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Database Configuration](#database-configuration)
4. [Static Files and Media Storage](#static-files-and-media-storage)
5. [Email Configuration](#email-configuration)
6. [Security Configuration](#security-configuration)
7. [Local Development Setup](#local-development-setup)
8. [Production Deployment](#production-deployment)
9. [Post-Deployment Setup](#post-deployment-setup)
10. [Monitoring and Maintenance](#monitoring-and-maintenance)
11. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements
- **Python**: 3.9+ (tested with 3.9, 3.10, 3.11)
- **Database**: PostgreSQL 12+ (SQLite for development)
- **Redis**: 6.0+ (for caching - optional)
- **Node.js**: 16+ (for frontend asset building - optional)

### Required Accounts
- **AWS Account**: For S3 storage and optional services
- **SendGrid Account**: For email delivery
- **Domain**: For production deployment

### Tools and Dependencies
- Git (for version control)
- pip (Python package manager)
- Virtual environment tool (venv, virtualenv, or conda)
- Database client (psql for PostgreSQL)

## Environment Setup

### 1. Python Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Linux/Mac:
source venv/bin/activate
# On Windows:
venv\Scripts\activate

# Upgrade pip
pip install --upgrade pip

# Install dependencies
pip install -r requirements.txt
```

### 2. Environment Variables

Create a `.env` file in the project root:

```bash
# --- Core Configuration ---
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# --- Database Configuration ---
DATABASE_URL=sqlite:///db.sqlite3
# For PostgreSQL:
# DATABASE_URL=postgresql://user:password@localhost:5432/cozywish

# --- Email Configuration ---
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=apikey
EMAIL_HOST_PASSWORD=your-sendgrid-api-key
DEFAULT_FROM_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>

# --- AWS S3 Configuration ---
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-s3-bucket-name
AWS_S3_REGION_NAME=us-east-1
AWS_S3_CUSTOM_DOMAIN=your-custom-domain.com

# --- Security Configuration ---
SECURE_SSL_REDIRECT=False
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False

# --- Application Configuration ---
PLATFORM_FEE_RATE=0.05
DASHBOARD_CACHE_TIMEOUT=300
NOTIFICATION_CACHE_TIMEOUT=60
```

### 3. Required Environment Variables

#### Production Environment Variables
```bash
# Core Settings
SECRET_KEY=your-production-secret-key
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com

# Database
DATABASE_URL=postgresql://user:password@host:port/database

# AWS S3 (Required for production)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-s3-bucket-name
AWS_S3_REGION_NAME=us-east-1
AWS_S3_CUSTOM_DOMAIN=cdn.yourdomain.com

# Email (SendGrid)
EMAIL_HOST_PASSWORD=your-sendgrid-api-key
DEFAULT_FROM_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>

# Security
SECURE_SSL_REDIRECT=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
```

## Database Configuration

### Development Database (SQLite)
SQLite is used by default for development:

```bash
# Apply migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Load initial data
python manage.py production_seed --reset-db --skip-confirmation
```

### Production Database (PostgreSQL)

#### 1. Install PostgreSQL
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install postgresql postgresql-contrib

# CentOS/RHEL
sudo yum install postgresql postgresql-server postgresql-contrib

# macOS
brew install postgresql
```

#### 2. Create Database
```bash
# Access PostgreSQL
sudo -u postgres psql

# Create database and user
CREATE DATABASE cozywish;
CREATE USER cozywish_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE cozywish TO cozywish_user;

# Exit PostgreSQL
\q
```

#### 3. Configure Database URL
```bash
DATABASE_URL=postgresql://cozywish_user:secure_password@localhost:5432/cozywish
```

#### 4. Migration and Setup
```bash
# Install PostgreSQL adapter
pip install psycopg2-binary

# Apply migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser
```

## Static Files and Media Storage

### Development Setup
For development, files are served locally:

```bash
# Collect static files
python manage.py collectstatic --no-input

# Static files served at /static/
# Media files served at /media/
```

### Production AWS S3 Setup

#### 1. Create S3 Bucket
```bash
# Using AWS CLI
aws s3 mb s3://your-bucket-name --region us-east-1

# Configure bucket policy for web access
aws s3api put-bucket-policy --bucket your-bucket-name --policy file://bucket-policy.json
```

#### 2. Bucket Policy Example
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::your-bucket-name/*"
    }
  ]
}
```

#### 3. Configure IAM User
Create an IAM user with S3 permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::your-bucket-name",
        "arn:aws:s3:::your-bucket-name/*"
      ]
    }
  ]
}
```

#### 4. CloudFront Setup (Optional)
For better performance, configure CloudFront CDN:

```bash
# Create CloudFront distribution
aws cloudfront create-distribution --distribution-config file://cloudfront-config.json
```

## Email Configuration

### Development Email
For development, emails are printed to console:

```bash
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
```

### Production Email (SendGrid)

#### 1. SendGrid Setup
```bash
# Sign up for SendGrid account
# Create API key with Mail Send permissions
# Configure environment variables
```

#### 2. Email Configuration
```bash
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=apikey
EMAIL_HOST_PASSWORD=your-sendgrid-api-key
DEFAULT_FROM_EMAIL=<EMAIL>
```

#### 3. Domain Authentication
```bash
# Add your domain to SendGrid
# Configure DNS records for domain authentication
# Verify domain in SendGrid dashboard
```

#### 4. Email Templates
Email templates are located in `templates/emails/`:
- `email_verification.html` - Email verification
- `password_reset.html` - Password reset
- `welcome.html` - Welcome email

## Security Configuration

### Development Security
```bash
DEBUG=True
SECURE_SSL_REDIRECT=False
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False
```

### Production Security
```bash
DEBUG=False
SECURE_SSL_REDIRECT=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
SECURE_HSTS_SECONDS=********
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True
X_FRAME_OPTIONS=DENY
```

### Password Security
The application includes comprehensive password validation:

```python
# Minimum 12 characters
# Complexity requirements (uppercase, lowercase, numbers, special chars)
# No common passwords
# No personal information
# No sequential/repeated characters
# Password history tracking (5 previous passwords)
```

### Account Security Features
- Account lockout after failed attempts
- IP-based lockout protection
- Login history tracking
- Two-factor authentication support
- Session security

## Local Development Setup

### 1. Clone Repository
```bash
git clone https://github.com/yourusername/CozyWish.git
cd CozyWish
```

### 2. Setup Environment
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Configure Environment
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your settings
DEBUG=True
SECRET_KEY=your-dev-secret-key
DATABASE_URL=sqlite:///db.sqlite3
```

### 4. Database Setup
```bash
# Apply migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Load sample data
python manage.py production_seed --reset-db --skip-confirmation
```

### 5. Run Development Server
```bash
# Start development server
python manage.py runserver

# Access application at http://localhost:8000
```

## Production Deployment

### Option 1: Deploy to Render

#### 1. Render Setup
```bash
# Connect GitHub repository to Render
# Configure environment variables in Render dashboard
# Deploy using build.sh script
```

#### 2. Render Configuration
```yaml
# render.yaml
services:
  - type: web
    name: cozywish
    runtime: python
    buildCommand: "./build.sh"
    startCommand: "gunicorn project_root.wsgi:application"
    plan: starter
    envVars:
      - key: DEBUG
        value: "False"
      - key: SECRET_KEY
        generateValue: true
      - key: DATABASE_URL
        fromDatabase:
          name: cozywish-db
          property: connectionString

databases:
  - name: cozywish-db
    plan: starter
```

### Option 2: Deploy to AWS EC2

#### 1. Launch EC2 Instance
```bash
# Launch Ubuntu 22.04 LTS instance
# Configure security groups (HTTP, HTTPS, SSH)
# Allocate Elastic IP
```

#### 2. Server Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install python3 python3-pip python3-venv nginx postgresql postgresql-contrib

# Create application user
sudo useradd -m -s /bin/bash cozywish
sudo usermod -aG sudo cozywish
```

#### 3. Application Setup
```bash
# Clone repository
sudo -u cozywish git clone https://github.com/yourusername/CozyWish.git /home/<USER>/app

# Setup virtual environment
sudo -u cozywish python3 -m venv /home/<USER>/app/venv

# Install dependencies
sudo -u cozywish /home/<USER>/app/venv/bin/pip install -r /home/<USER>/app/requirements.txt

# Configure environment
sudo -u cozywish cp /home/<USER>/app/.env.example /home/<USER>/app/.env
# Edit .env with production settings
```

#### 4. Database Setup
```bash
# Create database
sudo -u postgres createdb cozywish
sudo -u postgres createuser cozywish_user

# Apply migrations
sudo -u cozywish /home/<USER>/app/venv/bin/python /home/<USER>/app/manage.py migrate
```

#### 5. Web Server Configuration
```nginx
# /etc/nginx/sites-available/cozywish
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;

    location /static/ {
        alias /home/<USER>/app/staticfiles/;
    }

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### 6. Process Management
```bash
# Create systemd service
sudo tee /etc/systemd/system/cozywish.service > /dev/null <<EOF
[Unit]
Description=CozyWish Django Application
After=network.target

[Service]
Type=simple
User=cozywish
WorkingDirectory=/home/<USER>/app
ExecStart=/home/<USER>/app/venv/bin/gunicorn --workers 3 --bind 127.0.0.1:8000 project_root.wsgi:application
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Enable and start service
sudo systemctl enable cozywish
sudo systemctl start cozywish
```

### Option 3: Deploy with Docker

#### 1. Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

RUN python manage.py collectstatic --no-input

EXPOSE 8000

CMD ["gunicorn", "--bind", "0.0.0.0:8000", "project_root.wsgi:application"]
```

#### 2. Docker Compose
```yaml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    depends_on:
      - db
    environment:
      - DEBUG=False
      - DATABASE_URL=**************************************/cozywish
    volumes:
      - ./staticfiles:/app/staticfiles

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=cozywish
      - POSTGRES_USER=cozywish
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## Post-Deployment Setup

### 1. Initial Data Setup
```bash
# Create superuser
python manage.py createsuperuser

# Load initial data
python manage.py production_seed --minimal --skip-confirmation
```

### 2. SSL Certificate Setup
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo systemctl enable certbot.timer
```

### 3. Health Checks
```bash
# Test URL resolution
python manage.py test_urls --url-name=home

# Check database connectivity
python manage.py check --database

# Verify email configuration
python manage.py shell -c "from django.core.mail import send_mail; send_mail('Test', 'Test message', '<EMAIL>', ['<EMAIL>'])"
```

### 4. Performance Optimization
```bash
# Enable gzip compression
# Configure browser caching
# Optimize database queries
# Setup CDN for static files
```

## Monitoring and Maintenance

### 1. Log Configuration
```python
# settings/production.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/var/log/cozywish/django.log',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

### 2. Health Check Endpoint
```python
# Create health check view
def health_check(request):
    return JsonResponse({
        'status': 'healthy',
        'timestamp': timezone.now().isoformat(),
        'version': settings.APP_VERSION
    })
```

### 3. Database Maintenance
```bash
# Regular database backup
pg_dump cozywish > backup_$(date +%Y%m%d).sql

# Optimize database
python manage.py optimize_db

# Clean up old data
python manage.py cleanup_old_data
```

### 4. Security Monitoring
```bash
# Monitor failed login attempts
python manage.py monitor_security

# Check for security updates
pip list --outdated

# Review access logs
sudo tail -f /var/log/nginx/access.log
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Issues
```bash
# Check database connectivity
python manage.py check --database

# Verify DATABASE_URL format
echo $DATABASE_URL

# Test direct connection
psql $DATABASE_URL
```

#### 2. Static Files Not Loading
```bash
# Collect static files
python manage.py collectstatic --no-input

# Check static file configuration
python manage.py check --deploy

# Verify S3 permissions
aws s3 ls s3://your-bucket-name
```

#### 3. Email Not Sending
```bash
# Test email configuration
python manage.py shell -c "from django.core.mail import send_mail; send_mail('Test', 'Test', '<EMAIL>', ['<EMAIL>'])"

# Check SendGrid logs
# Verify API key permissions
# Check spam folder
```

#### 4. Application Errors
```bash
# Check application logs
tail -f /var/log/cozywish/django.log

# Debug mode (development only)
DEBUG=True python manage.py runserver

# Check system resources
htop
df -h
```

#### 5. Performance Issues
```bash
# Monitor database queries
python manage.py check --deploy

# Profile application
python manage.py profile_app

# Check server resources
iostat -x 1
```

### Debug Commands
```bash
# Check application status
python manage.py check

# Test URL patterns
python manage.py test_urls

# Validate models
python manage.py validate

# Check migrations
python manage.py showmigrations

# Django shell
python manage.py shell
```

### Recovery Procedures

#### 1. Database Recovery
```bash
# Restore from backup
psql cozywish < backup_20240101.sql

# Reset migrations (emergency)
python manage.py migrate --fake-initial
```

#### 2. Application Recovery
```bash
# Restart application
sudo systemctl restart cozywish

# Restart web server
sudo systemctl restart nginx

# Clear cache
python manage.py clear_cache
```

### Support Resources
- **Documentation**: Check project README and docs/
- **Logs**: Monitor application and server logs
- **Community**: Django community forums
- **Support**: Contact system administrator

---

*This deployment guide is regularly updated. For the latest information, check the project documentation or contact the development team.* 