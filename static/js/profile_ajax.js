/**
 * Profile AJAX Form Handler
 * 
 * Handles AJAX form submissions for customer and service provider profile updates
 * with real-time validation, error handling, and user feedback.
 */

class ProfileAjaxHandler {
    constructor(options = {}) {
        this.options = {
            customerFormSelector: '#customer-profile-form',
            providerFormSelector: '#provider-profile-form',
            customerAjaxUrl: '/accounts/ajax/customer/profile/update/',
            providerAjaxUrl: '/accounts/ajax/provider/profile/update/',
            fieldValidationUrl: '/accounts/ajax/validate-field/',
            loadingClass: 'loading',
            errorClass: 'is-invalid',
            successClass: 'is-valid',
            debounceDelay: 500,
            ...options
        };
        
        this.debounceTimers = {};
        this.init();
    }
    
    init() {
        this.bindFormEvents();
        this.bindFieldValidation();
        this.setupCSRFToken();
    }
    
    /**
     * Set up CSRF token for AJAX requests
     */
    setupCSRFToken() {
        const token = this.getCSRFToken();
        if (token) {
            // Set default CSRF token for all AJAX requests
            $.ajaxSetup({
                beforeSend: (xhr, settings) => {
                    if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.csrfSafeMethod(settings.type)) {
                        xhr.setRequestHeader('X-CSRFToken', token);
                    }
                }
            });
        }
    }
    
    /**
     * Get CSRF token from cookie or meta tag
     */
    getCSRFToken() {
        // Try to get from cookie first
        const cookieValue = this.getCookie('csrftoken');
        if (cookieValue) {
            return cookieValue;
        }
        
        // Fallback to meta tag
        const metaTag = document.querySelector('meta[name="csrf-token"]');
        return metaTag ? metaTag.getAttribute('content') : null;
    }
    
    /**
     * Get cookie value by name
     */
    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) {
            return parts.pop().split(';').shift();
        }
        return null;
    }
    
    /**
     * Check if HTTP method is CSRF safe
     */
    csrfSafeMethod(method) {
        return (/^(GET|HEAD|OPTIONS|TRACE)$/i.test(method));
    }
    
    /**
     * Bind form submission events
     */
    bindFormEvents() {
        const customerForm = $(this.options.customerFormSelector);
        const providerForm = $(this.options.providerFormSelector);
        
        if (customerForm.length) {
            customerForm.on('submit', (e) => this.handleFormSubmit(e, 'customer'));
        }
        
        if (providerForm.length) {
            providerForm.on('submit', (e) => this.handleFormSubmit(e, 'provider'));
        }
    }
    
    /**
     * Bind real-time field validation
     */
    bindFieldValidation() {
        const forms = $(`${this.options.customerFormSelector}, ${this.options.providerFormSelector}`);
        
        forms.find('input, textarea, select').on('blur change', (e) => {
            const field = $(e.target);
            const fieldName = field.attr('name');
            const fieldValue = field.val();
            
            if (fieldName && fieldValue) {
                this.validateField(field, fieldName, fieldValue);
            }
        });
        
        // Real-time validation on input with debouncing
        forms.find('input[type="text"], input[type="email"], input[type="tel"], textarea').on('input', (e) => {
            const field = $(e.target);
            const fieldName = field.attr('name');
            const fieldValue = field.val();
            
            if (fieldName && fieldValue) {
                this.debounceValidation(field, fieldName, fieldValue);
            }
        });
    }
    
    /**
     * Debounce field validation to avoid excessive API calls
     */
    debounceValidation(field, fieldName, fieldValue) {
        // Clear existing timer for this field
        if (this.debounceTimers[fieldName]) {
            clearTimeout(this.debounceTimers[fieldName]);
        }
        
        // Set new timer
        this.debounceTimers[fieldName] = setTimeout(() => {
            this.validateField(field, fieldName, fieldValue);
        }, this.options.debounceDelay);
    }
    
    /**
     * Validate individual field via AJAX
     */
    validateField(field, fieldName, fieldValue) {
        const form = field.closest('form');
        const profileType = form.hasClass('customer-profile') ? 'customer' : 'service_provider';
        
        const data = {
            field_name: fieldName,
            field_value: fieldValue,
            profile_type: profileType,
            csrfmiddlewaretoken: this.getCSRFToken()
        };
        
        $.ajax({
            url: this.options.fieldValidationUrl,
            type: 'POST',
            data: data,
            success: (response) => {
                if (response.success) {
                    this.showFieldSuccess(field);
                } else {
                    this.showFieldErrors(field, response.errors || [response.message]);
                }
            },
            error: (xhr) => {
                console.error('Field validation error:', xhr.responseText);
                this.clearFieldValidation(field);
            }
        });
    }
    
    /**
     * Handle form submission
     */
    handleFormSubmit(event, profileType) {
        event.preventDefault();
        
        const form = $(event.target);
        const url = profileType === 'customer' ? this.options.customerAjaxUrl : this.options.providerAjaxUrl;
        
        // Show loading state
        this.showFormLoading(form);
        this.clearFormErrors(form);
        
        // Prepare form data
        const formData = new FormData(form[0]);
        
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (response) => {
                this.handleSubmitSuccess(form, response);
            },
            error: (xhr) => {
                this.handleSubmitError(form, xhr);
            },
            complete: () => {
                this.hideFormLoading(form);
            }
        });
    }
    
    /**
     * Handle successful form submission
     */
    handleSubmitSuccess(form, response) {
        if (response.success) {
            this.showSuccessMessage(response.message);
            
            // Update UI with new data if provided
            if (response.data) {
                if (response.data.profile_picture_url) {
                    this.updateProfilePicture(response.data.profile_picture_url);
                }
                if (response.data.logo_url) {
                    this.updateBusinessLogo(response.data.logo_url);
                }
            }
            
            // Optionally redirect or update page
            if (response.redirect_url) {
                window.location.href = response.redirect_url;
            }
        } else {
            this.showErrorMessage(response.message || 'An error occurred');
            if (response.errors) {
                this.showFormErrors(form, response.errors);
            }
        }
    }
    
    /**
     * Handle form submission error
     */
    handleSubmitError(form, xhr) {
        let errorMessage = 'An unexpected error occurred. Please try again.';
        
        if (xhr.responseJSON) {
            errorMessage = xhr.responseJSON.message || errorMessage;
            if (xhr.responseJSON.errors) {
                this.showFormErrors(form, xhr.responseJSON.errors);
            }
        }
        
        this.showErrorMessage(errorMessage);
    }
    
    /**
     * Show form loading state
     */
    showFormLoading(form) {
        form.addClass(this.options.loadingClass);
        form.find('button[type="submit"]').prop('disabled', true).addClass('loading');
        
        // Add spinner to submit button
        const submitBtn = form.find('button[type="submit"]');
        if (!submitBtn.find('.spinner').length) {
            submitBtn.prepend('<span class="spinner spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>');
        }
    }
    
    /**
     * Hide form loading state
     */
    hideFormLoading(form) {
        form.removeClass(this.options.loadingClass);
        form.find('button[type="submit"]').prop('disabled', false).removeClass('loading');
        form.find('.spinner').remove();
    }
    
    /**
     * Show form validation errors
     */
    showFormErrors(form, errors) {
        for (const fieldName in errors) {
            const field = form.find(`[name="${fieldName}"]`);
            if (field.length) {
                this.showFieldErrors(field, errors[fieldName]);
            }
        }
        
        // Show general errors
        if (errors.__all__) {
            this.showErrorMessage(errors.__all__.join(', '));
        }
    }
    
    /**
     * Clear form errors
     */
    clearFormErrors(form) {
        form.find('.is-invalid').removeClass('is-invalid');
        form.find('.invalid-feedback').hide();
        form.find('.alert-danger').hide();
    }
    
    /**
     * Show field errors
     */
    showFieldErrors(field, errors) {
        field.addClass(this.options.errorClass).removeClass(this.options.successClass);
        
        // Find or create error message element
        let errorElement = field.next('.invalid-feedback');
        if (!errorElement.length) {
            errorElement = $('<div class="invalid-feedback"></div>');
            field.after(errorElement);
        }
        
        // Show errors
        const errorMessages = Array.isArray(errors) ? errors : [errors];
        errorElement.html(errorMessages.join('<br>')).show();
    }
    
    /**
     * Show field success state
     */
    showFieldSuccess(field) {
        field.addClass(this.options.successClass).removeClass(this.options.errorClass);
        field.next('.invalid-feedback').hide();
    }
    
    /**
     * Clear field validation state
     */
    clearFieldValidation(field) {
        field.removeClass(`${this.options.errorClass} ${this.options.successClass}`);
        field.next('.invalid-feedback').hide();
    }
    
    /**
     * Show success message
     */
    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }
    
    /**
     * Show error message
     */
    showErrorMessage(message) {
        this.showMessage(message, 'danger');
    }
    
    /**
     * Show message with specified type
     */
    showMessage(message, type = 'info') {
        // Remove existing messages
        $('.alert-ajax').remove();
        
        // Create new message
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show alert-ajax" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        // Show message at top of page or in a specific container
        const container = $('#messages-container').length ? $('#messages-container') : $('body');
        container.prepend(alertHtml);
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            $('.alert-ajax').fadeOut();
        }, 5000);
    }
    
    /**
     * Update profile picture in UI
     */
    updateProfilePicture(url) {
        const profileImages = $('.profile-picture, .profile-image');
        if (profileImages.length && url) {
            profileImages.attr('src', url);
        }
    }
    
    /**
     * Update business logo in UI
     */
    updateBusinessLogo(url) {
        const logoImages = $('.business-logo, .logo-image');
        if (logoImages.length && url) {
            logoImages.attr('src', url);
        }
    }
}

// Initialize when DOM is ready
$(document).ready(function() {
    // Initialize profile AJAX handler
    new ProfileAjaxHandler();
    
    // Add form classes for identification
    $('#customer-profile-form').addClass('customer-profile');
    $('#provider-profile-form').addClass('service-provider-profile');
});

// Export for use in other modules
window.ProfileAjaxHandler = ProfileAjaxHandler; 