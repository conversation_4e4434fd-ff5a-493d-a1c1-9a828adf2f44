"""
Core Functionality Tests for accounts_app

These tests focus on the core functionality that is guaranteed to work
without any template or URL dependencies.
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from accounts_app.models import CustomerProfile, ServiceProviderProfile
from accounts_app.constants import UserRoles, UserStatus

User = get_user_model()


class CoreFunctionalityTest(TestCase):
    """Test core MVP functionality for accounts_app."""
    
    def setUp(self):
        """Set up test data."""
        self.client.logout()
        
    def create_customer_user(self, **kwargs):
        """Create a test customer user."""
        defaults = {
            'email': '<EMAIL>',
            'password': 'TestPass123!',
            'role': UserRoles.CUSTOMER,
            'status': UserStatus.ACTIVE,
            'email_verified': True,
        }
        defaults.update(kwargs)
        
        user = User.objects.create_user(**defaults)
        CustomerProfile.objects.create(user=user)
        return user
    
    def create_provider_user(self, **kwargs):
        """Create a test service provider user."""
        defaults = {
            'email': '<EMAIL>',
            'password': 'TestPass123!',
            'role': UserRoles.SERVICE_PROVIDER,
            'status': UserStatus.ACTIVE,
            'email_verified': True,
        }
        defaults.update(kwargs)
        
        user = User.objects.create_user(**defaults)
        ServiceProviderProfile.objects.create(
            user=user,
            legal_name='Test Business',
            contact_name='Test Contact',
            address='123 Test St',
            city='Test City',
            state='CA',
            zip_code='90210',
            phone='+***********'
        )
        return user
    
    def test_user_creation(self):
        """Test that users can be created with profiles."""
        # Test customer creation
        customer = self.create_customer_user()
        self.assertEqual(customer.role, UserRoles.CUSTOMER)
        self.assertTrue(hasattr(customer, 'customer_profile'))
        
        # Test provider creation
        provider = self.create_provider_user()
        self.assertEqual(provider.role, UserRoles.SERVICE_PROVIDER)
        self.assertTrue(hasattr(provider, 'service_provider_profile'))
    
    def test_user_model_functionality(self):
        """Test basic user model functionality."""
        # Test user creation
        user = User.objects.create_user(
            email='<EMAIL>',
            password='TestPass123!',
            role=UserRoles.CUSTOMER
        )
        
        # Test user properties
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.role, UserRoles.CUSTOMER)
        self.assertTrue(user.check_password('TestPass123!'))
        
        # Test user manager
        self.assertTrue(User.objects.filter(email='<EMAIL>').exists())
    
    def test_profile_creation(self):
        """Test that profiles are created correctly."""
        # Test customer profile
        customer = self.create_customer_user()
        self.assertIsNotNone(customer.customer_profile)
        self.assertEqual(customer.customer_profile.user, customer)
        
        # Test provider profile
        provider = self.create_provider_user()
        self.assertIsNotNone(provider.service_provider_profile)
        self.assertEqual(provider.service_provider_profile.user, provider)
        self.assertEqual(provider.service_provider_profile.legal_name, 'Test Business')
    
    def test_user_status_functionality(self):
        """Test user status functionality."""
        # Test active user
        active_user = self.create_customer_user(status=UserStatus.ACTIVE)
        self.assertEqual(active_user.status, UserStatus.ACTIVE)
        
        # Test inactive user
        inactive_user = self.create_customer_user(
            email='<EMAIL>',
            status=UserStatus.INACTIVE
        )
        self.assertEqual(inactive_user.status, UserStatus.INACTIVE)
        
        # Test suspended user
        suspended_user = self.create_customer_user(
            email='<EMAIL>',
            status=UserStatus.SUSPENDED
        )
        self.assertEqual(suspended_user.status, UserStatus.SUSPENDED)
    
    def test_user_roles_functionality(self):
        """Test user roles functionality."""
        # Test customer role
        customer = self.create_customer_user()
        self.assertEqual(customer.role, UserRoles.CUSTOMER)
        
        # Test provider role
        provider = self.create_provider_user()
        self.assertEqual(provider.role, UserRoles.SERVICE_PROVIDER)
        
        # Test admin role
        admin = User.objects.create_user(
            email='<EMAIL>',
            password='TestPass123!',
            role=UserRoles.ADMIN
        )
        self.assertEqual(admin.role, UserRoles.ADMIN)
    
    def test_authentication_basic(self):
        """Test basic authentication functionality."""
        # Create a user
        user = self.create_customer_user()
        
        # Test login using Django's built-in authentication
        login_successful = self.client.login(email='<EMAIL>', password='TestPass123!')
        self.assertTrue(login_successful)
        
        # Test that user is logged in
        self.assertTrue(self.client.session.get('_auth_user_id'))
        
        # Test logout
        self.client.logout()
        self.assertNotIn('_auth_user_id', self.client.session)
    
    def test_provider_authentication_basic(self):
        """Test basic provider authentication functionality."""
        # Create a provider user
        user = self.create_provider_user()
        
        # Test login using Django's built-in authentication
        login_successful = self.client.login(email='<EMAIL>', password='TestPass123!')
        self.assertTrue(login_successful)
        
        # Test that user is logged in
        self.assertTrue(self.client.session.get('_auth_user_id'))
        
        # Test logout
        self.client.logout()
        self.assertNotIn('_auth_user_id', self.client.session)
    
    def test_user_managers(self):
        """Test custom user managers."""
        # Test customer manager
        customer = User.customers.create_user(
            email='<EMAIL>',
            password='TestPass123!',
            role=UserRoles.CUSTOMER
        )
        self.assertEqual(customer.role, UserRoles.CUSTOMER)
        
        # Test provider manager
        provider = User.providers.create_user(
            email='<EMAIL>',
            password='TestPass123!',
            role=UserRoles.SERVICE_PROVIDER
        )
        self.assertEqual(provider.role, UserRoles.SERVICE_PROVIDER)
        
        # Test admin manager
        admin = User.admins.create_user(
            email='<EMAIL>',
            password='TestPass123!',
            role=UserRoles.ADMIN
        )
        self.assertEqual(admin.role, UserRoles.ADMIN)
    
    def test_profile_relationships(self):
        """Test profile relationships and properties."""
        # Test customer profile
        customer = self.create_customer_user()
        profile = customer.customer_profile
        
        # Test profile properties
        self.assertEqual(profile.user, customer)
        self.assertIsNotNone(profile.created_at)
        self.assertIsNotNone(profile.updated_at)
        
        # Test provider profile
        provider = self.create_provider_user()
        provider_profile = provider.service_provider_profile
        
        # Test provider profile properties
        self.assertEqual(provider_profile.user, provider)
        self.assertEqual(provider_profile.business_name, 'Test Business')
        self.assertEqual(provider_profile.full_address, '123 Test St, Test City, CA 90210')
        self.assertIsNotNone(provider_profile.created)
        self.assertIsNotNone(provider_profile.updated)
    
    def test_user_validation(self):
        """Test user validation and constraints."""
        # Test unique email constraint
        user1 = self.create_customer_user(email='<EMAIL>')
        
        # Try to create another user with same email
        with self.assertRaises(Exception):  # Should raise IntegrityError or similar
            user2 = self.create_customer_user(email='<EMAIL>')
        
        # Test email validation
        with self.assertRaises(Exception):
            User.objects.create_user(
                email='invalid-email',
                password='TestPass123!',
                role=UserRoles.CUSTOMER
            )
    
    def test_profile_validation(self):
        """Test profile validation."""
        # Test provider profile required fields
        user = User.objects.create_user(
            email='<EMAIL>',
            password='TestPass123!',
            role=UserRoles.SERVICE_PROVIDER
        )
        
        # Test that creating a profile with minimal required fields works
        profile = ServiceProviderProfile.objects.create(
            user=user,
            legal_name='Test Business',
            contact_name='Test Contact',
            address='123 Test St',
            city='Test City',
            state='CA',
            zip_code='90210',
            phone='+***********'
        )
        self.assertIsNotNone(profile)
        self.assertEqual(profile.user, user) 