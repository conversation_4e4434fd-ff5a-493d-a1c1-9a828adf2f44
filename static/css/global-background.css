/**
 * Global Background Color Override
 * Ensures #F8F9FA background color is applied consistently across the entire CozyWish application
 */

/* Force background color on all key elements */
html {
    background-color: #F8F9FA !important;
    background: #F8F9FA !important;
}

body {
    background-color: #F8F9FA !important;
    background: #F8F9FA !important;
}

/* Main content area */
main {
    background-color: #F8F9FA !important;
    background: #F8F9FA !important;
}

/* Container elements */
.container {
    background-color: transparent !important;
}

.container-fluid {
    background-color: transparent !important;
}

/* Ensure the background is applied to the entire viewport */
#root, 
[data-authenticated] {
    background-color: #F8F9FA !important;
    background: #F8F9FA !important;
    min-height: 100vh;
}

/* Override any conflicting background styles */
*[style*="background: white"]:not(.card):not(.btn):not(.navbar):not(.dropdown-menu):not(.toast):not(.modal-content):not(.form-control) {
    background-color: #F8F9FA !important;
}

/* Ensure proper contrast for content cards */
.card,
.card-cw,
.alert,
.toast,
.modal-content,
.dropdown-menu,
.navbar {
    background-color: white !important;
}

/* Form elements should remain white for readability */
.form-control,
.form-control-cw,
input,
textarea,
select {
    background-color: white !important;
}

/* Buttons should maintain their original styling */
.btn,
.btn-cw-primary,
.btn-cw-secondary {
    background-color: initial !important;
} 