/* Booking Cart App - Customer Profiles Styles */

/* Customer Profiles Container */
.customer-profiles-container {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
}

/* Profiles Header */
.profiles-header {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

/* Search Bar */
.search-bar {
    max-width: 400px;
    margin-bottom: 20px;
}

.search-input {
    border-radius: 20px;
    border: 2px solid #e5e7eb;
    padding: 12px 20px;
    font-size: 0.9rem;
    transition: all 0.2s;
}

.search-input:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Profiles Grid */
.profiles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

/* Customer Card */
.customer-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    transition: all 0.3s;
    border: 2px solid transparent;
    position: relative;
}

.customer-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #4f46e5;
}

/* Customer Header */
.customer-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
}

.customer-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
    flex-shrink: 0;
}

.customer-info h5 {
    margin: 0 0 4px 0;
    color: #111827;
    font-size: 1.1rem;
    font-weight: 600;
}

.customer-email {
    color: #6b7280;
    font-size: 0.9rem;
    margin: 0;
}

/* Loyalty Badge */
.loyalty-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.loyalty-badge.gold {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    color: white;
}

.loyalty-badge.silver {
    background: linear-gradient(135deg, #d1d5db 0%, #9ca3af 100%);
    color: white;
}

.loyalty-badge.bronze {
    background: linear-gradient(135deg, #92400e 0%, #78350f 100%);
    color: white;
}

.loyalty-badge.new {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

/* Customer Stats */
.customer-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-bottom: 16px;
}

.stat-item {
    text-align: center;
    padding: 12px;
    background: #f9fafb;
    border-radius: 8px;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #4f46e5;
    margin-bottom: 2px;
}

.stat-label {
    font-size: 0.8rem;
    color: #6b7280;
}

/* Customer Preferences */
.customer-preferences {
    margin-bottom: 16px;
}

.preferences-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.favorite-service {
    background: #ede9fe;
    color: #6d28d9;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 0.8rem;
    display: inline-block;
    margin-bottom: 8px;
}

.last-visit {
    font-size: 0.8rem;
    color: #6b7280;
    margin-bottom: 16px;
}

/* Customer Actions */
.customer-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    background: white;
    color: #374151;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    text-align: center;
}

.action-btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
    text-decoration: none;
    color: #374151;
}

.action-btn.primary {
    background: #4f46e5;
    color: white;
    border-color: #4f46e5;
}

.action-btn.primary:hover {
    background: #4338ca;
    border-color: #4338ca;
    color: white;
}

/* Loyalty Progress */
.loyalty-progress {
    background: #f3f4f6;
    border-radius: 10px;
    height: 8px;
    margin-bottom: 8px;
    overflow: hidden;
}

.loyalty-progress-bar {
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.loyalty-progress-bar.gold {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

.loyalty-progress-bar.silver {
    background: linear-gradient(135deg, #d1d5db 0%, #9ca3af 100%);
}

.loyalty-progress-bar.bronze {
    background: linear-gradient(135deg, #92400e 0%, #78350f 100%);
}

.loyalty-progress-bar.new {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

.empty-icon {
    font-size: 4rem;
    color: #d1d5db;
    margin-bottom: 20px;
}

/* Pagination */
.pagination-wrapper {
    margin-top: 40px;
    display: flex;
    justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .profiles-grid {
        grid-template-columns: 1fr;
    }
    
    .customer-stats {
        grid-template-columns: 1fr;
    }
    
    .customer-actions {
        flex-direction: column;
    }
} 