{% extends 'accounts_app/base_account.html' %}

{% block title %}Sign Up with <PERSON>zy<PERSON>ish{% endblock %}

{% block account_extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

{% endblock %}

{% block content %}
<section class="signup-section">
    <div class="signup-container">
        <div class="signup-card">
            <div class="signup-header account-header">
                <div class="content">
                    <div class="signup-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h1 class="signup-title">Join <PERSON><PERSON></h1>
                    <p class="signup-subtitle">Create your account</p>
                </div>
            </div>

            <div class="signup-body account-wrapper">
                <form method="post" class="d-flex flex-column">
                  {% csrf_token %}

                  {% if form.non_field_errors %}
                  <div class="alert alert-danger mb-3" role="alert" aria-live="polite">
                    {% for error in form.non_field_errors %}
                    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}

                  <!-- Email field -->
                  <div class="mb-3">
                    <label for="{{ form.email.id_for_label }}" class="form-label">
                      <i class="fas fa-envelope"></i>{{ form.email.label }}
                    </label>
                    {% if form.email.errors %}
                      {{ form.email|add_class:"form-control-cw is-invalid"|attr:"placeholder:Enter your email address" }}
                      <div class="invalid-feedback" role="alert" aria-live="polite">
                        {% for error in form.email.errors %}
                        <i class="fas fa-exclamation-circle"></i>{{ error }}
                        {% endfor %}
                      </div>
                    {% else %}
                      {{ form.email|add_class:"form-control-cw"|attr:"placeholder:Enter your email address" }}
                    {% endif %}
                    {% if form.email.help_text %}
                    <div class="form-text">{{ form.email.help_text }}</div>
                    {% endif %}
                  </div>

                  <!-- Password field -->
                  <div class="mb-3">
                    <label for="{{ form.password1.id_for_label }}" class="form-label">
                      <i class="fas fa-lock"></i>{{ form.password1.label }}
                    </label>
                    <div class="input-group">
                      {% if form.password1.errors %}
                        {{ form.password1|add_class:"form-control-cw is-invalid"|attr:"placeholder:Create a strong password" }}
                      {% else %}
                        {{ form.password1|add_class:"form-control-cw"|attr:"placeholder:Create a strong password" }}
                      {% endif %}
                      <button type="button" class="toggle-password" data-target="#{{ form.password1.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                        <i class="fas fa-eye" aria-hidden="true"></i>
                      </button>
                    </div>
                    {% if form.password1.errors %}
                      <div class="invalid-feedback" role="alert" aria-live="polite">
                        {% for error in form.password1.errors %}
                        <i class="fas fa-exclamation-circle"></i>{{ error }}
                        {% endfor %}
                      </div>
                    {% endif %}
                    {% if form.password1.help_text %}
                    <div class="form-text">{{ form.password1.help_text }}</div>
                    {% endif %}
                  </div>

                  <!-- Confirm Password field -->
                  <div class="mb-3">
                    <label for="{{ form.password2.id_for_label }}" class="form-label">
                      <i class="fas fa-lock"></i>{{ form.password2.label }}
                    </label>
                    <div class="input-group">
                      {% if form.password2.errors %}
                        {{ form.password2|add_class:"form-control-cw is-invalid"|attr:"placeholder:Confirm your password" }}
                      {% else %}
                        {{ form.password2|add_class:"form-control-cw"|attr:"placeholder:Confirm your password" }}
                      {% endif %}
                      <button type="button" class="toggle-password" data-target="#{{ form.password2.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                        <i class="fas fa-eye" aria-hidden="true"></i>
                      </button>
                    </div>
                    {% if form.password2.errors %}
                      <div class="invalid-feedback" role="alert" aria-live="polite">
                        {% for error in form.password2.errors %}
                        <i class="fas fa-exclamation-circle"></i>{{ error }}
                        {% endfor %}
                      </div>
                    {% endif %}
                    {% if form.password2.help_text %}
                    <div class="form-text">{{ form.password2.help_text }}</div>
                    {% endif %}
                  </div>

                  <!-- Terms checkbox -->
                  <div class="form-check mb-3">
                    {% if form.agree_to_terms.errors %}
                      {{ form.agree_to_terms|add_class:"form-check-input is-invalid" }}
                      <div class="invalid-feedback" role="alert" aria-live="polite">
                        {% for error in form.agree_to_terms.errors %}
                        <i class="fas fa-exclamation-circle"></i>{{ error }}
                        {% endfor %}
                      </div>
                    {% else %}
                      {{ form.agree_to_terms|add_class:"form-check-input" }}
                    {% endif %}
                    <label class="form-check-label" for="{{ form.agree_to_terms.id_for_label }}">
                      I agree to the <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a>
                    </label>
                  </div>

                  <!-- Submit button -->
                  <div class="d-grid mb-3">
                    <button type="submit" class="btn btn-cw-primary">
                      <i class="fas fa-user-plus me-2"></i>Create Account
                    </button>
                  </div>
                </form>

                <!-- Login and business links -->
                <div class="text-center">
                  <p class="mb-3">Already have an account?</p>
                  <div class="d-grid mb-3">
                    <a href="{% url 'accounts_app:customer_login' %}" class="btn btn-cw-secondary">
                      <i class="fas fa-sign-in-alt me-2"></i>Sign In
                    </a>
                  </div>

                  <p class="mb-3">Are you a service provider?</p>
                  <div class="d-grid">
                    <a href="{% url 'accounts_app:for_business' %}" class="btn btn-cw-ghost">
                      <i class="fas fa-store me-2"></i>For Business
                    </a>
                  </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Password Toggle JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    // Password toggle functionality
    function togglePassword(toggleBtn) {
        const targetSelector = toggleBtn.getAttribute('data-target');
        if (!targetSelector) {
            console.error('No data-target attribute found on toggle button');
            return;
        }

        const input = document.querySelector(targetSelector);
        if (!input) {
            console.error('Target input not found:', targetSelector);
            return;
        }

        // Toggle password visibility
        const isPassword = input.type === 'password';
        input.type = isPassword ? 'text' : 'password';

        // Update icon
        const icon = toggleBtn.querySelector('i');
        if (icon) {
            if (isPassword) {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Update accessibility attributes
        const newTitle = isPassword ? 'Hide password' : 'Show password';
        toggleBtn.title = newTitle;
        toggleBtn.setAttribute('aria-label', newTitle);
    }

    // Click event handler
    document.addEventListener('click', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn) {
            e.preventDefault();
            e.stopPropagation();
            togglePassword(toggleBtn);
        }
    });

    // Keyboard event handler for accessibility
    document.addEventListener('keydown', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn && (e.key === 'Enter' || e.key === ' ' || e.key === 'Spacebar')) {
            e.preventDefault();
            e.stopPropagation();
            togglePassword(toggleBtn);
        }
    });

    // Initialize toggle buttons
    const toggleButtons = document.querySelectorAll('.toggle-password');
    toggleButtons.forEach(function(btn) {
        // Ensure button has proper attributes
        if (!btn.hasAttribute('tabindex')) {
            btn.setAttribute('tabindex', '0');
        }
        
        // Add role for screen readers
        btn.setAttribute('role', 'button');
        
        // Ensure aria-label is set
        if (!btn.hasAttribute('aria-label')) {
            btn.setAttribute('aria-label', 'Toggle password visibility');
        }
    });
});
</script>
{% endblock %}
