# CozyWish Email Templates Documentation

## Overview

This directory contains all email templates for the CozyWish platform. The templates are designed with a consistent design system and are optimized for both desktop and mobile email clients.

## Template Structure

### Base Template
- **`base_email.html`**: The main template that all other email templates extend
- Includes consistent branding, styles, and responsive design
- Provides reusable blocks for customization

### Core Email Templates
- **`welcome.html`**: Unified welcome template for both customers and providers
- **`password_reset.html`**: Password reset email template
- **`email_verification.html`**: Email verification template
- **`welcome_customer.html`**: Legacy customer welcome template
- **`welcome_provider.html`**: Legacy provider welcome template

### Specialized Templates
- **`booking_confirmation.html`**: Booking confirmation emails
- **`payment_confirmation.html`**: Payment confirmation emails
- **`review_response.html`**: Review response notifications
- **`new_review.html`**: New review notifications

## Configuration

### Email Settings (production.py)

```python
# SendGrid Configuration
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.sendgrid.net'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = 'apikey'
EMAIL_HOST_PASSWORD = 'your-sendgrid-api-key'

# Email Addresses
DEFAULT_FROM_EMAIL = '<EMAIL>'
SERVER_EMAIL = '<EMAIL>'
SUPPORT_EMAIL = '<EMAIL>'

# Template Configuration
EMAIL_TEMPLATE_DIR = 'emails'
EMAIL_TEMPLATE_CONTEXT = {
    'site_name': 'CozyWish',
    'site_url': 'https://cozywish.com',
    'support_email': '<EMAIL>',
    'company_name': 'CozyWish',
    'company_address': 'Your Company Address',
    'unsubscribe_url': 'https://cozywish.com/unsubscribe',
}

# SendGrid Features
SENDGRID_TRACK_CLICKS = True
SENDGRID_TRACK_OPENS = True
SENDGRID_SANDBOX_MODE = False  # Set to True for testing
```

### Environment Variables

Required:
- `EMAIL_HOST_PASSWORD` or `SENDGRID_API_KEY`: Your SendGrid API key

Optional:
- `DEFAULT_FROM_EMAIL`: Default sender email (default: <EMAIL>)
- `SUPPORT_EMAIL`: Support email address (default: <EMAIL>)
- `SENDGRID_TRACK_CLICKS`: Enable click tracking (default: True)
- `SENDGRID_TRACK_OPENS`: Enable open tracking (default: True)
- `EMAIL_SUBJECT_PREFIX`: Subject prefix for all emails (default: [CozyWish])

## Usage Examples

### 1. Sending Welcome Email

```python
from django.template.loader import render_to_string
from django.core.mail import EmailMultiAlternatives
from django.conf import settings

def send_welcome_email(user, user_type='customer'):
    context = {
        'user': user,
        'user_type': user_type,
        'dashboard_url': f"{settings.EMAIL_TEMPLATE_CONTEXT['site_url']}/dashboard",
        'help_url': f"{settings.EMAIL_TEMPLATE_CONTEXT['site_url']}/help",
        **settings.EMAIL_TEMPLATE_CONTEXT
    }
    
    subject = f"Welcome to {settings.EMAIL_TEMPLATE_CONTEXT['site_name']}"
    html_content = render_to_string('emails/welcome.html', context)
    
    email = EmailMultiAlternatives(
        subject=subject,
        body='',  # Plain text version can be added
        from_email=settings.DEFAULT_FROM_EMAIL,
        to=[user.email]
    )
    email.attach_alternative(html_content, "text/html")
    email.send()
```

### 2. Sending Password Reset Email

```python
def send_password_reset_email(user, reset_url):
    context = {
        'user': user,
        'reset_url': reset_url,
        'timestamp': timezone.now(),
        **settings.EMAIL_TEMPLATE_CONTEXT
    }
    
    subject = f"{settings.EMAIL_SUBJECT_PREFIX}Password Reset"
    html_content = render_to_string('emails/password_reset.html', context)
    
    email = EmailMultiAlternatives(
        subject=subject,
        body='',
        from_email=settings.NO_REPLY_EMAIL,
        to=[user.email]
    )
    email.attach_alternative(html_content, "text/html")
    email.send()
```

### 3. Sending Email Verification

```python
def send_verification_email(user, verification_url, verification_code=None):
    context = {
        'user': user,
        'verification_url': verification_url,
        'verification_code': verification_code,
        **settings.EMAIL_TEMPLATE_CONTEXT
    }
    
    subject = f"Verify Your {settings.EMAIL_TEMPLATE_CONTEXT['site_name']} Account"
    html_content = render_to_string('emails/email_verification.html', context)
    
    email = EmailMultiAlternatives(
        subject=subject,
        body='',
        from_email=settings.NO_REPLY_EMAIL,
        to=[user.email]
    )
    email.attach_alternative(html_content, "text/html")
    email.send()
```

## Template Customization

### Extending Base Template

```html
{% extends "emails/base_email.html" %}

{% block email_title %}Your Custom Email Title{% endblock %}

{% block header_gradient %}linear-gradient(135deg, #your-color1, #your-color2){% endblock %}
{% block button_gradient %}linear-gradient(135deg, #your-color1, #your-color2){% endblock %}

{% block header_icon %}🎉{% endblock %}
{% block header_title %}Custom Header Title{% endblock %}
{% block header_subtitle %}Custom subtitle here{% endblock %}

{% block custom_styles %}
.your-custom-class {
    /* Your custom styles */
}
{% endblock %}

{% block email_content %}
<p>Your email content here</p>
{% endblock %}

{% block action_section %}
<div style="text-align: center; margin: 30px 0;">
    <a href="{{ your_action_url }}" class="action-button">
        Your Action Button
    </a>
</div>
{% endblock %}

{% block details_section %}
<!-- Your details section -->
{% endblock %}

{% block additional_content %}
<!-- Additional content -->
{% endblock %}
```

### Available Template Blocks

- **`email_title`**: Email title in the `<title>` tag
- **`header_gradient`**: CSS gradient for header background
- **`button_gradient`**: CSS gradient for action buttons
- **`header_icon`**: Icon displayed in the header
- **`header_title`**: Main header title
- **`header_subtitle`**: Header subtitle
- **`custom_styles`**: Custom CSS styles
- **`email_content`**: Main email content
- **`action_section`**: Call-to-action buttons
- **`details_section`**: Detailed information sections
- **`additional_content`**: Additional content before footer

## Design System

### Colors
- **Primary**: #2F160F (Dark brown)
- **Primary Light**: #4a2a1f
- **Accent**: #fae1d7 (Light peach)
- **Accent Light**: #fef7f0
- **Success**: #16a34a (Green)
- **Warning**: #d97706 (Orange)
- **Error**: #dc2626 (Red)
- **Info**: #0284c7 (Blue)

### Typography
- **Primary Font**: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto
- **Heading Font**: 'Playfair Display', Georgia, 'Times New Roman', serif
- **Monospace Font**: 'Courier New', monospace

### Components

#### Info Boxes
```html
<div class="info-box">Default info box</div>
<div class="info-box success">Success message</div>
<div class="info-box warning">Warning message</div>
<div class="info-box error">Error message</div>
<div class="info-box info">Info message</div>
```

#### Action Buttons
```html
<a href="{{ url }}" class="action-button">Action Button</a>
```

#### Details Lists
```html
<ul class="details-list">
    <li>
        <span class="details-label">Label:</span>
        <span class="details-value">Value</span>
    </li>
</ul>
```

## Testing

### Email Testing in Development

```python
# In development.py
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
```

### SendGrid Sandbox Mode

```python
# For testing without sending real emails
SENDGRID_SANDBOX_MODE = True
```

### Email Preview

Create a management command to preview emails:

```python
# management/commands/preview_email.py
from django.core.management.base import BaseCommand
from django.template.loader import render_to_string

class Command(BaseCommand):
    def handle(self, *args, **options):
        context = {
            'user': {'email': '<EMAIL>', 'first_name': 'Test'},
            'user_type': 'customer',
            'site_name': 'CozyWish',
            'site_url': 'https://cozywish.com',
        }
        
        html_content = render_to_string('emails/welcome.html', context)
        
        with open('email_preview.html', 'w') as f:
            f.write(html_content)
        
        self.stdout.write('Preview saved to email_preview.html')
```

## Best Practices

### 1. Email Client Compatibility
- Use inline CSS for critical styles
- Test across major email clients (Gmail, Outlook, Apple Mail)
- Use table-based layouts for better compatibility
- Provide fallback fonts

### 2. Content Guidelines
- Keep subject lines under 50 characters
- Use clear, actionable language
- Include plain text alternatives
- Provide unsubscribe links where required

### 3. Performance
- Optimize images for email
- Keep email size under 100KB
- Use web-safe fonts
- Minimize CSS complexity

### 4. Security
- Validate all user input in email templates
- Use HTTPS for all links
- Implement proper email authentication (SPF, DKIM)
- Monitor bounce rates and spam reports

## Troubleshooting

### Common Issues

1. **Images not loading**: Ensure images are hosted on HTTPS and are publicly accessible
2. **Styles not applying**: Use inline styles for critical formatting
3. **SendGrid authentication errors**: Check API key and permissions
4. **High bounce rates**: Verify email addresses before sending

### Debug Commands

```bash
# Test email configuration
python manage.py shell
>>> from django.core.mail import send_mail
>>> send_mail('Test', 'Test message', '<EMAIL>', ['<EMAIL>'])

# Check SendGrid configuration
python manage.py shell
>>> from django.conf import settings
>>> print(settings.EMAIL_HOST)
>>> print(settings.EMAIL_HOST_USER)
```

## Monitoring

### SendGrid Analytics
- Track email opens and clicks
- Monitor delivery rates
- Review spam reports
- Analyze user engagement

### Django Logging
```python
LOGGING = {
    'loggers': {
        'django.core.mail': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
    },
}
```

## Support

For questions or issues with the email templates:
- Check the troubleshooting section above
- Review SendGrid documentation
- Contact the development team
- Test in SendGrid sandbox mode first 