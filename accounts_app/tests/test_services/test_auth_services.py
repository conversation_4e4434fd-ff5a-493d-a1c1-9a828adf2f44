"""
Comprehensive tests for authentication services in accounts_app.
"""

import pytest
from django.contrib.auth import get_user_model
from django.test import TestCase, RequestFactory
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from unittest.mock import patch, MagicMock

from accounts_app.constants import UserRoles, UserStatus
from accounts_app.services import (
    AuthenticationService, EmailVerificationService, PasswordService,
    AccountService, EmailService
)
from accounts_app.models import (
    UserSecurity, EmailVerificationToken, CustomerProfile
)

User = get_user_model()


class AuthenticationServiceTests(TestCase):
    """Test cases for AuthenticationService"""

    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER,
            status=UserStatus.ACTIVE,
            email_verified=True
        )
        
        self.auth_service = AuthenticationService()

    def test_authenticate_user_valid_credentials(self):
        """Test authenticate_user with valid credentials"""
        success, user, message = AuthenticationService.authenticate_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.assertTrue(success)
        self.assertEqual(user, self.user)
        self.assertIn('successful', message)

    def test_authenticate_user_invalid_email(self):
        """Test authenticate_user with invalid email"""
        from accounts_app.services.auth import AuthenticationError
        with self.assertRaises(AuthenticationError):
            AuthenticationService.authenticate_user(
                email='<EMAIL>',
                password='testpass123'
            )

    def test_authenticate_user_invalid_password(self):
        """Test authenticate_user with invalid password"""
        from accounts_app.services.auth import AuthenticationError
        with self.assertRaises(AuthenticationError):
            AuthenticationService.authenticate_user(
                email='<EMAIL>',
                password='wrongpass'
            )

    def test_login_user(self):
        """Test login_user"""
        request = self.factory.get('/test/')
        request.META['REMOTE_ADDR'] = '127.0.0.1'
        # Attach a session to the request
        from django.contrib.sessions.middleware import SessionMiddleware
        middleware = SessionMiddleware(lambda req: None)
        middleware.process_request(request)
        request.session.save()
        # Set backend for the user
        self.user.backend = 'django.contrib.auth.backends.ModelBackend'
        result = AuthenticationService.login_user(self.user, request)
        self.assertTrue(result)

    def test_logout_user(self):
        """Test logout_user"""
        request = self.factory.get('/test/')
        # Attach a session to the request
        from django.contrib.sessions.middleware import SessionMiddleware
        middleware = SessionMiddleware(lambda req: None)
        middleware.process_request(request)
        request.session.save()
        # Use setattr to avoid linter issues
        setattr(request, 'user', self.user)
        result = AuthenticationService.logout_user(request)
        self.assertTrue(result)


class EmailVerificationServiceTests(TestCase):
    """Test cases for EmailVerificationService"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER,
            email_verified=False
        )
        
        self.email_service = EmailVerificationService()

    def test_send_verification_email(self):
        """Test send_verification_email"""
        with patch('accounts_app.services.email.send_mail') as mock_send, \
             patch('accounts_app.services.email.settings') as mock_settings:
            mock_settings.SITE_URL = 'http://testserver'
            success, message = EmailVerificationService.send_verification_email(self.user)
            self.assertTrue(success)
            self.assertIn('successfully', message)
            mock_send.assert_called_once()


class EmailServiceTests(TestCase):
    """Test cases for EmailService"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER,
            email_verified=True
        )

    def test_send_templated_email(self):
        """Test send_templated_email"""
        with patch('accounts_app.services.email.EmailMultiAlternatives') as mock_email, \
             patch('accounts_app.services.email.render_to_string', return_value='<p>Test</p>'):
            mock_instance = MagicMock()
            mock_email.return_value = mock_instance
            
            success, message = EmailService.send_templated_email(
                subject='Test Subject',
                template_name='test_template.html',
                context={'user': self.user},
                to_email='<EMAIL>'
            )
            
            self.assertTrue(success)
            self.assertIn('successfully', message)
            mock_instance.send.assert_called_once()

    def test_send_verification_email(self):
        """Test send_verification_email"""
        with patch('accounts_app.services.email.EmailService.send_templated_email') as mock_send, \
             patch('accounts_app.services.email.settings') as mock_settings:
            mock_send.return_value = (True, 'Email sent successfully')
            mock_settings.SITE_URL = 'http://testserver'
            success, message = EmailService.send_verification_email(self.user)
            
            self.assertTrue(success)
            self.assertIn('successfully', message)
            mock_send.assert_called_once()


class PasswordServiceTests(TestCase):
    """Test cases for PasswordService"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER,
            status=UserStatus.ACTIVE,
            email_verified=True
        )

    def test_change_password_valid(self):
        """Test change_password with valid data"""
        success, message = PasswordService.change_password(
            self.user, 'testpass123', 'newpassword123'
        )
        
        self.assertTrue(success)
        self.assertIn('successfully', message)
        
        # Check password was changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('newpassword123'))

    def test_change_password_wrong_old_password(self):
        """Test change_password with wrong old password"""
        success, message = PasswordService.change_password(
            self.user, 'wrongpassword', 'newpassword123'
        )
        
        self.assertFalse(success)
        self.assertIn('incorrect', message)

    def test_reset_password(self):
        """Test reset_password"""
        success, message = PasswordService.reset_password(
            self.user, 'newpassword123'
        )
        
        self.assertTrue(success)
        self.assertIn('successfully', message)
        
        # Check password was changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('newpassword123'))


@pytest.mark.django_db
class AuthServicesPytestTests:
    """Pytest-style tests for authentication services"""

    def test_service_integration(self):
        """Test integration between services"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER
        )
        
        # Test authentication
        success, auth_user, message = AuthenticationService.authenticate_user(
            '<EMAIL>', 'testpass123'
        )
        assert success
        assert auth_user == user
        
        # Test password change
        success, message = PasswordService.change_password(
            user, 'testpass123', 'newpass123'
        )
        assert success
        
        # Test email verification with proper mocking
        with patch('accounts_app.services.email.send_mail'), \
             patch('accounts_app.services.email.settings') as mock_settings:
            mock_settings.SITE_URL = 'http://testserver'
            success, message = EmailVerificationService.send_verification_email(user)
            assert success

    def test_service_error_handling(self):
        """Test service error handling"""
        # Test with non-existent user
        from accounts_app.services.auth import AuthenticationError
        with pytest.raises(AuthenticationError):
            AuthenticationService.authenticate_user('<EMAIL>', 'pass')

    def test_service_performance(self):
        """Test service performance"""
        # Create multiple users for performance testing
        users = []
        for i in range(10):
            user = User.objects.create_user(
                email=f'user{i}@example.com',
                password='testpass123',
                role=UserRoles.CUSTOMER
            )
            users.append(user)
        
        # Test bulk operations
        for user in users:
            success, message = PasswordService.reset_password(user, 'newpass123')
            assert success 