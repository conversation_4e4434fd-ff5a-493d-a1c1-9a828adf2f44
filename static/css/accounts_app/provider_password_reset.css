/* CozyWish Design System - Provider Password Reset */
:root {
    /* Brand Colors */
    --cw-brand-primary: #2F160F;
    --cw-brand-light: #4a2a1f;
    --cw-brand-accent: #fae1d7;
    --cw-accent-light: #fef7f0;
    --cw-accent-dark: #f1d4c4;

    /* Neutral Colors */
    --cw-neutral-600: #525252;
    --cw-neutral-700: #404040;
    --cw-neutral-800: #262626;

    /* Typography */
    --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

    /* Shadows */
    --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Gradients */
    --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
    --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
    --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
}

body {
    font-family: var(--cw-font-primary);
    line-height: 1.6;
    color: var(--cw-neutral-800);
    background: white;
    min-height: 100vh;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--cw-font-heading);
    font-weight: 600;
    color: var(--cw-brand-primary);
    line-height: 1.3;
}

.password-reset-section {
    padding: 5rem 0;
    background: white;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.password-reset-container {
    max-width: 700px;
    width: 100%;
    margin: 0 auto;
    padding: 0 2rem;
}

.password-reset-card {
    background: white;
    border: 1px solid rgba(250, 225, 215, 0.3);
    border-radius: 1rem;
    box-shadow: var(--cw-shadow-lg);
    overflow: hidden;
    width: 100%;
}

.password-reset-header {
    background: var(--cw-gradient-card-subtle);
    padding: 3rem 3rem 2rem;
    text-align: center;
    position: relative;
    border-bottom: 1px solid var(--cw-brand-accent);
}

.password-reset-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="reset-pattern" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="1" fill="%23f1d4c4" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23reset-pattern)"/></svg>') repeat;
    opacity: 0.6;
    z-index: 1;
}

.password-reset-header .content {
    position: relative;
    z-index: 2;
}

.password-reset-icon {
    width: 80px;
    height: 80px;
    background: var(--cw-gradient-brand-button);
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--cw-shadow-md);
}

.password-reset-title {
    font-family: var(--cw-font-display);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--cw-brand-primary);
    margin-bottom: 1rem;
    line-height: 1.2;
}

.password-reset-subtitle {
    font-size: 1.125rem;
    color: var(--cw-neutral-600);
    margin-bottom: 0;
    line-height: 1.6;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.password-reset-body {
    padding: 3rem;
}

.form-control-cw {
    border: 2px solid var(--cw-brand-accent);
    border-radius: 0.5rem;
    padding: 0.875rem 1rem;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: white;
    color: var(--cw-neutral-800);
    font-family: var(--cw-font-primary);
}

.form-control-cw:focus {
    border-color: var(--cw-brand-primary);
    box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    outline: none;
}

.form-control-cw.is-invalid {
    border-color: #dc2626;
    box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.1);
}

.form-label {
    color: var(--cw-neutral-700);
    font-weight: 600;
    font-family: var(--cw-font-heading);
    margin-bottom: 0.5rem;
}

.form-label i {
    color: var(--cw-brand-primary);
    margin-right: 0.5rem;
}

.btn-cw-primary {
    background: var(--cw-gradient-brand-button);
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    padding: 1rem 2rem;
    color: white;
    transition: all 0.2s ease;
    box-shadow: var(--cw-shadow-sm);
    font-family: var(--cw-font-heading);
    letter-spacing: 0.025em;
    width: 100%;
    font-size: 1.125rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-cw-primary:hover {
    background: linear-gradient(135deg, #4a2a1f 0%, #2F160F 100%);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: var(--cw-shadow-md);
}

.alert-cw {
    border-radius: 0.5rem;
    padding: 1rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid;
    font-family: var(--cw-font-primary);
}

.alert-cw-info {
    background: #f0f9ff;
    border-color: #bae6fd;
    color: #0c4a6e;
}

.alert-cw-error {
    background: #fef2f2;
    border-color: #fecaca;
    color: #991b1b;
}

.invalid-feedback {
    display: block !important;
    width: 100%;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #dc2626;
    font-weight: 500;
    font-family: var(--cw-font-primary);
}

.invalid-feedback i {
    margin-right: 0.25rem;
}

a {
    color: var(--cw-brand-primary);
    text-decoration: none;
    font-weight: 500;
}

a:hover {
    color: var(--cw-brand-light);
    text-decoration: none;
}

.text-center p {
    color: var(--cw-neutral-700);
    font-family: var(--cw-font-primary);
    font-weight: 500;
}

.small-note {
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .password-reset-section {
        padding: 3rem 0;
    }
    .password-reset-container {
        max-width: 600px;
        padding: 0 1.5rem;
    }
    .password-reset-header {
        padding: 2rem 2rem 1.5rem;
    }
    .password-reset-title {
        font-size: 2rem;
    }
    .password-reset-subtitle {
        font-size: 1rem;
    }
    .password-reset-body {
        padding: 2rem;
    }
}

@media (max-width: 576px) {
    .password-reset-container {
        padding: 0 1rem;
    }
    .password-reset-header {
        padding: 1.5rem 1.5rem 1rem;
    }
    .password-reset-body {
        padding: 1.5rem;
    }
    .password-reset-title {
        font-size: 1.75rem;
    }
    .password-reset-icon {
        width: 64px;
        height: 64px;
        font-size: 1.5rem;
    }
} 