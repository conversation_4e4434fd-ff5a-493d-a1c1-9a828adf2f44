{% extends 'base.html' %}
{% load static %}

{% block title %}Account Temporarily Locked{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-lock fa-3x text-warning"></i>
                    </div>
                    
                    <h2 class="card-title text-warning mb-4">Account Temporarily Locked</h2>
                    
                    <p class="card-text mb-4">
                        Your account has been temporarily locked due to multiple failed login attempts 
                        from your IP address ({{ ip_address }}).
                    </p>
                    
                    <div class="alert alert-warning" role="alert">
                        <strong>Security Notice:</strong> 
                        {{ failed_attempts }} failed login attempts detected.
                    </div>
                    
                    {% if locked_until %}
                        <p class="text-muted">
                            <strong>Account will be unlocked on:</strong><br>
                            {{ locked_until|date:"F d, Y \a\t g:i A" }} UTC
                        </p>
                        
                        <div class="mt-4 mb-4">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning locked-progress" 
                                     role="progressbar">
                                    Locked
                                </div>
                            </div>
                        </div>
                    {% endif %}
                    
                    <div class="mt-4">
                        <h5>What you can do:</h5>
                        <ul class="list-unstyled text-start">
                            <li class="mb-2">
                                <i class="fas fa-clock text-primary"></i> 
                                Wait for the lockout period to expire
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-key text-primary"></i> 
                                Make sure you're using the correct password
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-envelope text-primary"></i> 
                                Use the password reset option if you've forgotten your password
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-shield-alt text-primary"></i> 
                                Ensure your account hasn't been compromised
                            </li>
                        </ul>
                    </div>
                    
                    <div class="mt-4">
                        <a href="{% url 'account_reset_password' %}" class="btn btn-primary me-2">
                            <i class="fas fa-key"></i> Reset Password
                        </a>
                        <a href="{% url 'home_app:home' %}" class="btn btn-secondary">
                            <i class="fas fa-home"></i> Return Home
                        </a>
                    </div>
                    
                    <div class="mt-4 text-muted">
                        <small>
                            <i class="fas fa-info-circle"></i> 
                            This security measure helps protect your account from unauthorized access attempts.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh the page when lockout expires
{% if locked_until %}
    const lockoutTime = new Date('{{ locked_until|date:"c" }}').getTime();
    const now = new Date().getTime();
    const timeRemaining = lockoutTime - now;
    
    if (timeRemaining > 0) {
        setTimeout(function() {
            location.reload();
        }, timeRemaining + 1000); // Add 1 second buffer
    }
{% endif %}
</script>
{% endblock %} 