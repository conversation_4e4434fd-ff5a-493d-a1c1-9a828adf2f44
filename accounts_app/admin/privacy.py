# --- Django Imports ---
from django.contrib import admin, messages
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# --- Local Imports ---
from ..models import ProfilePrivacySettings, ProfileVerification


@admin.register(ProfilePrivacySettings)
class ProfilePrivacySettingsAdmin(admin.ModelAdmin):
    """
    Admin interface for ProfilePrivacySettings.
    """
    list_display = (
        'user', 'profile_visibility', 'show_email', 'show_phone',
        'discoverable_in_search', 'data_processing_consent', 'created_at'
    )
    list_filter = (
        'profile_visibility', 'show_email', 'show_phone', 'discoverable_in_search',
        'data_processing_consent', 'marketing_consent', 'created_at'
    )
    search_fields = ('user__email',)
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (_('User'), {'fields': ('user',)}),
        (_('Profile Visibility'), {
            'fields': ('profile_visibility',),
            'description': 'Control overall profile visibility'
        }),
        (_('Profile Information'), {
            'fields': (
                'show_email', 'show_phone', 'show_address', 'show_birth_date',
                'show_gender', 'show_profile_picture', 'show_last_seen'
            ),
            'description': 'Control visibility of profile information'
        }),
        (_('Activity & Interactions'), {
            'fields': (
                'show_booking_history', 'show_reviews', 'show_favorites'
            ),
            'description': 'Control visibility of user activities'
        }),
        (_('Search & Discovery'), {
            'fields': (
                'discoverable_in_search', 'allow_friend_requests', 'allow_messages'
            ),
            'description': 'Control how users can be found and contacted'
        }),
        (_('Data & Analytics'), {
            'fields': (
                'allow_analytics', 'allow_personalized_ads'
            ),
            'description': 'Control data collection and advertising'
        }),
        (_('GDPR Compliance'), {
            'fields': (
                'data_processing_consent', 'marketing_consent', 'third_party_sharing'
            ),
            'description': 'Legal compliance and consent management'
        }),
        (_('Metadata'), {'fields': ('created_at', 'updated_at')}),
    )
    
    actions = ['reset_to_defaults', 'enable_privacy_mode', 'disable_privacy_mode']
    
    def reset_to_defaults(self, request, queryset):
        """Reset privacy settings to default values."""
        default_settings = ProfilePrivacySettings.get_default_settings()
        updated = 0
        
        for privacy_setting in queryset:
            for field, value in default_settings.items():
                setattr(privacy_setting, field, value)
            privacy_setting.save()
            updated += 1
        
        self.message_user(
            request,
            f'Reset {updated} privacy settings to default values.',
            messages.SUCCESS
        )
    reset_to_defaults.short_description = 'Reset to default privacy settings'
    
    def enable_privacy_mode(self, request, queryset):
        """Enable maximum privacy mode."""
        updated = queryset.update(
            profile_visibility='private',
            show_email=False,
            show_phone=False,
            show_address=False,
            show_birth_date=False,
            show_last_seen=False,
            show_booking_history=False,
            show_favorites=False,
            discoverable_in_search=False,
            allow_friend_requests=False,
            allow_analytics=False,
            allow_personalized_ads=False,
            third_party_sharing=False
        )
        
        self.message_user(
            request,
            f'Enabled privacy mode for {updated} users.',
            messages.SUCCESS
        )
    enable_privacy_mode.short_description = 'Enable maximum privacy mode'
    
    def disable_privacy_mode(self, request, queryset):
        """Disable privacy mode (set to public)."""
        updated = queryset.update(
            profile_visibility='public',
            show_profile_picture=True,
            show_gender=True,
            show_reviews=True,
            discoverable_in_search=True,
            allow_friend_requests=True,
            allow_messages=True
        )
        
        self.message_user(
            request,
            f'Disabled privacy mode for {updated} users.',
            messages.SUCCESS
        )
    disable_privacy_mode.short_description = 'Disable privacy mode'


@admin.register(ProfileVerification)
class ProfileVerificationAdmin(admin.ModelAdmin):
    """
    Admin interface for ProfileVerification.
    """
    list_display = (
        'user', 'verification_type', 'status', 'submitted_at',
        'verified_at', 'verified_by', 'is_verified', 'days_until_expiry'
    )
    list_filter = (
        'verification_type', 'status', 'submitted_at', 'verified_at',
        'verified_by', 'expires_at'
    )
    search_fields = ('user__email', 'verification_type', 'verified_by__email')
    readonly_fields = ('submitted_at', 'created_at', 'updated_at')
    
    fieldsets = (
        (_('User'), {'fields': ('user',)}),
        (_('Verification Details'), {
            'fields': (
                'verification_type', 'status', 'verification_data',
                'verification_document'
            )
        }),
        (_('Processing'), {
            'fields': (
                'verified_by', 'verified_at', 'expires_at',
                'rejection_reason', 'admin_notes'
            )
        }),
        (_('Metadata'), {'fields': ('submitted_at', 'created_at', 'updated_at')}),
    )
    
    actions = ['approve_verifications', 'reject_verifications', 'expire_verifications']
    
    def approve_verifications(self, request, queryset):
        """Approve selected verification requests."""
        updated = 0
        for verification in queryset.filter(status='pending'):
            verification.verify(verified_by_user=request.user)
            updated += 1
        
        self.message_user(
            request,
            f'Approved {updated} verification requests.',
            messages.SUCCESS
        )
    approve_verifications.short_description = 'Approve selected verifications'
    
    def reject_verifications(self, request, queryset):
        """Reject selected verification requests."""
        updated = 0
        for verification in queryset.filter(status='pending'):
            verification.reject(
                reason='Rejected by administrator',
                rejected_by_user=request.user
            )
            updated += 1
        
        self.message_user(
            request,
            f'Rejected {updated} verification requests.',
            messages.WARNING
        )
    reject_verifications.short_description = 'Reject selected verifications'
    
    def expire_verifications(self, request, queryset):
        """Mark selected verifications as expired."""
        updated = queryset.filter(status='verified').update(status='expired')
        
        self.message_user(
            request,
            f'Expired {updated} verifications.',
            messages.WARNING
        )
    expire_verifications.short_description = 'Mark as expired'
    
    def get_readonly_fields(self, request, obj=None):
        """Make certain fields readonly based on verification status."""
        readonly_fields = list(self.readonly_fields)
        
        if obj and obj.status == 'verified':
            readonly_fields.extend(['verification_type', 'verification_document'])
        
        return tuple(readonly_fields)
    
    def save_model(self, request, obj, form, change):
        """Handle verification approval/rejection in admin."""
        if change:
            # Check if status changed to verified
            if obj.status == 'verified' and not obj.verified_at:
                obj.verified_at = timezone.now()
                obj.verified_by = request.user
                # Set expiry to 1 year from verification
                obj.set_expiry(days=365)
        
        super().save_model(request, obj, form, change) 