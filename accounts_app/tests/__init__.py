# Import all test modules to ensure they are discovered by <PERSON><PERSON><PERSON>'s test runner

# Model tests
from .test_models.test_custom_user import *
from .test_models.test_user_profile import *
from .test_models.test_customer_profile import *
from .test_models.test_security_models import *

# View tests
from .test_views.test_customer_views import *
from .test_views.test_common_views import *

# Form tests
from .test_forms.test_customer_forms import *

# Utility tests
from .test_utils.test_validators import *
from .test_utils.test_decorators import *

# Service tests
from .test_services.test_auth_services import *

# URL tests
from .test_urls.test_url_patterns import *

# Import existing test modules for backward compatibility
from .test_models import *
from .test_forms import *
from .test_views import *
from .test_integration import *

