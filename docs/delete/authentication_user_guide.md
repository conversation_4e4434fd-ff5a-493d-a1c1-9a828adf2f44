# CozyWish Authentication User Guide

## Table of Contents
1. [Overview](#overview)
2. [Getting Started](#getting-started)
3. [User Registration](#user-registration)
4. [Login Process](#login-process)
5. [Password Management](#password-management)
6. [Email Verification](#email-verification)
7. [Account Security](#account-security)
8. [Two-Factor Authentication](#two-factor-authentication)
9. [Account Recovery](#account-recovery)
10. [Privacy Settings](#privacy-settings)
11. [Troubleshooting](#troubleshooting)

## Overview

CozyWish uses a secure, email-based authentication system with role-based access control. The platform supports three main user types:

- **Customers**: Users who book services and venues
- **Service Providers**: Users who offer services through the platform
- **Admin Users**: System administrators with elevated privileges

### Key Features

- Email-based authentication (no usernames required)
- Role-based access control
- Account lockout protection
- Password history tracking
- Email verification system
- Two-factor authentication support
- Privacy controls and GDPR compliance
- Comprehensive audit logging

## Getting Started

### System Requirements

- Modern web browser (Chrome, Firefox, Safari, Edge)
- Valid email address
- JavaScript enabled
- Cookies enabled

### Supported Authentication Methods

- Email and password
- Two-factor authentication (SMS, Email, Authenticator App)
- Password reset via email

## User Registration

### For Customers

1. **Navigate to Registration**
   - Go to the main website
   - Click "Sign Up" or "Register"
   - Select "Customer" account type

2. **Fill Required Information**
   - Email address (must be unique)
   - Password (minimum 8 characters)
   - First and last name
   - Accept terms and conditions

3. **Email Verification**
   - Check your email for verification link
   - Click the verification link
   - Your account is now active

### For Service Providers

1. **Navigate to Business Registration**
   - Go to the business signup page
   - Click "Register as Service Provider"

2. **Complete Business Information**
   - Business email address
   - Business name and description
   - Contact information
   - Service categories
   - Business verification documents

3. **Account Review Process**
   - Your application will be reviewed
   - You'll receive an email notification
   - Complete any additional requirements

### Password Requirements

- Minimum 8 characters
- Cannot be a commonly used password
- Cannot be too similar to your personal information
- Cannot be entirely numeric
- Cannot be one of your last 5 passwords

## Login Process

### Standard Login

1. **Access Login Page**
   - Go to the login page
   - Enter your email address
   - Enter your password
   - Click "Sign In"

2. **Role-Based Redirection**
   - Customers: Redirected to customer dashboard
   - Service Providers: Redirected to provider dashboard
   - Admin: Redirected to admin panel

### Security Features

- **Account Lockout**: After 5 failed attempts, accounts are temporarily locked
- **IP Tracking**: Login attempts are monitored by IP address
- **Session Management**: Secure session handling with automatic timeout
- **Login History**: All login attempts are logged and tracked

### Remember Me Feature

- Check "Remember Me" to stay logged in
- Session will persist for 30 days
- Can be disabled in privacy settings

## Password Management

### Changing Your Password

1. **Navigate to Security Settings**
   - Go to your profile settings
   - Click "Security" tab
   - Select "Change Password"

2. **Update Password**
   - Enter current password
   - Enter new password
   - Confirm new password
   - Click "Update Password"

### Password Reset

1. **Request Reset**
   - Go to login page
   - Click "Forgot Password?"
   - Enter your email address
   - Check your email for reset link

2. **Reset Process**
   - Click the reset link in your email
   - Enter new password
   - Confirm new password
   - Your password is now updated

### Password History

- System remembers your last 5 passwords
- You cannot reuse recent passwords
- Password history is securely encrypted
- Passwords expire after 90 days for security

## Email Verification

### Initial Verification

All new accounts must verify their email address:

1. **Registration Confirmation**
   - Complete the registration process
   - Check your email inbox
   - Look for "Verify Your Email" message

2. **Verification Process**
   - Click the verification link
   - You'll be redirected to confirmation page
   - Your email is now verified

### Resending Verification

If you didn't receive the verification email:

1. **Request New Verification**
   - Go to login page
   - Click "Resend Verification Email"
   - Enter your email address
   - Check your email for new verification link

2. **Check Spam Folder**
   - Verification emails may be in spam
   - Add <EMAIL> to your contacts
   - Check promotions tab in Gmail

## Account Security

### Security Features

**Account Lockout Protection**
- 5 failed login attempts trigger lockout
- Lockout duration: 30 minutes
- Automatic unlock after timeout
- Admin can manually unlock accounts

**Session Security**
- Sessions expire after 2 hours of inactivity
- Secure session cookies
- Cross-site request forgery protection
- SQL injection protection

**Password Security**
- Passwords are hashed using bcrypt
- Password history tracking
- Strength requirements enforced
- Regular password expiration

### Monitoring Your Account

1. **Login History**
   - View recent login attempts
   - Check login locations and times
   - Report suspicious activity

2. **Security Alerts**
   - Email notifications for logins
   - Unusual activity detection
   - Password change notifications

## Two-Factor Authentication

### Available Methods

1. **SMS Authentication**
   - Receive codes via text message
   - Requires verified phone number
   - Backup codes provided

2. **Email Authentication**
   - Receive codes via email
   - Uses your verified email address
   - Backup codes provided

3. **Authenticator App**
   - Use apps like Google Authenticator
   - Scan QR code to set up
   - More secure than SMS/email

### Setting Up 2FA

1. **Navigate to Security Settings**
   - Go to your profile
   - Click "Security" tab
   - Select "Two-Factor Authentication"

2. **Choose Method**
   - Select preferred 2FA method
   - Follow setup instructions
   - Save backup codes securely

3. **Verify Setup**
   - Enter verification code
   - Confirm 2FA is working
   - Test backup codes

### Using 2FA

1. **Login Process**
   - Enter email and password
   - You'll be prompted for 2FA code
   - Enter code from your chosen method
   - Complete login

2. **Backup Codes**
   - Use if primary method unavailable
   - Each code can only be used once
   - Generate new codes when running low

## Account Recovery

### If You're Locked Out

1. **Wait for Automatic Unlock**
   - Lockout expires after 30 minutes
   - No action required
   - Try logging in after timeout

2. **Contact Support**
   - Email: <EMAIL>
   - Provide account details
   - Request manual unlock

### If You've Lost Access

1. **Email Access Lost**
   - Contact support immediately
   - Provide identity verification
   - Update email address

2. **2FA Device Lost**
   - Use backup codes to login
   - Disable 2FA temporarily
   - Set up new 2FA method

## Privacy Settings

### Data Controls

1. **Profile Visibility**
   - Control who sees your profile
   - Set public/private status
   - Manage search visibility

2. **Communication Preferences**
   - Email notifications settings
   - Marketing communications
   - System alerts preferences

3. **Data Export**
   - Download your data (GDPR)
   - Export in JSON format
   - Includes all account information

### Account Deletion

1. **Request Deletion**
   - Go to privacy settings
   - Select "Delete Account"
   - Confirm deletion request

2. **Deletion Process**
   - Account marked for deletion
   - Data removed after 30 days
   - Cannot be undone after processing

## Troubleshooting

### Common Issues

**Can't Log In**
- Check email spelling
- Verify password is correct
- Check if account is locked
- Clear browser cache/cookies

**Didn't Receive Verification Email**
- Check spam folder
- Verify email address is correct
- Request new verification email
- Contact support if persistent

**2FA Not Working**
- Check device time synchronization
- Try backup codes
- Verify internet connection
- Disable and re-enable 2FA

**Account Locked**
- Wait 30 minutes for automatic unlock
- Check for security alerts
- Contact support if needed
- Review recent login activity

### Error Messages

**"Invalid email or password"**
- Double-check credentials
- Try password reset if needed
- Ensure account exists

**"Account is temporarily locked"**
- Wait for automatic unlock
- Check security notifications
- Contact support if urgent

**"Email verification required"**
- Check email for verification link
- Resend verification if needed
- Contact support if not received

### Getting Help

**Self-Service Options**
- Password reset
- Email verification resend
- Account unlock (automatic)
- FAQ section

**Contact Support**
- Email: <EMAIL>
- Live chat: Available 9 AM - 6 PM EST
- Phone: 1-800-COZY-WISH
- Response time: 24 hours

**When Contacting Support**
- Provide account email
- Describe the issue clearly
- Include error messages
- Mention troubleshooting steps tried

---

*This guide is updated regularly. For the latest information, visit our help center or contact support.* 