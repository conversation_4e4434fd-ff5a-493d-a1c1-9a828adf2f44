"""
Tests for card components functionality.
"""

from django.test import TestCase, Client
from django.urls import reverse


class CardComponentsTest(TestCase):
    """Test card components functionality."""

    def setUp(self):
        """Set up test client."""
        self.client = Client()

    def test_card_test_page_loads(self):
        """Test that the card test page loads successfully."""
        response = self.client.get(reverse('home_app:card_test'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'card-test.html')

    def test_card_test_page_contains_expected_content(self):
        """Test that the card test page contains expected card components."""
        response = self.client.get(reverse('home_app:card_test'))
        content = str(response.content)
        
        # Check for basic card elements
        self.assertIn('Basic Card', content)
        self.assertIn('Brand Card', content)
        self.assertIn('card-stats', content)
        self.assertIn('card-profile', content)
        self.assertIn('card-service', content)
        self.assertIn('card-brand', content)

    def test_card_test_page_uses_design_system_css(self):
        """Test that the card test page includes the design system CSS."""
        response = self.client.get(reverse('home_app:card_test'))
        content = str(response.content)
        
        # Check for CSS import
        self.assertIn('design-system/main.css', content)

    def test_card_variants_are_present(self):
        """Test that all card variants are present in the test page."""
        response = self.client.get(reverse('home_app:card_test'))
        content = str(response.content)
        
        # Check for different card types
        card_types = [
            'Foundation Cards',
            'User Profile Cards', 
            'Venue Service Cards',
            'Analytics Dashboard Cards',
            'Structured Content Cards',
            'Interactive Effects'
        ]
        
        for card_type in card_types:
            self.assertIn(card_type, content, f"Card type '{card_type}' not found in test page") 