# --- Production Settings ---
from .base import *
import sys
import dj_database_url
from decouple import config

# --- Debug Mode ---
DEBUG = config("DEBUG", default=False, cast=bool)
ENABLE_TEST_VIEW = DEBUG

# --- Allowed Hosts & CSRF ---
ALLOWED_HOSTS = ['.cozywish.com', 'cozywish.onrender.com']
RENDER_EXTERNAL_HOSTNAME = os.environ.get('RENDER_EXTERNAL_HOSTNAME')
if RENDER_EXTERNAL_HOSTNAME:
    ALLOWED_HOSTS.append(RENDER_EXTERNAL_HOSTNAME)

CSRF_TRUSTED_ORIGINS = [
    'https://www.cozywish.com',
    'https://cozywish.onrender.com'
]

# --- Database Configuration ---
DATABASE_URL = os.environ.get('DATABASE_URL')
if DATABASE_URL:
    DATABASES = {'default': dj_database_url.parse(DATABASE_URL, conn_max_age=600)}
else:
    # Fallback to SQLite if no DATABASE_URL is provided
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }

# --- Static Files Configuration ---
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
WHITENOISE_USE_FINDERS = True
WHITENOISE_AUTOREFRESH = True

# --- AWS S3 Configuration ---
AWS_ACCESS_KEY_ID = config('AWS_ACCESS_KEY_ID', default=None)
AWS_SECRET_ACCESS_KEY = config('AWS_SECRET_ACCESS_KEY', default=None)
AWS_STORAGE_BUCKET_NAME = config('AWS_STORAGE_BUCKET_NAME', default=None)
AWS_S3_REGION_NAME = config('AWS_S3_REGION_NAME', default='us-east-1')
AWS_S3_CUSTOM_DOMAIN = config('AWS_S3_CUSTOM_DOMAIN', default=None)

if AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY and AWS_STORAGE_BUCKET_NAME:
    # Django 4.2+ STORAGES configuration
    STORAGES = {
        "default": {
            "BACKEND": "storages.backends.s3boto3.S3Boto3Storage",
            "OPTIONS": {
                "access_key": AWS_ACCESS_KEY_ID,
                "secret_key": AWS_SECRET_ACCESS_KEY,
                "bucket_name": AWS_STORAGE_BUCKET_NAME,
                "region_name": AWS_S3_REGION_NAME,
                "custom_domain": AWS_S3_CUSTOM_DOMAIN,
                "file_overwrite": False,
                "default_acl": None,
                "signature_version": "s3v4",
                "addressing_style": "virtual",
                "use_ssl": True,
                "verify": True,
                "object_parameters": {
                    "CacheControl": "max-age=86400",
                },
                "querystring_auth": True,
                "querystring_expire": 3600,  # 1 hour
            },
        },
        "staticfiles": {
            "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
        },
    }

    # Legacy AWS settings for backward compatibility
    AWS_S3_FILE_OVERWRITE = False
    AWS_DEFAULT_ACL = None
    AWS_S3_VERIFY = True
    AWS_S3_USE_SSL = True
    AWS_S3_SIGNATURE_VERSION = 's3v4'
    AWS_S3_ADDRESSING_STYLE = 'virtual'
    AWS_QUERYSTRING_AUTH = True
    AWS_QUERYSTRING_EXPIRE = 3600  # 1 hour

    # Performance and caching
    AWS_S3_OBJECT_PARAMETERS = {
        'CacheControl': 'max-age=86400',
    }

    # URL configuration
    if AWS_S3_CUSTOM_DOMAIN:
        MEDIA_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/'
    else:
        MEDIA_URL = f'https://{AWS_STORAGE_BUCKET_NAME}.s3.{AWS_S3_REGION_NAME}.amazonaws.com/'

    # Ensure URLs don't have double slashes
    if not MEDIA_URL.endswith('/'):
        MEDIA_URL += '/'

else:
    # Log missing credentials for debugging
    missing_creds = []
    if not AWS_ACCESS_KEY_ID:
        missing_creds.append('AWS_ACCESS_KEY_ID')
    if not AWS_SECRET_ACCESS_KEY:
        missing_creds.append('AWS_SECRET_ACCESS_KEY')
    if not AWS_STORAGE_BUCKET_NAME:
        missing_creds.append('AWS_STORAGE_BUCKET_NAME')

    error_msg = f"AWS S3 credentials are required in production. Missing: {', '.join(missing_creds)}"
    print(f"WARNING: {error_msg}")

    # Fallback to local storage with warning
    MEDIA_URL = '/media/'
    MEDIA_ROOT = BASE_DIR / 'media'
    STORAGES = {
        "default": {
            "BACKEND": "django.core.files.storage.FileSystemStorage",
        },
        "staticfiles": {
            "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
        },
    }

# --- Email Configuration (SendGrid) ---
EMAIL_HOST_PASSWORD = config('EMAIL_HOST_PASSWORD', default='')
FORCE_EMAIL_BACKEND = config('FORCE_EMAIL_BACKEND', default=False, cast=bool)

# Email backend logic for production
if TESTING:
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
elif FORCE_EMAIL_BACKEND or EMAIL_HOST_PASSWORD:
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
else:
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# SendGrid SMTP Configuration
EMAIL_HOST = config('EMAIL_HOST', default='smtp.sendgrid.net')
EMAIL_PORT = config('EMAIL_PORT', default=587, cast=int)
EMAIL_USE_TLS = config('EMAIL_USE_TLS', default=True, cast=bool)
EMAIL_USE_SSL = config('EMAIL_USE_SSL', default=False, cast=bool)
EMAIL_HOST_USER = config('EMAIL_HOST_USER', default='apikey')
EMAIL_TIMEOUT = config('EMAIL_TIMEOUT', default=30, cast=int)

# Email addresses configuration
DEFAULT_FROM_EMAIL = config('DEFAULT_FROM_EMAIL', default='<EMAIL>')
SERVER_EMAIL = config('SERVER_EMAIL', default='<EMAIL>')
SUPPORT_EMAIL = config('SUPPORT_EMAIL', default='<EMAIL>')
NO_REPLY_EMAIL = config('NO_REPLY_EMAIL', default='<EMAIL>')

# Email template configuration
EMAIL_TEMPLATE_DIR = 'emails'
EMAIL_TEMPLATE_CONTEXT = {
    'site_name': 'CozyWish',
    'site_url': 'https://cozywish.com',
    'support_email': SUPPORT_EMAIL,
    'company_name': 'CozyWish',
    'company_address': 'Your Company Address',
    'unsubscribe_url': 'https://cozywish.com/unsubscribe',
}

# Email retry configuration
EMAIL_MAX_RETRIES = config('EMAIL_MAX_RETRIES', default=3, cast=int)
EMAIL_RETRY_DELAY = config('EMAIL_RETRY_DELAY', default=60, cast=int)

# SendGrid-specific settings
SENDGRID_API_KEY = config('SENDGRID_API_KEY', default=EMAIL_HOST_PASSWORD)
SENDGRID_TRACK_CLICKS = config('SENDGRID_TRACK_CLICKS', default=True, cast=bool)
SENDGRID_TRACK_OPENS = config('SENDGRID_TRACK_OPENS', default=True, cast=bool)
SENDGRID_SANDBOX_MODE = config('SENDGRID_SANDBOX_MODE', default=False, cast=bool)

# Email content validation
EMAIL_SUBJECT_PREFIX = config('EMAIL_SUBJECT_PREFIX', default='[CozyWish] ')
EMAIL_HTML_REQUIRED = config('EMAIL_HTML_REQUIRED', default=True, cast=bool)
EMAIL_PLAIN_TEXT_FALLBACK = config('EMAIL_PLAIN_TEXT_FALLBACK', default=True, cast=bool)

# --- Celery Configuration Removed ---
# Celery dependencies have been removed from this project

# --- Production Security Settings ---
SECURE_SSL_REDIRECT = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'

# --- Production Environment Variables Documentation ---
"""
## Required Production Environment Variables
AWS_ACCESS_KEY_ID
AWS_S3_CUSTOM_DOMAIN
AWS_S3_REGION_NAME
AWS_SECRET_ACCESS_KEY
AWS_STORAGE_BUCKET_NAME
DATABASE_URL
DEBUG
EMAIL_HOST_PASSWORD (or SENDGRID_API_KEY)
SECRET_KEY

## Optional Email Configuration Variables
DEFAULT_FROM_EMAIL (default: <EMAIL>)
EMAIL_HOST (default: smtp.sendgrid.net)
EMAIL_HOST_USER (default: apikey)
EMAIL_PORT (default: 587)
EMAIL_USE_TLS (default: True)
EMAIL_USE_SSL (default: False)
EMAIL_TIMEOUT (default: 30)
EMAIL_MAX_RETRIES (default: 3)
EMAIL_RETRY_DELAY (default: 60)
EMAIL_SUBJECT_PREFIX (default: [CozyWish] )
FORCE_EMAIL_BACKEND (default: False)
NO_REPLY_EMAIL (default: <EMAIL>)
SENDGRID_API_KEY (default: EMAIL_HOST_PASSWORD)
SENDGRID_TRACK_CLICKS (default: True)
SENDGRID_TRACK_OPENS (default: True)
SENDGRID_SANDBOX_MODE (default: False)
SERVER_EMAIL (default: <EMAIL>)
SUPPORT_EMAIL (default: <EMAIL>)

## Optional System Variables
LOG_LEVEL
WEB_CONCURRENCY
""" 