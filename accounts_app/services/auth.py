"""
Authentication service for accounts_app.

This module contains the AuthenticationService class and related utilities
for handling user authentication operations.
"""

import logging
from typing import Optional, Tuple
from django.contrib.auth import authenticate, login, logout
from django.utils.translation import gettext_lazy as _

# Local imports
from ..models import CustomUser, LoginHistory, AccountLockout
from ..utils.logging import log_authentication_event, log_user_activity


logger = logging.getLogger(__name__)


def get_client_ip(request):
    """
    Get client IP address from request with proxy support.
    
    Handles both direct connections and connections through proxies
    by checking X-Forwarded-For header first, then falling back to
    REMOTE_ADDR.
    
    Args:
        request: Django HttpRequest object
        
    Returns:
        str: Client IP address or localhost if request is None
    """
    if request:
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    return '127.0.0.1'


class AuthenticationError(Exception):
    """Authentication-related errors"""
    pass


class AuthenticationService:
    """
    Service for handling user authentication operations.
    
    Provides methods for login, logout, signup, and authentication validation
    with comprehensive logging and security features.
    """
    
    @staticmethod
    def authenticate_user(email: str, password: str, request=None) -> Tuple[bool, Optional[CustomUser], str]:
        """
        Authenticate user credentials.
        
        Args:
            email: User's email address
            password: User's password
            request: HTTP request object for logging
            
        Returns:
            Tuple of (success, user_object, message)
            
        Raises:
            AuthenticationError: If authentication fails
        """
        try:
            # Check for account lockout
            client_ip = get_client_ip(request) if request else None
            if client_ip and AccountLockout.is_ip_locked(client_ip):
                logger.warning(f"Authentication blocked for locked IP: {client_ip}")
                raise AuthenticationError(_("Account is temporarily locked. Please try again later."))
            
            # Try to find user by email first for failed login tracking
            user = None
            try:
                user = CustomUser.objects.get(email=email)
            except CustomUser.DoesNotExist:
                pass
            
            # Authenticate user
            authenticated_user = authenticate(request=request, email=email, password=password)
            
            if authenticated_user is not None:
                if authenticated_user.is_active:
                    # Reset failed attempts on successful login
                    if client_ip:
                        AccountLockout.reset_failed_attempts(client_ip)
                    
                    AuthenticationService._log_successful_authentication(authenticated_user, request)
                    return True, authenticated_user, _("Authentication successful")
                else:
                    # Record failed login attempt for inactive account
                    if user:
                        AuthenticationService._record_failed_login(user, request, "account_inactive")
                    
                    AuthenticationService._log_failed_authentication(email, request, "account_inactive")
                    raise AuthenticationError(_("Your account has been deactivated."))
            else:
                # Record failed attempt
                if client_ip:
                    AccountLockout.record_failed_attempt(client_ip, user)
                
                # Record failed login attempt in LoginHistory
                if user:
                    AuthenticationService._record_failed_login(user, request, "invalid_credentials")
                
                AuthenticationService._log_failed_authentication(email, request, "invalid_credentials")
                raise AuthenticationError(_("Invalid email or password"))
                
        except AuthenticationError:
            raise
        except Exception as e:
            logger.error(f"Authentication service error: {str(e)}", exc_info=True)
            raise AuthenticationError(_("Authentication failed. Please try again."))
    
    @staticmethod
    def login_user(user: CustomUser, request) -> bool:
        """
        Log in a user and record the session.
        
        Args:
            user: User object to log in
            request: HTTP request object
            
        Returns:
            Boolean indicating success
        """
        try:
            # Use ModelBackend for login since we're using custom user model
            from django.contrib.auth.backends import ModelBackend
            login(request, user, backend='django.contrib.auth.backends.ModelBackend')
            
            # Record login history
            LoginHistory.objects.create(
                user=user,
                ip_address=get_client_ip(request) or '127.0.0.1',
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                is_successful=True
            )
            
            # Update user's last login IP
            user.last_login_ip = get_client_ip(request)
            user.save(update_fields=['last_login_ip'])
            
            logger.info(f"User logged in successfully: {user.email}")
            return True
            
        except Exception as e:
            logger.error(f"Login service error: {str(e)}", exc_info=True)
            return False
    
    @staticmethod
    def logout_user(request) -> bool:
        """
        Log out a user and clean up session.
        
        Args:
            request: HTTP request object
            
        Returns:
            Boolean indicating success
        """
        try:
            user = request.user
            if user.is_authenticated:
                log_user_activity(
                    activity_type='logout',
                    user=user,
                    request=request,
                    details={'logout_method': 'manual'}
                )
                logger.info(f"User logged out: {user.email}")
            
            logout(request)
            return True
            
        except Exception as e:
            logger.error(f"Logout service error: {str(e)}", exc_info=True)
            return False
    
    @staticmethod
    def _record_failed_login(user: CustomUser, request, reason: str):
        """Record failed login attempt in LoginHistory"""
        try:
            LoginHistory.objects.create(
                user=user,
                ip_address=get_client_ip(request) or '127.0.0.1',
                user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
                is_successful=False
            )
            logger.info(f"Failed login attempt recorded for user: {user.email}, reason: {reason}")
        except Exception as e:
            logger.error(f"Failed to record login attempt: {str(e)}", exc_info=True)
    
    @staticmethod
    def _log_successful_authentication(user: CustomUser, request):
        """Log successful authentication event"""
        log_authentication_event(
            event_type='login_success',
            user_email=user.email,
            success=True,
            request=request
        )
        log_user_activity(
            activity_type='login',
            user=user,
            request=request,
            details={'login_method': 'password'}
        )
    
    @staticmethod
    def _log_failed_authentication(email: str, request, reason: str):
        """Log failed authentication event"""
        log_authentication_event(
            event_type='login_failed',
            user_email=email,
            success=False,
            request=request,
            failure_reason=reason
        ) 