{% extends 'base.html' %}
{% load static %}

{% block title %}Access Denied - CozyWish{% endblock %}

{% block extra_css %}
<style>
    .error-container {
        min-height: 60vh;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 2rem 0;
    }
    
    .error-content {
        max-width: 600px;
        margin: 0 auto;
    }
    
    .error-code {
        font-size: 8rem;
        font-weight: bold;
        color: #ffc107;
        line-height: 1;
        margin-bottom: 1rem;
    }
    
    .error-title {
        font-size: 2rem;
        font-weight: 600;
        color: #212529;
        margin-bottom: 1rem;
    }
    
    .error-message {
        font-size: 1.1rem;
        color: #6c757d;
        margin-bottom: 2rem;
        line-height: 1.6;
    }
    
    .error-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .btn-primary {
        background-color: #000;
        border-color: #000;
        padding: 0.75rem 2rem;
        font-weight: 500;
    }
    
    .btn-primary:hover {
        background-color: #333;
        border-color: #333;
    }
    
    .btn-outline-secondary {
        color: #6c757d;
        border-color: #6c757d;
        padding: 0.75rem 2rem;
        font-weight: 500;
    }
    
    .btn-outline-secondary:hover {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
    }
    
    .error-icon {
        font-size: 4rem;
        color: #ffc107;
        margin-bottom: 1rem;
    }
    
    .access-info {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-top: 2rem;
        text-align: left;
    }
    
    .access-info h6 {
        color: #856404;
        margin-bottom: 0.5rem;
    }
    
    .access-info p {
        color: #856404;
        margin-bottom: 0;
        font-size: 0.9rem;
    }
    
    @media (max-width: 768px) {
        .error-code {
            font-size: 6rem;
        }
        
        .error-title {
            font-size: 1.5rem;
        }
        
        .error-actions {
            flex-direction: column;
            align-items: center;
        }
        
        .btn {
            width: 100%;
            max-width: 300px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="error-container">
    <div class="error-content">
        <div class="error-icon">
            <i class="fas fa-lock"></i>
        </div>
        
        <div class="error-code">403</div>
        
        <h1 class="error-title">Access Denied</h1>
        
        <p class="error-message">
            You don't have permission to access this page. 
            This could be because you need to log in with the appropriate account type 
            or you don't have the required permissions.
        </p>
        
        <div class="error-actions">
            <a href="{% url 'home_app:home' %}" class="btn btn-primary">
                <i class="fas fa-home me-2"></i>Go to Homepage
            </a>
            <a href="{% url 'accounts_app:customer_login' %}" class="btn btn-outline-secondary">
                <i class="fas fa-sign-in-alt me-2"></i>Login
            </a>
        </div>
        
        <div class="access-info">
            <h6><i class="fas fa-info-circle me-2"></i>Need access?</h6>
            <p>
                If you believe you should have access to this page, please make sure you're 
                logged in with the correct account type (Customer, Service Provider, or Admin). 
                Different areas of CozyWish require different permission levels.
            </p>
        </div>
        
        {% if request_path %}
        <div class="mt-3">
            <small class="text-muted">
                Requested path: <code>{{ request_path }}</code>
            </small>
        </div>
        {% endif %}
        
        <div class="mt-3">
            <small class="text-muted">
                Need help? Contact us at 
                <a href="mailto:{{ support_email|default:'<EMAIL>' }}" class="text-decoration-none">
                    {{ support_email|default:'<EMAIL>' }}
                </a>
            </small>
        </div>
    </div>
</div>
{% endblock %} 