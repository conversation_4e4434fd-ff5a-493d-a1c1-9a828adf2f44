/* Accounts App - Verification Status Styles */

/* Progress Bar */
.verification-progress {
    height: 20px;
}

.verification-progress .progress-bar {
    transition: width 0.3s ease;
}

/* Timeline Marker */
.timeline-marker {
    width: 12px;
    height: 12px;
    margin-top: 6px;
}

/* Border Left Colors */
.border-left-primary {
    border-left: 4px solid #007bff;
}

.border-left-warning {
    border-left: 4px solid #ffc107;
}

/* Card Borders */
.card.border-success {
    border-width: 2px;
}

.card.border-warning {
    border-width: 2px;
}

.card.border-danger {
    border-width: 2px;
}

.card.border-secondary {
    border-width: 2px;
}

/* Verification Badge Icons */
.verification-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.verification-icon.text-success {
    color: #28a745;
}

.verification-icon.text-warning {
    color: #ffc107;
}

.verification-icon.text-danger {
    color: #dc3545;
}

.verification-icon.text-muted {
    color: #6c757d;
}

/* Verification Cards */
.verification-card {
    transition: all 0.3s ease;
    height: 100%;
}

.verification-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Status Badges */
.verification-badge {
    margin-bottom: 0.5rem;
}

.verification-badge .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

/* Timeline */
.timeline {
    position: relative;
}

.timeline-item {
    margin-bottom: 1rem;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

/* Action Buttons */
.verification-actions {
    margin-top: 1rem;
}

.verification-actions .btn {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .verification-icon {
        font-size: 2.5rem;
    }
    
    .verification-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .verification-actions .btn:last-child {
        margin-bottom: 0;
    }
} 