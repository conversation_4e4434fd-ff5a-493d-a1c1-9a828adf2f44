{% extends 'base.html' %}

{% block title %}Reset Password - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
{% endblock %}

{% block content %}
<section class="password-reset-section">
    <div class="password-reset-container">
        <div class="password-reset-card">
            <div class="password-reset-header">
                <div class="content">
                    <div class="password-reset-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <h1 class="password-reset-title">Reset Password</h1>
                    <p class="password-reset-subtitle">We'll send password reset instructions to your email address</p>
                </div>
            </div>

            <div class="password-reset-body">
                <!-- Show helpful message if email is pre-populated -->
                {% if request.GET.email %}
                <div class="alert-cw alert-cw-info mb-4" role="alert">
                  <i class="fas fa-info-circle me-2"></i>
                  <strong>Email pre-filled:</strong> We've filled in your email address from the login form. You can change it if needed.
                </div>
                {% endif %}

                <form method="post" novalidate>
                  {% csrf_token %}

                  {% if form.non_field_errors %}
                  <div class="alert-cw alert-cw-error mb-4">
                    {% for error in form.non_field_errors %}
                    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}

                  {% for field in form %}
                  <div class="mb-4">
                    <label for="{{ field.id_for_label }}" class="form-label">
                      <i class="fas fa-envelope"></i>{{ field.label }}
                    </label>
                    {% if field.errors %}
                      {{ field|add_class:"form-control-cw is-invalid"|attr:"placeholder:Enter your email address" }}
                    {% else %}
                      {{ field|add_class:"form-control-cw"|attr:"placeholder:Enter your email address" }}
                    {% endif %}
                    {% if field.errors %}
                    <div class="invalid-feedback" role="alert" aria-live="polite">
                      {% for error in field.errors %}
                      <i class="fas fa-exclamation-circle"></i>{{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                    {% if field.help_text %}
                    <div class="form-text">{{ field.help_text }}</div>
                    {% endif %}
                  </div>
                  {% endfor %}

                  <div class="d-grid mb-4">
                    <button type="submit" class="btn-cw-primary">
                      <i class="fas fa-paper-plane me-2"></i>Send Reset Link
                    </button>
                  </div>
                </form>

                <div class="text-center">
                  <a href="{% url 'accounts_app:customer_login' %}" class="btn-cw-secondary">
                    <i class="fas fa-arrow-left"></i>Back to Login
                  </a>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
