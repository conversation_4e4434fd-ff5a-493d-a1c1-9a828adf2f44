{% extends 'base.html' %}

{% block extra_css %}
    {% load static %}
    {% load widget_tweaks %}

    <!-- Preconnect for Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Base Account Styles -->
    <link rel="stylesheet" href="{% static 'css/accounts_app/base_account.css' %}">

    {% block account_extra_css %}{% endblock %}
{% endblock %}

{% block extra_js %}
{{ block.super }}
<!-- Base Account JavaScript -->
<script src="{% static 'js/accounts_app/base_account.js' %}"></script>
{% block account_extra_js %}{% endblock %}
{% endblock %}

{% block content %}
{% if request.resolver_match.url_name == 'customer_profile' or request.resolver_match.url_name == 'service_provider_profile' or request.resolver_match.url_name == 'customer_change_password' or request.resolver_match.url_name == 'service_provider_change_password' or request.resolver_match.url_name == 'customer_profile_edit' or request.resolver_match.url_name == 'service_provider_profile_edit' %}
    <!-- Full-width layout for profile pages -->
    <div class="profile-wrapper">
        {% block account_content %}{% endblock %}
    </div>
{% else %}
    <!-- Centered card layout for forms and other account pages -->
    <div class="account-wrapper d-flex align-items-center justify-content-center p-3">
        <div class="account-card w-100">
            <div class="account-card-body">
                <div class="messages-container" role="alert" aria-live="polite">
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    </div>
                {% block account_form_content %}{% endblock %}
            </div>
        </div>
        {% block below_card %}{% endblock %}
    </div>
{% endif %}
{% endblock %}
