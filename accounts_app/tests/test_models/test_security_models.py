"""
Comprehensive tests for security-related models.
"""

# --- Standard Library Imports ---
from datetime import timedelta

# --- Django Imports ---
from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model

# --- Third Party Imports ---
import pytest

# --- Local App Imports ---
from accounts_app.models import (
    UserSecurity, AccountLockout, EmailVerificationToken
)

User = get_user_model()


class UserSecurityModelTests(TestCase):
    """Test cases for UserSecurity model"""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

    def test_user_security_creation(self):
        """Test creating a user security record"""
        security = UserSecurity.objects.create(user=self.user)
        
        self.assertEqual(security.user, self.user)
        self.assertEqual(security.failed_login_attempts, 0)
        self.assertIsNone(security.account_locked_until)

    def test_user_security_str_method(self):
        """Test string representation of user security"""
        security = UserSecurity.objects.create(user=self.user)
        
        expected_str = f"{self.user.email} - Security"
        self.assertEqual(str(security), expected_str)

    def test_one_to_one_relationship(self):
        """Test one-to-one relationship with CustomUser"""
        security = UserSecurity.objects.create(user=self.user)
        
        # Test forward relationship
        self.assertEqual(security.user, self.user)
        
        # Test reverse relationship
        self.assertEqual(self.user.user_security, security)

    def test_is_account_locked_property(self):
        """Test is_account_locked property"""
        security = UserSecurity.objects.create(user=self.user)
        
        # Initially not locked
        self.assertFalse(security.is_account_locked)
        
        # Lock account
        security.account_locked_until = timezone.now() + timedelta(minutes=30)
        security.save()
        
        self.assertTrue(security.is_account_locked)
        
        # Unlock account
        security.account_locked_until = None
        security.save()
        
        self.assertFalse(security.is_account_locked)

    def test_lock_account_method(self):
        """Test lock_account method"""
        security = UserSecurity.objects.create(user=self.user)
        
        security.lock_account(30)  # Lock for 30 minutes
        
        self.assertIsNotNone(security.account_locked_until)
        self.assertTrue(security.is_account_locked)

    def test_unlock_account_method(self):
        """Test unlock_account method"""
        security = UserSecurity.objects.create(user=self.user)
        
        # Lock account first
        security.lock_account(30)
        self.assertTrue(security.is_account_locked)
        
        # Unlock account
        security.unlock_account()
        
        self.assertIsNone(security.account_locked_until)
        self.assertEqual(security.failed_login_attempts, 0)
        self.assertFalse(security.is_account_locked)

    def test_record_failed_login(self):
        """Test record_failed_login method"""
        security = UserSecurity.objects.create(user=self.user)
        
        # Record failed login
        security.record_failed_login()
        
        self.assertEqual(security.failed_login_attempts, 1)
        
        # Record more failed logins
        security.record_failed_login()
        security.record_failed_login()
        
        self.assertEqual(security.failed_login_attempts, 3)

    def test_reset_failed_attempts(self):
        """Test reset_failed_attempts method"""
        security = UserSecurity.objects.create(user=self.user)
        
        # Record some failed attempts
        security.record_failed_login()
        security.record_failed_login()
        
        self.assertEqual(security.failed_login_attempts, 2)
        
        # Reset failed attempts
        security.reset_failed_attempts()
        
        self.assertEqual(security.failed_login_attempts, 0)

    def test_update_last_activity(self):
        """Test update_last_activity method"""
        security = UserSecurity.objects.create(user=self.user)
        
        original_activity = security.last_activity
        
        # Update activity
        security.update_last_activity()
        
        self.assertIsNotNone(security.last_activity)
        if original_activity:
            self.assertGreater(security.last_activity, original_activity)

    def test_two_factor_methods(self):
        """Test two-factor authentication methods"""
        security = UserSecurity.objects.create(user=self.user)
        
        # Test default two-factor method
        self.assertIsNotNone(security.two_factor_method)
        
        # Test is_two_factor_enabled property
        self.assertIsInstance(security.is_two_factor_enabled, bool)

    def test_backup_codes_management(self):
        """Test backup codes management"""
        security = UserSecurity.objects.create(user=self.user)
        
        # Test default backup codes
        self.assertEqual(security.backup_codes, [])
        
        # Test setting backup codes
        codes = ['code1', 'code2', 'code3']
        security.backup_codes = codes
        security.save()
        
        self.assertEqual(security.backup_codes, codes)

    def test_session_management(self):
        """Test session management"""
        security = UserSecurity.objects.create(user=self.user)
        
        # Test session key
        session_key = 'test_session_key'
        security.session_key = session_key
        security.save()
        
        self.assertEqual(security.session_key, session_key)

    def test_cascade_deletion(self):
        """Test that security record is deleted when user is deleted"""
        security = UserSecurity.objects.create(user=self.user)
        security_id = security.id
        
        self.user.delete()
        
        # Security record should be deleted
        self.assertFalse(UserSecurity.objects.filter(id=security_id).exists())


class AccountLockoutModelTests(TestCase):
    """Test cases for AccountLockout model"""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

    def test_account_lockout_creation(self):
        """Test creating an account lockout record"""
        lockout = AccountLockout.objects.create(
            user=self.user,
            ip_address='***********',
            failed_attempts=3
        )
        
        self.assertEqual(lockout.user, self.user)
        self.assertEqual(lockout.ip_address, '***********')
        self.assertEqual(lockout.failed_attempts, 3)

    def test_account_lockout_str_method(self):
        """Test string representation of account lockout"""
        lockout = AccountLockout.objects.create(
            user=self.user,
            ip_address='***********',
            failed_attempts=3
        )
        
        expected_str = f"{self.user.email} @ *********** - 3 attempts"
        self.assertEqual(str(lockout), expected_str)

    def test_account_lockout_anonymous_user(self):
        """Test account lockout with anonymous user"""
        lockout = AccountLockout.objects.create(
            user=None,
            ip_address='***********',
            failed_attempts=5
        )
        
        expected_str = f"Anonymous @ *********** - 5 attempts"
        self.assertEqual(str(lockout), expected_str)

    def test_is_locked_property(self):
        """Test is_locked property"""
        lockout = AccountLockout.objects.create(
            user=self.user,
            ip_address='***********',
            failed_attempts=3
        )
        
        # Initially not locked
        self.assertFalse(lockout.is_locked)
        
        # Lock account
        lockout.locked_until = timezone.now() + timedelta(minutes=30)
        lockout.save()
        
        self.assertTrue(lockout.is_locked)
        
        # Unlock account
        lockout.locked_until = None
        lockout.save()
        
        self.assertFalse(lockout.is_locked)

    def test_unlock_account_method(self):
        """Test unlock_account method"""
        lockout = AccountLockout.objects.create(
            user=self.user,
            ip_address='***********',
            failed_attempts=3
        )
        
        # Lock account first by setting locked_until
        lockout.locked_until = timezone.now() + timedelta(minutes=30)
        lockout.save()
        self.assertTrue(lockout.is_locked)
        
        # Unlock account
        lockout.unlock()
        
        self.assertIsNone(lockout.locked_until)
        self.assertEqual(lockout.failed_attempts, 0)
        self.assertFalse(lockout.is_locked)

    def test_increment_attempts_method(self):
        """Test increment_attempts method"""
        lockout = AccountLockout.objects.create(
            user=self.user,
            ip_address='***********',
            failed_attempts=1
        )
        
        # Use the class method to record failed attempts
        AccountLockout.record_failed_attempt('***********', self.user)
        
        # Refresh from database
        lockout.refresh_from_db()
        
        self.assertEqual(lockout.failed_attempts, 2)

    def test_unique_ip_constraint(self):
        """Test unique IP constraint"""
        # Create first lockout record
        lockout1 = AccountLockout.objects.create(
            user=self.user,
            ip_address='***********',
            failed_attempts=3,
            locked_until=timezone.now() + timedelta(minutes=30)
        )
        
        # Try to create another lockout record with same IP (should fail)
        with self.assertRaises(Exception):
            AccountLockout.objects.create(
                user=self.user,
                ip_address='***********',
                failed_attempts=5,
                locked_until=timezone.now() + timedelta(minutes=30)
            )

    def test_cascade_deletion(self):
        """Test that lockout record is deleted when user is deleted"""
        lockout = AccountLockout.objects.create(
            user=self.user,
            ip_address='***********',
            failed_attempts=3
        )
        lockout_id = lockout.id
        
        self.user.delete()
        
        # Lockout record should be deleted
        self.assertFalse(AccountLockout.objects.filter(id=lockout_id).exists())


class EmailVerificationTokenModelTests(TestCase):
    """Test cases for EmailVerificationToken model"""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

    def test_email_verification_token_creation(self):
        """Test creating an email verification token"""
        token = EmailVerificationToken.objects.create(
            user=self.user,
            token='test_token_123',
            email_address='<EMAIL>',
            expires_at=timezone.now() + timedelta(hours=24)
        )
        
        self.assertEqual(token.user, self.user)
        self.assertEqual(token.token, 'test_token_123')
        self.assertEqual(token.email_address, '<EMAIL>')
        self.assertFalse(token.is_used)

    def test_email_verification_token_str_method(self):
        """Test string representation of email verification token"""
        token = EmailVerificationToken.objects.create(
            user=self.user,
            token='test_token_123',
            email_address='<EMAIL>',
            expires_at=timezone.now() + timedelta(hours=24)
        )
        
        # The new format shows truncated token
        expected_str = f"{self.user.email} - Token (test_tok...)"
        self.assertEqual(str(token), expected_str)

    def test_is_expired_property(self):
        """Test is_expired property"""
        # Non-expired token
        token = EmailVerificationToken.objects.create(
            user=self.user,
            token='test_token_123',
            email_address='<EMAIL>',
            expires_at=timezone.now() + timedelta(hours=24)
        )
        
        self.assertFalse(token.is_expired)
        
        # Expired token
        token.expires_at = timezone.now() - timedelta(hours=1)
        token.save()
        
        self.assertTrue(token.is_expired)

    def test_is_valid_property(self):
        """Test is_valid property"""
        token = EmailVerificationToken.objects.create(
            user=self.user,
            token='test_token_123',
            email_address='<EMAIL>',
            expires_at=timezone.now() + timedelta(hours=24)
        )
        
        # Valid token
        self.assertTrue(token.is_valid)
        
        # Used token
        token.is_used = True
        token.save()
        
        self.assertFalse(token.is_valid)
        
        # Expired token
        token.is_used = False
        token.expires_at = timezone.now() - timedelta(hours=1)
        token.save()
        
        self.assertFalse(token.is_valid)

    def test_mark_as_used_method(self):
        """Test mark_as_used method"""
        token = EmailVerificationToken.objects.create(
            user=self.user,
            token='test_token_123',
            email_address='<EMAIL>',
            expires_at=timezone.now() + timedelta(hours=24)
        )
        
        # Mark token as used
        token.mark_as_used()
        
        self.assertTrue(token.is_used)
        self.assertIsNotNone(token.used_at)

    def test_generate_token_method(self):
        """Test generate_token class method"""
        # Use the class method to generate a token
        token = EmailVerificationToken.generate_token(
            user=self.user,
            email_address='<EMAIL>'
        )
        
        self.assertIsNotNone(token)
        self.assertEqual(token.user, self.user)
        self.assertEqual(token.email_address, '<EMAIL>')
        self.assertFalse(token.is_used)
        self.assertIsNotNone(token.token)
        self.assertGreater(len(token.token), 0)

    def test_unique_token_constraint(self):
        """Test unique token constraint"""
        token1 = EmailVerificationToken.objects.create(
            user=self.user,
            token='unique_token_123',
            email_address='<EMAIL>',
            expires_at=timezone.now() + timedelta(hours=24)
        )
        
        # Try to create another token with same value (should fail)
        with self.assertRaises(Exception):
            EmailVerificationToken.objects.create(
                user=self.user,
                token='unique_token_123',
                email_address='<EMAIL>',
                expires_at=timezone.now() + timedelta(hours=24)
            )

    def test_cascade_deletion(self):
        """Test that tokens are deleted when user is deleted"""
        token = EmailVerificationToken.objects.create(
            user=self.user,
            token='test_token_123',
            email_address='<EMAIL>',
            expires_at=timezone.now() + timedelta(hours=24)
        )
        token_id = token.id
        
        self.user.delete()
        
        # Token should be deleted
        self.assertFalse(EmailVerificationToken.objects.filter(id=token_id).exists())


@pytest.mark.django_db
class SecurityModelsPytestTests:
    """Pytest-style tests for security models"""

    def test_security_model_integration(self):
        """Test integration between security models"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create security record
        security = UserSecurity.objects.create(user=user)
        
        # Create lockout record
        lockout = AccountLockout.objects.create(
            user=user,
            ip_address='***********',
            failed_attempts=3
        )
        
        # Create verification token
        token = EmailVerificationToken.objects.create(
            user=user,
            token='integration_token',
            email_address=user.email,
            expires_at=timezone.now() + timedelta(hours=24)
        )
        
        # Test relationships
        assert security.user == user
        assert lockout.user == user
        assert token.user == user
        
        # Test cleanup on user deletion
        user.delete()
        
        # Need to save the user first before filtering
        assert not UserSecurity.objects.filter(user_id=user.id).exists()
        assert not AccountLockout.objects.filter(user_id=user.id).exists()
        assert not EmailVerificationToken.objects.filter(user_id=user.id).exists()

    def test_security_workflow(self):
        """Test complete security workflow"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        security = UserSecurity.objects.create(user=user)
        
        # Simulate failed login attempts
        for i in range(3):
            security.record_failed_login()
        
        assert security.failed_login_attempts == 3
        
        # Lock account after too many attempts
        security.lock_account(30)
        assert security.is_account_locked
        
        # Unlock account
        security.unlock_account()
        assert not security.is_account_locked
        assert security.failed_login_attempts == 0 