{% extends 'notifications_app/base_notifications.html' %}
{% load static %}

{% block title %}{{ notification.title }} - Notification Detail - CozyWish{% endblock %}

{% block notifications_extra_css %}
<style>
    /* Professional Notification Detail Design - CozyWish Brand */

    /* Page Container */
    .notifications-wrapper {
        background: var(--cw-accent-light);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .notifications-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* Header Section */
    .notification-header {
        background: white;
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-sm);
        position: relative;
        overflow: hidden;
    }

    .notification-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--cw-gradient-brand-button);
    }

    .notification-header-content {
        display: flex;
        align-items: flex-start;
        gap: 1.5rem;
    }

    .notification-avatar {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        background: var(--cw-gradient-accent);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        border: 3px solid var(--cw-brand-primary);
        font-size: 1.5rem;
        color: var(--cw-brand-primary);
        box-shadow: var(--cw-shadow-md);
    }

    .notification-avatar.booking {
        background: linear-gradient(135deg, var(--cw-success) 0%, #059669 100%);
        color: white;
        border-color: var(--cw-success);
    }

    .notification-avatar.payment {
        background: linear-gradient(135deg, var(--cw-info) 0%, #0284c7 100%);
        color: white;
        border-color: var(--cw-info);
    }

    .notification-avatar.review {
        background: linear-gradient(135deg, var(--cw-warning) 0%, #d97706 100%);
        color: white;
        border-color: var(--cw-warning);
    }

    .notification-avatar.announcement {
        background: linear-gradient(135deg, var(--cw-error) 0%, #dc2626 100%);
        color: white;
        border-color: var(--cw-error);
    }

    .notification-avatar.message {
        background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
        color: white;
        border-color: #6366f1;
    }

    .notification-avatar.comment {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        color: white;
        border-color: #8b5cf6;
    }

    .notification-avatar.connect {
        background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
        color: white;
        border-color: #06b6d4;
    }

    .notification-header-main {
        flex: 1;
        min-width: 0;
    }

    .notification-category {
        display: inline-block;
        padding: 0.375rem 1rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        background: var(--cw-accent-light);
        color: var(--cw-brand-primary);
        border: 1px solid var(--cw-brand-accent);
    }

    .notification-category.booking {
        background: rgba(5, 150, 105, 0.1);
        color: var(--cw-success);
        border-color: rgba(5, 150, 105, 0.2);
    }

    .notification-category.payment {
        background: rgba(2, 132, 199, 0.1);
        color: var(--cw-info);
        border-color: rgba(2, 132, 199, 0.2);
    }

    .notification-category.review {
        background: rgba(217, 119, 6, 0.1);
        color: var(--cw-warning);
        border-color: rgba(217, 119, 6, 0.2);
    }

    .notification-category.announcement {
        background: rgba(220, 38, 38, 0.1);
        color: var(--cw-error);
        border-color: rgba(220, 38, 38, 0.2);
    }

    .notification-category.message {
        background: rgba(99, 102, 241, 0.1);
        color: #6366f1;
        border-color: rgba(99, 102, 241, 0.2);
    }

    .notification-category.comment {
        background: rgba(139, 92, 246, 0.1);
        color: #8b5cf6;
        border-color: rgba(139, 92, 246, 0.2);
    }

    .notification-category.connect {
        background: rgba(6, 182, 212, 0.1);
        color: #06b6d4;
        border-color: rgba(6, 182, 212, 0.2);
    }

    .notification-title {
        font-family: var(--cw-font-heading);
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        line-height: 1.3;
        letter-spacing: -0.02em;
    }

    .notification-meta {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        flex-wrap: wrap;
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        font-weight: 500;
    }

    .notification-meta .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        border-radius: 9999px;
        font-weight: 600;
    }

    .badge.bg-success {
        background: var(--cw-success) !important;
        color: white;
    }

    .badge.bg-danger {
        background: var(--cw-error) !important;
        color: white;
    }

    /* Content Section */
    .notification-content-section {
        background: white;
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .section-title {
        font-family: var(--cw-font-heading);
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid var(--cw-accent-light);
    }

    .notification-message {
        color: var(--cw-neutral-700);
        line-height: 1.7;
        font-size: 1.0625rem;
        font-weight: 400;
    }

    /* Metadata Section */
    .metadata-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 1.25rem;
    }

    .metadata-item {
        padding: 1.25rem;
        background: var(--cw-accent-light);
        border-radius: 0.75rem;
        border: 1px solid var(--cw-brand-accent);
        transition: all 0.2s ease;
    }

    .metadata-item:hover {
        background: var(--cw-brand-accent);
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
    }

    .metadata-label {
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-family: var(--cw-font-heading);
    }

    .metadata-value {
        color: var(--cw-neutral-700);
        font-weight: 500;
        font-size: 1rem;
    }

    /* Action Buttons */
    .action-section {
        background: white;
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.875rem 1.75rem;
        border-radius: 0.75rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 0.9375rem;
        font-family: var(--cw-font-heading);
        box-shadow: var(--cw-shadow-sm);
    }

    .action-btn.primary {
        background: var(--cw-gradient-brand-button);
        color: white;
    }

    .action-btn.primary:hover {
        background: var(--cw-brand-light);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .action-btn.secondary {
        background: white;
        color: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-primary);
    }

    .action-btn.secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .action-btn.danger {
        background: white;
        color: var(--cw-error);
        border: 2px solid var(--cw-error);
    }

    .action-btn.danger:hover {
        background: var(--cw-error);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    /* Back Navigation */
    .back-navigation {
        text-align: center;
        padding-top: 2rem;
    }

    .back-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem 2rem;
        background: var(--cw-gradient-accent);
        color: var(--cw-brand-primary);
        text-decoration: none;
        border-radius: 0.75rem;
        font-weight: 600;
        font-family: var(--cw-font-heading);
        transition: all 0.3s ease;
        box-shadow: var(--cw-shadow-sm);
        border: 1px solid var(--cw-brand-accent);
    }

    .back-btn:hover {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        text-decoration: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .notifications-wrapper {
            padding: 1rem 0;
        }

        .notifications-container {
            padding: 0 0.75rem;
        }

        .notification-header,
        .notification-content-section,
        .action-section {
            padding: 1.5rem;
        }

        .notification-header-content {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }

        .notification-avatar {
            width: 56px;
            height: 56px;
            font-size: 1.25rem;
        }

        .notification-title {
            font-size: 1.5rem;
        }

        .notification-meta {
            justify-content: center;
            gap: 1rem;
        }

        .action-buttons {
            flex-direction: column;
            align-items: stretch;
        }

        .action-btn {
            text-align: center;
            justify-content: center;
        }

        .metadata-grid {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 480px) {
        .notification-header,
        .notification-content-section,
        .action-section {
            padding: 1rem;
        }

        .notification-title {
            font-size: 1.25rem;
        }

        .notification-meta {
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block notifications_content %}
<div class="notifications-wrapper">
    <div class="notifications-container">
        <!-- System Messages -->
        {% if messages %}
        <div class="mb-3">
            {% for message in messages %}
            <div class="alert alert-info alert-dismissible fade show notification-alert-cw" role="alert">
                <i class="fas fa-info-circle me-2"></i>{{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Header Section -->
        <div class="notification-header">
            <div class="notification-header-content">
                <div class="notification-avatar {{ notification.notification_type }}">
                    {% if notification.notification_type == 'booking' %}
                        <i class="fas fa-calendar-check"></i>
                    {% elif notification.notification_type == 'payment' %}
                        <i class="fas fa-credit-card"></i>
                    {% elif notification.notification_type == 'review' %}
                        <i class="fas fa-star"></i>
                    {% elif notification.notification_type == 'announcement' %}
                        <i class="fas fa-bullhorn"></i>
                    {% elif notification.notification_type == 'message' %}
                        <i class="fas fa-envelope"></i>
                    {% elif notification.notification_type == 'comment' %}
                        <i class="fas fa-comment"></i>
                    {% elif notification.notification_type == 'connect' %}
                        <i class="fas fa-user-plus"></i>
                    {% else %}
                        <i class="fas fa-bell"></i>
                    {% endif %}
                </div>
                <div class="notification-header-main">
                    <span class="notification-category {{ notification.notification_type }}">
                        {% if notification.notification_type == 'booking' %}
                            Booking Update
                        {% elif notification.notification_type == 'payment' %}
                            Payment
                        {% elif notification.notification_type == 'review' %}
                            Review
                        {% elif notification.notification_type == 'announcement' %}
                            Announcement
                        {% elif notification.notification_type == 'message' %}
                            Message
                        {% elif notification.notification_type == 'comment' %}
                            Comment
                        {% elif notification.notification_type == 'connect' %}
                            Connect
                        {% else %}
                            {{ notification.get_notification_type_display }}
                        {% endif %}
                    </span>
                    <h1 class="notification-title">{{ notification.title }}</h1>
                    <div class="notification-meta">
                        <span><i class="fas fa-clock me-1"></i>{{ notification.created_at|date:"M d, Y, g:i A" }}</span>
                        {% if notification.read_status == 'read' %}
                            <span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>Read</span>
                        {% else %}
                            <span class="badge bg-danger"><i class="fas fa-exclamation-circle me-1"></i>Unread</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Section -->
        <div class="notification-content-section">
            <h2 class="section-title">
                <i class="fas fa-envelope-open me-2"></i>Message Content
            </h2>
            <div class="notification-message">
                {{ notification.message|linebreaks }}
            </div>
        </div>

        <!-- Metadata Section -->
        <div class="notification-content-section">
            <h2 class="section-title">
                <i class="fas fa-info-circle me-2"></i>Notification Details
            </h2>
            <div class="metadata-grid">
                <div class="metadata-item">
                    <div class="metadata-label">Notification ID</div>
                    <div class="metadata-value">#{{ notification.id }}</div>
                </div>
                <div class="metadata-item">
                    <div class="metadata-label">Type</div>
                    <div class="metadata-value">{{ notification.get_notification_type_display }}</div>
                </div>
                <div class="metadata-item">
                    <div class="metadata-label">Status</div>
                    <div class="metadata-value">
                        {% if notification.read_status == 'read' %}
                            <span class="text-success">Read</span>
                        {% else %}
                            <span class="text-danger">Unread</span>
                        {% endif %}
                    </div>
                </div>
                <div class="metadata-item">
                    <div class="metadata-label">Created</div>
                    <div class="metadata-value">{{ notification.created_at|date:"M d, Y, g:i A" }}</div>
                </div>
                {% if notification.user.first_name or notification.user.last_name %}
                <div class="metadata-item">
                    <div class="metadata-label">User</div>
                    <div class="metadata-value">{{ notification.user.first_name }} {{ notification.user.last_name }}</div>
                </div>
                {% elif notification.user.email %}
                <div class="metadata-item">
                    <div class="metadata-label">User</div>
                    <div class="metadata-value">{{ notification.user.email }}</div>
                </div>
                {% endif %}
                {% if notification.action_url %}
                <div class="metadata-item">
                    <div class="metadata-label">Action Available</div>
                    <div class="metadata-value">Yes</div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Action Section -->
        <div class="action-section">
            <h2 class="section-title">
                <i class="fas fa-cog me-2"></i>Actions
            </h2>
            <div class="action-buttons">
                {% if notification.read_status == 'read' %}
                <form method="post" action="{% url 'notifications_app:mark_notification_unread' notification.id %}" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="action-btn secondary">
                        <i class="fas fa-envelope"></i>
                        Mark as Unread
                    </button>
                </form>
                {% else %}
                <form method="post" action="{% url 'notifications_app:mark_notification_read' notification.id %}" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="action-btn primary">
                        <i class="fas fa-envelope-open"></i>
                        Mark as Read
                    </button>
                </form>
                {% endif %}

                {% if notification.action_url %}
                <a href="{{ notification.action_url }}" class="action-btn primary">
                    <i class="fas fa-external-link-alt"></i>
                    Take Action
                </a>
                {% endif %}

                <form method="post" action="{% url 'notifications_app:delete_notification' notification.id %}"
                      class="d-inline" onsubmit="return confirm('Are you sure you want to delete this notification? This action cannot be undone.')">
                    {% csrf_token %}
                    <button type="submit" class="action-btn danger">
                        <i class="fas fa-trash"></i>
                        Delete Notification
                    </button>
                </form>
            </div>
        </div>

        <!-- Back Navigation -->
        <div class="back-navigation">
            <a href="{% url 'notifications_app:notification_list' %}" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Notifications
            </a>
        </div>
    </div>
</div>
{% endblock %}


