# --- Standard Library Imports ---
from typing import Optional, Dict, Any, TYPE_CHECKING

# --- Django Imports ---
from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.core.exceptions import ValidationError
from django.core.validators import EmailValidator
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ..constants import (
    UserRoles, UserStatus, ModelConstants, ValidationMessages, DefaultValues
)
from ..utils.helpers import (
    calculate_customer_profile_completion, calculate_provider_profile_completion,
    get_profile_completion_suggestions
)


# --- Custom User Management ---

class CustomUserManager(BaseUserManager):
    """Custom user manager using email as unique identifier instead of username"""
    
    def create_user(self, email: str, password: Optional[str] = None, **extra_fields):
        """
        Create and save a regular user with the given email and password.
        
        Args:
            email (str): The email address for the user (will be normalized)
            password (Optional[str]): The password for the user (will be hashed)
            **extra_fields: Additional fields to set on the user model
            
        Returns:
            CustomUser: The created user instance
            
        Raises:
            ValueError: If email is not provided
        """
        if not email:
            raise ValueError(_('The Email field must be set'))
        
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email: str, password: Optional[str] = None, **extra_fields):
        """
        Create and save a superuser with the given email and password.
        
        Args:
            email (str): The email address for the superuser
            password (Optional[str]): The password for the superuser
            **extra_fields: Additional fields to set on the user model
            
        Returns:
            CustomUser: The created superuser instance
            
        Raises:
            ValueError: If is_staff or is_superuser is False
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)
        extra_fields.setdefault('role', UserRoles.ADMIN)
        extra_fields.setdefault('email_verified', True)
        extra_fields.setdefault('status', UserStatus.ACTIVE)

        if not extra_fields.get('is_staff'):
            raise ValueError(_('Superuser must have is_staff=True.'))
        if not extra_fields.get('is_superuser'):
            raise ValueError(_('Superuser must have is_superuser=True.'))

        return self.create_user(email, password, **extra_fields)


class ActiveUserManager(models.Manager):
    """
    Manager for active users only.
    
    This manager filters the queryset to only include users who are both
    active (is_active=True) and have an active status.
    """
    
    def get_queryset(self):
        """
        Return queryset filtered to active users only.
        
        Returns:
            QuerySet: Users with is_active=True and status=ACTIVE
        """
        return super().get_queryset().filter(is_active=True, status=UserStatus.ACTIVE)


class CustomerManager(models.Manager):
    """Manager for customer users"""
    
    def get_queryset(self):
        return super().get_queryset().filter(role=UserRoles.CUSTOMER)


class ProviderManager(models.Manager):
    """Manager for service provider users"""
    
    def get_queryset(self):
        return super().get_queryset().filter(role=UserRoles.SERVICE_PROVIDER)


class AdminManager(models.Manager):
    """Manager for admin users"""
    
    def get_queryset(self):
        return super().get_queryset().filter(role=UserRoles.ADMIN)


class CustomUser(AbstractUser):
    """
    Custom authentication model using email as unique identifier with role-based access.
    This model focuses on core authentication and authorization.
    """
    # Authentication configuration
    username = None
    email = models.EmailField(
        _('email address'), 
        unique=True, 
        max_length=ModelConstants.EMAIL_MAX_LENGTH,
        validators=[EmailValidator(message=ValidationMessages.INVALID_EMAIL)],
        error_messages={
            'unique': ValidationMessages.UNIQUE_EMAIL
        }
    )
    role = models.CharField(
        _('role'), 
        max_length=20, 
        choices=UserRoles.CHOICES, 
        default=DefaultValues.DEFAULT_ROLE,
        db_index=True
    )
    email_verified = models.BooleanField(
        _('email verified'),
        default=False,
        help_text=_('Whether the user has verified their email address')
    )
    status = models.CharField(
        _('status'),
        max_length=20,
        choices=UserStatus.CHOICES,
        default=DefaultValues.DEFAULT_STATUS,
        db_index=True
    )
    date_joined = models.DateTimeField(_('date joined'), default=timezone.now)
    last_login_ip = models.GenericIPAddressField(
        _('last login IP'),
        null=True,
        blank=True
    )
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []
    
    # Multiple managers for different query needs
    objects = CustomUserManager()
    active_users = ActiveUserManager()
    customers = CustomerManager()
    providers = ProviderManager()
    admins = AdminManager()

    class Meta:
        verbose_name = _('User')
        verbose_name_plural = _('Users')
        ordering = ['-date_joined']
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['role']),
            models.Index(fields=['status']),
            models.Index(fields=['email_verified']),
            models.Index(fields=['date_joined']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['email'],
                name='unique_user_email'
            ),
            models.CheckConstraint(
                check=models.Q(role__in=[UserRoles.CUSTOMER, UserRoles.SERVICE_PROVIDER, UserRoles.ADMIN]),
                name='valid_user_role'
            ),
        ]

    def __str__(self) -> str:
        return str(self.email)

    def clean(self):
        super().clean()
        self.email = self.__class__.objects.normalize_email(self.email)
        
        # Validate email format
        self.validate_email()
        
        # Check for duplicate email addresses
        if self.email:
            existing_user = self.__class__.objects.filter(
                email__iexact=self.email
            ).exclude(pk=self.pk).first()
            
            if existing_user:
                raise ValidationError({'email': ValidationMessages.UNIQUE_EMAIL})

    def validate_email(self):
        """
        Validate the email field and check for common issues.
        
        This method performs comprehensive email validation including:
        - Format validation
        - Domain validation
        - Blocked domain checking
        - Character validation
        
        Raises:
            ValidationError: If email is invalid
        """
        if not self.email:
            return
            
        # Basic format validation is handled by EmailValidator
        validator = EmailValidator(message=ValidationMessages.INVALID_EMAIL)
        try:
            validator(self.email)
        except ValidationError:
            raise ValidationError({'email': ValidationMessages.INVALID_EMAIL})
            
        # Additional custom validation can be added here
        # For example, checking against blocked domains
        blocked_domains = ['tempmail.com', 'guerrillamail.com', '10minutemail.com']
        domain = self.email.split('@')[1].lower()
        
        if domain in blocked_domains:
            raise ValidationError({
                'email': _('Email addresses from this domain are not allowed.')
            })

    def validate_phone(self, phone_number):
        """
        Validate phone number format and check for common issues.
        
        Args:
            phone_number (str): Phone number to validate
            
        Raises:
            ValidationError: If phone number is invalid
        """
        if not phone_number:
            return
            
        # Remove all non-digit characters for validation
        digits_only = ''.join(filter(str.isdigit, phone_number))
        
        # Check length (US phone numbers)
        if len(digits_only) not in [10, 11]:
            raise ValidationError(_('Phone number must be 10 or 11 digits long.'))

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    @property
    def is_customer(self) -> bool:
        return self.role == UserRoles.CUSTOMER

    @property
    def is_service_provider(self) -> bool:
        return self.role == UserRoles.SERVICE_PROVIDER

    @property
    def is_admin(self) -> bool:
        return self.role == UserRoles.ADMIN

    @property
    def is_verified(self) -> bool:
        return self.email_verified

    @property
    def is_active_user(self) -> bool:
        return self.is_active and self.status == UserStatus.ACTIVE

    @property
    def display_name(self) -> str:
        """Get the display name for the user"""
        if hasattr(self, 'user_profile') and self.user_profile.full_name:
            return self.user_profile.full_name
        return self.email

    def get_full_name(self) -> str:
        """Get the full name of the user"""
        if hasattr(self, 'user_profile'):
            return self.user_profile.full_name
        return self.email

    def get_short_name(self) -> str:
        """Get the short name of the user"""
        if hasattr(self, 'user_profile') and self.user_profile.first_name:
            return self.user_profile.first_name
        return self.email.split('@')[0]

    def verify_email(self):
        """Mark the user's email as verified"""
        self.email_verified = True
        self.save(update_fields=['email_verified'])

    def activate(self):
        """Activate the user account"""
        self.is_active = True
        self.status = UserStatus.ACTIVE
        self.save(update_fields=['is_active', 'status'])

    def deactivate(self):
        """Deactivate the user account"""
        self.is_active = False
        self.status = UserStatus.INACTIVE
        self.save(update_fields=['is_active', 'status'])

    def suspend(self):
        """Suspend the user account"""
        self.status = UserStatus.SUSPENDED
        self.save(update_fields=['status'])

    def get_profile_completion(self) -> Dict[str, Any]:
        """
        Calculate profile completion percentage and missing fields.
        
        Returns:
            Dict containing completion percentage and missing fields
        """
        if self.is_customer:
            return calculate_customer_profile_completion(self)
        elif self.is_service_provider:
            return calculate_provider_profile_completion(self)
        else:
            return {'percentage': 100, 'missing_fields': []}

    def get_profile_completion_suggestions(self) -> Dict[str, Any]:
        """
        Get suggestions for completing the user's profile.
        
        Returns:
            Dict containing suggestions for profile completion
        """
        return get_profile_completion_suggestions(self)

    @property
    def profile_completion_percentage(self) -> int:
        """Get the profile completion percentage"""
        return self.get_profile_completion().get('percentage', 0)

    @property
    def is_profile_complete(self) -> bool:
        """Check if the user's profile is complete"""
        return self.profile_completion_percentage >= 80

    @property
    def needs_profile_completion(self) -> bool:
        """
        Check if the user needs to complete their profile.
        
        Returns True if profile completion is below 50%
        """
        return self.profile_completion_percentage < 50 