/**
 * Profile Completion Progress Bar JavaScript
 * CozyWish - Interactive profile completion widget
 */

class ProfileCompletionWidget {
    constructor(element) {
        this.widget = element;
        this.percentage = parseInt(element.dataset.completionPercentage) || 0;
        this.progressBar = element.querySelector('.progress-bar');
        this.actionButtons = element.querySelectorAll('.suggestion-action');
        this.progressBarWrapper = element.querySelector('.progress-bar-wrapper');
        
        this.init();
    }

    init() {
        this.animateProgressBar();
        this.setupEventListeners();
        this.addAccessibilityFeatures();
        this.setupTooltips();
    }

    animateProgressBar() {
        if (!this.progressBar) return;

        // Start with 0% width
        this.progressBar.style.width = '0%';
        
        // Animate to target percentage after a short delay
        setTimeout(() => {
            this.progressBar.style.width = this.percentage + '%';
            
            // Add completion milestone effects
            this.addMilestoneEffects();
        }, 500);
    }

    addMilestoneEffects() {
        const milestones = [25, 50, 75, 100];
        const currentMilestone = milestones.find(m => this.percentage >= m);
        
        if (currentMilestone) {
            const step = this.widget.querySelector(`[data-step="${currentMilestone}"]`);
            if (step) {
                step.classList.add('active');
                step.style.color = '#4CAF50';
                step.style.fontWeight = '600';
            }
        }
    }

    setupEventListeners() {
        // Handle suggestion action buttons
        this.actionButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.handleSuggestionAction(button.dataset.action);
            });
        });

        // Handle progress bar click for interaction
        if (this.progressBarWrapper) {
            this.progressBarWrapper.addEventListener('click', (e) => {
                this.handleProgressBarClick(e);
            });
        }

        // Handle suggestion item clicks
        const suggestionItems = this.widget.querySelectorAll('.suggestion-item');
        suggestionItems.forEach(item => {
            item.addEventListener('click', (e) => {
                if (e.target.classList.contains('suggestion-action')) return;
                
                const actionButton = item.querySelector('.suggestion-action');
                if (actionButton) {
                    this.handleSuggestionAction(actionButton.dataset.action);
                }
            });
        });
    }

    handleSuggestionAction(action) {
        // Add loading state
        this.setLoadingState(true);
        
        // Analytics tracking
        this.trackAction(action);
        
        // Handle different actions
        switch(action) {
            case 'complete_basic_info':
                this.navigateToProfile();
                break;
            case 'upload_photo':
                this.navigateToProfileSection('profile-picture');
                break;
            case 'add_contact_info':
                this.navigateToProfileSection('contact-info');
                break;
            case 'add_location':
                this.navigateToProfileSection('location-info');
                break;
            case 'verify_email':
                this.navigateToEmailVerification();
                break;
            case 'complete_business_info':
                this.navigateToProviderProfile();
                break;
            case 'upload_logo':
                this.navigateToProviderSection('business-logo');
                break;
            default:
                console.log('Unknown action:', action);
                this.setLoadingState(false);
        }
    }

    navigateToProfile() {
        // Try to determine the correct profile URL
        const profileUrls = [
            '/accounts/profile/',
            '/accounts/customer/profile/',
            '/accounts/edit-profile/'
        ];
        
        this.tryNavigateToUrls(profileUrls);
    }

    navigateToProfileSection(section) {
        this.navigateToProfile();
        
        // Add section anchor after a short delay
        setTimeout(() => {
            if (window.location.hash !== `#${section}`) {
                window.location.hash = section;
            }
        }, 100);
    }

    navigateToEmailVerification() {
        const verificationUrls = [
            '/accounts/verify-email/',
            '/accounts/email-verification/',
            '/accounts/verify/'
        ];
        
        this.tryNavigateToUrls(verificationUrls);
    }

    navigateToProviderProfile() {
        const providerUrls = [
            '/accounts/provider/profile/',
            '/accounts/business-profile/',
            '/accounts/edit-provider-profile/'
        ];
        
        this.tryNavigateToUrls(providerUrls);
    }

    navigateToProviderSection(section) {
        this.navigateToProviderProfile();
        
        setTimeout(() => {
            if (window.location.hash !== `#${section}`) {
                window.location.hash = section;
            }
        }, 100);
    }

    tryNavigateToUrls(urls) {
        // Try the first URL, fallback to others if needed
        const primaryUrl = urls[0];
        
        // Check if URL exists via fetch (optional enhancement)
        window.location.href = primaryUrl;
    }

    handleProgressBarClick(e) {
        const rect = this.progressBarWrapper.getBoundingClientRect();
        const clickX = e.clientX - rect.left;
        const clickPercentage = (clickX / rect.width) * 100;
        
        // Show temporary tooltip
        this.showProgressTooltip(clickPercentage, e.clientX, e.clientY);
    }

    showProgressTooltip(percentage, x, y) {
        const tooltip = document.createElement('div');
        tooltip.className = 'progress-tooltip';
        tooltip.textContent = `${Math.round(percentage)}%`;
        tooltip.style.cssText = `
            position: fixed;
            top: ${y - 30}px;
            left: ${x - 20}px;
            background: black;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            pointer-events: none;
        `;
        
        document.body.appendChild(tooltip);
        
        setTimeout(() => {
            tooltip.remove();
        }, 2000);
    }

    setupTooltips() {
        const suggestionItems = this.widget.querySelectorAll('.suggestion-item');
        
        suggestionItems.forEach(item => {
            item.addEventListener('mouseenter', () => {
                this.showSuggestionTooltip(item);
            });
            
            item.addEventListener('mouseleave', () => {
                this.hideSuggestionTooltip();
            });
        });
    }

    showSuggestionTooltip(item) {
        const priority = item.classList.contains('priority-critical') ? 'Critical' :
                        item.classList.contains('priority-high') ? 'High Priority' :
                        item.classList.contains('priority-medium') ? 'Medium Priority' : 'Low Priority';
        
        const tooltip = document.createElement('div');
        tooltip.className = 'suggestion-tooltip';
        tooltip.textContent = `${priority} - Click to complete`;
        tooltip.style.cssText = `
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            margin-bottom: 5px;
        `;
        
        item.style.position = 'relative';
        item.appendChild(tooltip);
    }

    hideSuggestionTooltip() {
        const tooltip = this.widget.querySelector('.suggestion-tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }

    addAccessibilityFeatures() {
        // Add ARIA labels
        this.progressBar.setAttribute('role', 'progressbar');
        this.progressBar.setAttribute('aria-valuenow', this.percentage);
        this.progressBar.setAttribute('aria-valuemin', '0');
        this.progressBar.setAttribute('aria-valuemax', '100');
        this.progressBar.setAttribute('aria-label', `Profile completion: ${this.percentage}%`);
        
        // Add keyboard navigation
        this.actionButtons.forEach(button => {
            button.setAttribute('tabindex', '0');
            button.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.handleSuggestionAction(button.dataset.action);
                }
            });
        });
        
        // Add focus management
        this.widget.addEventListener('focusin', () => {
            this.widget.classList.add('focused');
        });
        
        this.widget.addEventListener('focusout', () => {
            this.widget.classList.remove('focused');
        });
    }

    setLoadingState(isLoading) {
        if (isLoading) {
            this.widget.classList.add('loading');
            this.actionButtons.forEach(button => {
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            });
        } else {
            this.widget.classList.remove('loading');
            this.actionButtons.forEach(button => {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-arrow-right"></i>';
            });
        }
    }

    trackAction(action) {
        // Analytics tracking (integrate with your analytics service)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'profile_completion_action', {
                action: action,
                current_percentage: this.percentage,
                user_role: document.body.dataset.userRole || 'unknown'
            });
        }
        
        // Console logging for development
        console.log('Profile completion action:', action, 'Current percentage:', this.percentage);
    }

    // Public method to update percentage (for AJAX updates)
    updatePercentage(newPercentage) {
        this.percentage = newPercentage;
        this.animateProgressBar();
        
        // Update percentage display
        const percentageElement = this.widget.querySelector('.completion-percentage');
        if (percentageElement) {
            percentageElement.textContent = newPercentage + '%';
        }
        
        // Update ARIA values
        this.progressBar.setAttribute('aria-valuenow', newPercentage);
        this.progressBar.setAttribute('aria-label', `Profile completion: ${newPercentage}%`);
    }

    // Public method to refresh suggestions
    refreshSuggestions() {
        // This would typically fetch new suggestions via AJAX
        // For now, we'll just reload the page
        window.location.reload();
    }
}

// Initialize all profile completion widgets on page load
document.addEventListener('DOMContentLoaded', function() {
    const widgets = document.querySelectorAll('.profile-completion-widget');
    
    widgets.forEach(widget => {
        new ProfileCompletionWidget(widget);
    });
});

// Export for use in other scripts
window.ProfileCompletionWidget = ProfileCompletionWidget; 