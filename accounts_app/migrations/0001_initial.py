# Generated by Django 5.2.4 on 2025-07-09 09:04

import accounts_app.utils.helpers
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="CustomUser",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        error_messages={
                            "unique": "A user with this email already exists."
                        },
                        max_length=254,
                        unique=True,
                        validators=[
                            django.core.validators.EmailValidator(
                                message="Enter a valid email address."
                            )
                        ],
                        verbose_name="email address",
                    ),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("customer", "Customer"),
                            ("service_provider", "Service Provider"),
                            ("admin", "Admin"),
                        ],
                        db_index=True,
                        default="customer",
                        max_length=20,
                        verbose_name="role",
                    ),
                ),
                (
                    "email_verified",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the user has verified their email address",
                        verbose_name="email verified",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("suspended", "Suspended"),
                            ("pending_verification", "Pending Verification"),
                        ],
                        db_index=True,
                        default="active",
                        max_length=20,
                        verbose_name="status",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "last_login_ip",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="last login IP"
                    ),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "User",
                "verbose_name_plural": "Users",
                "ordering": ["-date_joined"],
            },
        ),
        migrations.CreateModel(
            name="AccountLockout",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        help_text="IP address of the failed login attempt",
                        verbose_name="IP address",
                    ),
                ),
                (
                    "failed_attempts",
                    models.PositiveIntegerField(
                        default=1, verbose_name="failed attempts"
                    ),
                ),
                (
                    "locked_until",
                    models.DateTimeField(
                        blank=True,
                        help_text="Account is locked until this time",
                        null=True,
                        verbose_name="locked until",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="lockout_records",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="user",
                    ),
                ),
            ],
            options={
                "verbose_name": "Account Lockout",
                "verbose_name_plural": "Account Lockouts",
                "ordering": ["-updated_at"],
            },
        ),
        migrations.CreateModel(
            name="CustomerProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="last name"
                    ),
                ),
                (
                    "profile_picture",
                    models.ImageField(
                        blank=True,
                        help_text="Profile image uploaded to cloud storage (max 5MB, JPEG/PNG/GIF/WEBP)",
                        null=True,
                        upload_to=accounts_app.utils.helpers.get_customer_profile_image_path,
                        verbose_name="profile picture",
                    ),
                ),
                (
                    "gender",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("M", "Male"),
                            ("F", "Female"),
                            ("O", "Other"),
                            ("P", "Prefer not to say"),
                        ],
                        max_length=1,
                        verbose_name="gender",
                    ),
                ),
                (
                    "birth_month",
                    models.PositiveSmallIntegerField(
                        blank=True,
                        choices=[
                            (1, "January"),
                            (2, "February"),
                            (3, "March"),
                            (4, "April"),
                            (5, "May"),
                            (6, "June"),
                            (7, "July"),
                            (8, "August"),
                            (9, "September"),
                            (10, "October"),
                            (11, "November"),
                            (12, "December"),
                        ],
                        null=True,
                        verbose_name="birth month",
                    ),
                ),
                (
                    "birth_year",
                    models.PositiveSmallIntegerField(
                        blank=True,
                        help_text="Format: YYYY",
                        null=True,
                        verbose_name="birth year",
                    ),
                ),
                (
                    "phone_number",
                    models.CharField(
                        blank=True,
                        max_length=20,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Enter a valid phone number (e.g., +**********)",
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                        verbose_name="phone number",
                    ),
                ),
                (
                    "address",
                    models.CharField(
                        blank=True, max_length=255, verbose_name="street address"
                    ),
                ),
                (
                    "city",
                    models.CharField(blank=True, max_length=100, verbose_name="city"),
                ),
                (
                    "zip_code",
                    models.CharField(
                        blank=True, max_length=10, verbose_name="postal code"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="customer_profile",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="user account",
                    ),
                ),
            ],
            options={
                "verbose_name": "Customer Profile",
                "verbose_name_plural": "Customer Profiles",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="EmailPreferences",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "email_notifications_enabled",
                    models.BooleanField(
                        default=True,
                        help_text="Master switch for all email notifications",
                        verbose_name="email notifications enabled",
                    ),
                ),
                (
                    "account_notifications",
                    models.BooleanField(
                        default=True,
                        help_text="Account security, password changes, login alerts",
                        verbose_name="account notifications",
                    ),
                ),
                (
                    "booking_notifications",
                    models.BooleanField(
                        default=True,
                        help_text="Booking confirmations, cancellations, reminders",
                        verbose_name="booking notifications",
                    ),
                ),
                (
                    "payment_notifications",
                    models.BooleanField(
                        default=True,
                        help_text="Payment confirmations, receipts, refunds",
                        verbose_name="payment notifications",
                    ),
                ),
                (
                    "marketing_emails",
                    models.BooleanField(
                        default=False,
                        help_text="Promotional offers, newsletters, product updates",
                        verbose_name="marketing emails",
                    ),
                ),
                (
                    "event_notifications",
                    models.BooleanField(
                        default=True,
                        help_text="New events, event updates, early access notifications",
                        verbose_name="event notifications",
                    ),
                ),
                (
                    "review_notifications",
                    models.BooleanField(
                        default=True,
                        help_text="Review requests, review responses, rating updates",
                        verbose_name="review notifications",
                    ),
                ),
                (
                    "digest_frequency",
                    models.CharField(
                        choices=[
                            ("immediate", "Immediate"),
                            ("daily", "Daily Digest"),
                            ("weekly", "Weekly Digest"),
                            ("monthly", "Monthly Digest"),
                            ("disabled", "Disabled"),
                        ],
                        default="immediate",
                        help_text="How often to receive grouped notifications",
                        max_length=20,
                        verbose_name="digest frequency",
                    ),
                ),
                (
                    "email_format",
                    models.CharField(
                        choices=[("html", "HTML (Rich Text)"), ("plain", "Plain Text")],
                        default="html",
                        help_text="Preferred email format",
                        max_length=10,
                        verbose_name="email format",
                    ),
                ),
                (
                    "unsubscribe_all",
                    models.BooleanField(
                        default=False,
                        help_text="User has unsubscribed from all non-essential emails",
                        verbose_name="unsubscribe all",
                    ),
                ),
                (
                    "unsubscribe_date",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the user unsubscribed from all emails",
                        null=True,
                        verbose_name="unsubscribe date",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="email_preferences",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="user account",
                    ),
                ),
            ],
            options={
                "verbose_name": "Email Preferences",
                "verbose_name_plural": "Email Preferences",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="EmailVerificationToken",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "token",
                    models.CharField(
                        help_text="Unique token for email verification",
                        max_length=64,
                        unique=True,
                        verbose_name="verification token",
                    ),
                ),
                (
                    "email_address",
                    models.EmailField(
                        help_text="Email address being verified",
                        max_length=254,
                        verbose_name="email address",
                    ),
                ),
                (
                    "is_used",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this token has been used",
                        verbose_name="is used",
                    ),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        help_text="When this token expires", verbose_name="expires at"
                    ),
                ),
                (
                    "used_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When this token was used",
                        null=True,
                        verbose_name="used at",
                    ),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True,
                        help_text="IP address when token was created",
                        null=True,
                        verbose_name="IP address",
                    ),
                ),
                (
                    "user_agent",
                    models.TextField(
                        blank=True,
                        help_text="Browser user agent when token was created",
                        verbose_name="user agent",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="email_verification_tokens",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="user account",
                    ),
                ),
            ],
            options={
                "verbose_name": "Email Verification Token",
                "verbose_name_plural": "Email Verification Tokens",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="LoginAlert",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("ip_address", models.GenericIPAddressField(verbose_name="source IP")),
                (
                    "alert_type",
                    models.CharField(
                        choices=[
                            ("multiple_failures", "Multiple Failed Attempts"),
                            ("suspicious_ip", "Suspicious IP Address"),
                            ("unusual_location", "Unusual Login Location"),
                            ("brute_force", "Brute Force Attack"),
                        ],
                        max_length=20,
                        verbose_name="type",
                    ),
                ),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="severity",
                    ),
                ),
                ("description", models.TextField(verbose_name="details")),
                (
                    "attempt_count",
                    models.PositiveIntegerField(default=1, verbose_name="attempts"),
                ),
                (
                    "is_resolved",
                    models.BooleanField(default=False, verbose_name="resolved"),
                ),
                (
                    "resolved_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="resolution time"
                    ),
                ),
                (
                    "resolution_notes",
                    models.TextField(blank=True, verbose_name="resolution notes"),
                ),
                (
                    "created",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "resolved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resolved_alerts",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="resolved by",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="alerts",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="associated user",
                    ),
                ),
            ],
            options={
                "verbose_name": "Security Alert",
                "verbose_name_plural": "Security Alerts",
                "ordering": ["-created"],
            },
        ),
        migrations.CreateModel(
            name="LoginHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(auto_now_add=True, verbose_name="timestamp"),
                ),
                ("ip_address", models.GenericIPAddressField(verbose_name="IP address")),
                ("user_agent", models.TextField(blank=True, verbose_name="user agent")),
                (
                    "is_successful",
                    models.BooleanField(default=False, verbose_name="successful"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="login_history",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Login History",
                "verbose_name_plural": "Login History Records",
                "ordering": ["-timestamp"],
            },
        ),
        migrations.CreateModel(
            name="PasswordHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "password_hash",
                    models.CharField(
                        help_text="Hashed password for comparison",
                        max_length=128,
                        verbose_name="password hash",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="password_history",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="user",
                    ),
                ),
            ],
            options={
                "verbose_name": "Password History",
                "verbose_name_plural": "Password History Records",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ProfilePrivacySettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "profile_visibility",
                    models.CharField(
                        choices=[
                            ("public", "Public"),
                            ("private", "Private"),
                            ("friends_only", "Friends Only"),
                        ],
                        default="public",
                        help_text="Who can see your profile",
                        max_length=20,
                        verbose_name="profile visibility",
                    ),
                ),
                (
                    "show_email",
                    models.BooleanField(
                        default=False,
                        help_text="Show email address on profile",
                        verbose_name="show email",
                    ),
                ),
                (
                    "show_phone",
                    models.BooleanField(
                        default=False,
                        help_text="Show phone number on profile",
                        verbose_name="show phone",
                    ),
                ),
                (
                    "show_address",
                    models.BooleanField(
                        default=False,
                        help_text="Show address on profile",
                        verbose_name="show address",
                    ),
                ),
                (
                    "show_birth_date",
                    models.BooleanField(
                        default=False,
                        help_text="Show birth date on profile",
                        verbose_name="show birth date",
                    ),
                ),
                (
                    "show_gender",
                    models.BooleanField(
                        default=True,
                        help_text="Show gender on profile",
                        verbose_name="show gender",
                    ),
                ),
                (
                    "show_profile_picture",
                    models.BooleanField(
                        default=True,
                        help_text="Show profile picture",
                        verbose_name="show profile picture",
                    ),
                ),
                (
                    "show_last_seen",
                    models.BooleanField(
                        default=True,
                        help_text="Show last login/activity time",
                        verbose_name="show last seen",
                    ),
                ),
                (
                    "show_booking_history",
                    models.BooleanField(
                        default=False,
                        help_text="Show booking history to other users",
                        verbose_name="show booking history",
                    ),
                ),
                (
                    "show_reviews",
                    models.BooleanField(
                        default=True,
                        help_text="Show reviews written by you",
                        verbose_name="show reviews",
                    ),
                ),
                (
                    "show_favorites",
                    models.BooleanField(
                        default=False,
                        help_text="Show your favorite venues/services",
                        verbose_name="show favorites",
                    ),
                ),
                (
                    "discoverable_in_search",
                    models.BooleanField(
                        default=True,
                        help_text="Allow others to find you in search",
                        verbose_name="discoverable in search",
                    ),
                ),
                (
                    "allow_friend_requests",
                    models.BooleanField(
                        default=True,
                        help_text="Allow other users to send friend requests",
                        verbose_name="allow friend requests",
                    ),
                ),
                (
                    "allow_messages",
                    models.BooleanField(
                        default=True,
                        help_text="Allow other users to send you messages",
                        verbose_name="allow messages",
                    ),
                ),
                (
                    "allow_analytics",
                    models.BooleanField(
                        default=True,
                        help_text="Allow collection of usage analytics",
                        verbose_name="allow analytics",
                    ),
                ),
                (
                    "allow_personalized_ads",
                    models.BooleanField(
                        default=False,
                        help_text="Allow personalized advertising",
                        verbose_name="allow personalized ads",
                    ),
                ),
                (
                    "data_processing_consent",
                    models.BooleanField(
                        default=True,
                        help_text="Consent to process personal data",
                        verbose_name="data processing consent",
                    ),
                ),
                (
                    "marketing_consent",
                    models.BooleanField(
                        default=False,
                        help_text="Consent to receive marketing communications",
                        verbose_name="marketing consent",
                    ),
                ),
                (
                    "third_party_sharing",
                    models.BooleanField(
                        default=False,
                        help_text="Allow sharing data with trusted third parties",
                        verbose_name="third party sharing",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="privacy_settings",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="user account",
                    ),
                ),
            ],
            options={
                "verbose_name": "Privacy Settings",
                "verbose_name_plural": "Privacy Settings",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ProfileVerification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "verification_type",
                    models.CharField(
                        choices=[
                            ("email", "Email Verification"),
                            ("phone", "Phone Verification"),
                            ("identity", "Identity Verification"),
                            ("business", "Business Verification"),
                            ("address", "Address Verification"),
                            ("payment", "Payment Method Verification"),
                        ],
                        help_text="Type of verification",
                        max_length=20,
                        verbose_name="verification type",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("verified", "Verified"),
                            ("rejected", "Rejected"),
                            ("expired", "Expired"),
                        ],
                        default="pending",
                        help_text="Current verification status",
                        max_length=20,
                        verbose_name="status",
                    ),
                ),
                (
                    "verification_data",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Additional verification details and metadata",
                        verbose_name="verification data",
                    ),
                ),
                (
                    "verification_document",
                    models.FileField(
                        blank=True,
                        help_text="Supporting document for verification",
                        null=True,
                        upload_to="verification_documents/",
                        verbose_name="verification document",
                    ),
                ),
                (
                    "submitted_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="When verification was submitted",
                        verbose_name="submitted at",
                    ),
                ),
                (
                    "verified_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When verification was approved",
                        null=True,
                        verbose_name="verified at",
                    ),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When verification expires (if applicable)",
                        null=True,
                        verbose_name="expires at",
                    ),
                ),
                (
                    "rejection_reason",
                    models.TextField(
                        blank=True,
                        help_text="Reason for rejection if verification was denied",
                        verbose_name="rejection reason",
                    ),
                ),
                (
                    "admin_notes",
                    models.TextField(
                        blank=True,
                        help_text="Internal notes for staff",
                        verbose_name="admin notes",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="verifications",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="user account",
                    ),
                ),
                (
                    "verified_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="Staff member who approved verification",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="verified_users",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="verified by",
                    ),
                ),
            ],
            options={
                "verbose_name": "Profile Verification",
                "verbose_name_plural": "Profile Verifications",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ServiceProviderProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "legal_name",
                    models.CharField(
                        help_text="Official registered business name",
                        max_length=200,
                        verbose_name="legal business name",
                    ),
                ),
                (
                    "display_name",
                    models.CharField(
                        blank=True,
                        help_text="Name shown to customers (if different from legal name)",
                        max_length=200,
                        verbose_name="public display name",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Brief overview of services offered (500 characters max)",
                        max_length=500,
                        verbose_name="business description",
                    ),
                ),
                (
                    "logo",
                    models.ImageField(
                        blank=True,
                        help_text="Company logo displayed on your profile (max 5MB, JPEG/PNG/GIF/WEBP)",
                        null=True,
                        upload_to=accounts_app.utils.helpers.get_provider_profile_image_path,
                        verbose_name="business logo",
                    ),
                ),
                (
                    "phone",
                    models.CharField(
                        max_length=20,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Enter a valid phone number (e.g., +**********)",
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                        verbose_name="business phone",
                    ),
                ),
                (
                    "contact_name",
                    models.CharField(
                        help_text="Name of main business contact",
                        max_length=100,
                        verbose_name="primary contact",
                    ),
                ),
                (
                    "address",
                    models.CharField(max_length=255, verbose_name="street address"),
                ),
                ("city", models.CharField(max_length=100, verbose_name="city")),
                (
                    "state",
                    models.CharField(
                        choices=[
                            ("AL", "Alabama"),
                            ("AK", "Alaska"),
                            ("AZ", "Arizona"),
                            ("AR", "Arkansas"),
                            ("CA", "California"),
                            ("CO", "Colorado"),
                            ("CT", "Connecticut"),
                            ("DE", "Delaware"),
                            ("FL", "Florida"),
                            ("GA", "Georgia"),
                            ("HI", "Hawaii"),
                            ("ID", "Idaho"),
                            ("IL", "Illinois"),
                            ("IN", "Indiana"),
                            ("IA", "Iowa"),
                            ("KS", "Kansas"),
                            ("KY", "Kentucky"),
                            ("LA", "Louisiana"),
                            ("ME", "Maine"),
                            ("MD", "Maryland"),
                            ("MA", "Massachusetts"),
                            ("MI", "Michigan"),
                            ("MN", "Minnesota"),
                            ("MS", "Mississippi"),
                            ("MO", "Missouri"),
                            ("MT", "Montana"),
                            ("NE", "Nebraska"),
                            ("NV", "Nevada"),
                            ("NH", "New Hampshire"),
                            ("NJ", "New Jersey"),
                            ("NM", "New Mexico"),
                            ("NY", "New York"),
                            ("NC", "North Carolina"),
                            ("ND", "North Dakota"),
                            ("OH", "Ohio"),
                            ("OK", "Oklahoma"),
                            ("OR", "Oregon"),
                            ("PA", "Pennsylvania"),
                            ("RI", "Rhode Island"),
                            ("SC", "South Carolina"),
                            ("SD", "South Dakota"),
                            ("TN", "Tennessee"),
                            ("TX", "Texas"),
                            ("UT", "Utah"),
                            ("VT", "Vermont"),
                            ("VA", "Virginia"),
                            ("WA", "Washington"),
                            ("WV", "West Virginia"),
                            ("WI", "Wisconsin"),
                            ("WY", "Wyoming"),
                        ],
                        max_length=2,
                        verbose_name="state",
                    ),
                ),
                (
                    "county",
                    models.CharField(blank=True, max_length=100, verbose_name="county"),
                ),
                ("zip_code", models.CharField(max_length=10, verbose_name="ZIP code")),
                (
                    "ein",
                    models.CharField(
                        blank=True,
                        help_text="Employer Identification Number (optional)",
                        max_length=20,
                        verbose_name="EIN number",
                    ),
                ),
                ("website", models.URLField(blank=True, verbose_name="website URL")),
                (
                    "instagram",
                    models.URLField(blank=True, verbose_name="Instagram URL"),
                ),
                ("facebook", models.URLField(blank=True, verbose_name="Facebook URL")),
                (
                    "is_public",
                    models.BooleanField(
                        default=True,
                        help_text="Show business in public listings",
                        verbose_name="public visibility",
                    ),
                ),
                (
                    "venue_creation_tutorial_completed",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the user has completed the venue creation guided tour",
                        verbose_name="venue creation tutorial completed",
                    ),
                ),
                (
                    "created",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="service_provider_profile",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="business account",
                    ),
                ),
            ],
            options={
                "verbose_name": "Service Provider",
                "verbose_name_plural": "Service Providers",
                "ordering": ["-created"],
            },
        ),
        migrations.CreateModel(
            name="TeamMember",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Team member's full name",
                        max_length=100,
                        verbose_name="full name",
                    ),
                ),
                (
                    "position",
                    models.CharField(
                        help_text="Role or job title",
                        max_length=100,
                        verbose_name="position",
                    ),
                ),
                (
                    "photo",
                    models.ImageField(
                        blank=True,
                        help_text="Professional headshot (optional)",
                        null=True,
                        upload_to=accounts_app.utils.helpers.get_staff_profile_image_path,
                        verbose_name="profile photo",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Is this team member currently active?",
                        verbose_name="active status",
                    ),
                ),
                (
                    "created",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "service_provider",
                    models.ForeignKey(
                        help_text="Business this team member belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="team",
                        to="accounts_app.serviceproviderprofile",
                        verbose_name="service provider",
                    ),
                ),
            ],
            options={
                "verbose_name": "Team Member",
                "verbose_name_plural": "Team Members",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="UserPreferences",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "theme_preference",
                    models.CharField(
                        choices=[
                            ("light", "Light"),
                            ("dark", "Dark"),
                            ("auto", "Auto"),
                        ],
                        default="light",
                        max_length=10,
                        verbose_name="theme preference",
                    ),
                ),
                (
                    "language_preference",
                    models.CharField(
                        choices=[
                            ("en", "English"),
                            ("es", "Spanish"),
                            ("fr", "French"),
                            ("de", "German"),
                        ],
                        default="en",
                        max_length=5,
                        verbose_name="language preference",
                    ),
                ),
                (
                    "timezone_preference",
                    models.CharField(
                        choices=[
                            ("UTC", "UTC"),
                            ("US/Eastern", "US/Eastern"),
                            ("US/Central", "US/Central"),
                            ("US/Mountain", "US/Mountain"),
                            ("US/Pacific", "US/Pacific"),
                        ],
                        default="UTC",
                        max_length=50,
                        verbose_name="timezone preference",
                    ),
                ),
                (
                    "notification_preference",
                    models.CharField(
                        choices=[
                            ("email", "Email"),
                            ("sms", "SMS"),
                            ("push", "Push Notification"),
                            ("disabled", "Disabled"),
                        ],
                        default="email",
                        max_length=10,
                        verbose_name="notification preference",
                    ),
                ),
                (
                    "email_notifications",
                    models.BooleanField(
                        default=True, verbose_name="email notifications"
                    ),
                ),
                (
                    "marketing_emails",
                    models.BooleanField(default=False, verbose_name="marketing emails"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_preferences",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="user account",
                    ),
                ),
            ],
            options={
                "verbose_name": "User Preferences",
                "verbose_name_plural": "User Preferences",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="last name"
                    ),
                ),
                (
                    "profile_picture",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to=accounts_app.utils.helpers.get_customer_profile_image_path,
                        verbose_name="profile picture",
                    ),
                ),
                (
                    "phone_number",
                    models.CharField(
                        blank=True,
                        max_length=20,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Enter a valid phone number (e.g., +**********)",
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                        verbose_name="phone number",
                    ),
                ),
                (
                    "birth_date",
                    models.DateField(blank=True, null=True, verbose_name="birth date"),
                ),
                (
                    "gender",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("M", "Male"),
                            ("F", "Female"),
                            ("O", "Other"),
                            ("P", "Prefer not to say"),
                        ],
                        max_length=1,
                        verbose_name="gender",
                    ),
                ),
                (
                    "bio",
                    models.TextField(blank=True, max_length=500, verbose_name="bio"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_profile",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="user account",
                    ),
                ),
            ],
            options={
                "verbose_name": "User Profile",
                "verbose_name_plural": "User Profiles",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="UserSecurity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "two_factor_method",
                    models.CharField(
                        choices=[
                            ("disabled", "Disabled"),
                            ("sms", "SMS"),
                            ("email", "Email"),
                            ("authenticator", "Authenticator App"),
                        ],
                        default="disabled",
                        max_length=20,
                        verbose_name="two-factor method",
                    ),
                ),
                (
                    "backup_codes",
                    models.JSONField(
                        blank=True, default=list, verbose_name="backup codes"
                    ),
                ),
                (
                    "last_password_change",
                    models.DateTimeField(
                        default=django.utils.timezone.now,
                        verbose_name="last password change",
                    ),
                ),
                (
                    "failed_login_attempts",
                    models.PositiveIntegerField(
                        default=0, verbose_name="failed login attempts"
                    ),
                ),
                (
                    "account_locked_until",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="account locked until"
                    ),
                ),
                (
                    "last_activity",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last activity"
                    ),
                ),
                (
                    "session_key",
                    models.CharField(
                        blank=True, max_length=40, verbose_name="session key"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_security",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="user account",
                    ),
                ),
            ],
            options={
                "verbose_name": "User Security",
                "verbose_name_plural": "User Security",
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="customuser",
            index=models.Index(fields=["email"], name="accounts_ap_email_71e114_idx"),
        ),
        migrations.AddIndex(
            model_name="customuser",
            index=models.Index(fields=["role"], name="accounts_ap_role_99f07c_idx"),
        ),
        migrations.AddIndex(
            model_name="customuser",
            index=models.Index(fields=["status"], name="accounts_ap_status_23f5e2_idx"),
        ),
        migrations.AddIndex(
            model_name="customuser",
            index=models.Index(
                fields=["email_verified"], name="accounts_ap_email_v_1696cd_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="customuser",
            index=models.Index(
                fields=["date_joined"], name="accounts_ap_date_jo_11b113_idx"
            ),
        ),
        migrations.AddConstraint(
            model_name="customuser",
            constraint=models.UniqueConstraint(
                fields=("email",), name="unique_user_email"
            ),
        ),
        migrations.AddConstraint(
            model_name="customuser",
            constraint=models.CheckConstraint(
                condition=models.Q(
                    ("role__in", ["customer", "service_provider", "admin"])
                ),
                name="valid_user_role",
            ),
        ),
        migrations.AddIndex(
            model_name="accountlockout",
            index=models.Index(fields=["ip_address"], name="lockout_ip_idx"),
        ),
        migrations.AddIndex(
            model_name="accountlockout",
            index=models.Index(fields=["user"], name="lockout_user_idx"),
        ),
        migrations.AddIndex(
            model_name="accountlockout",
            index=models.Index(fields=["locked_until"], name="lockout_until_idx"),
        ),
        migrations.AddConstraint(
            model_name="accountlockout",
            constraint=models.UniqueConstraint(
                condition=models.Q(("locked_until__isnull", False)),
                fields=("ip_address",),
                name="unique_ip_lockout",
            ),
        ),
        migrations.AddIndex(
            model_name="customerprofile",
            index=models.Index(fields=["user"], name="customer_profile_user_idx"),
        ),
        migrations.AddIndex(
            model_name="customerprofile",
            index=models.Index(
                fields=["created_at"], name="customer_profile_created_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="emailpreferences",
            index=models.Index(fields=["user"], name="email_preferences_user_idx"),
        ),
        migrations.AddIndex(
            model_name="emailpreferences",
            index=models.Index(
                fields=["created_at"], name="email_preferences_created_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="emailpreferences",
            index=models.Index(
                fields=["digest_frequency"], name="email_preferences_digest_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="emailverificationtoken",
            index=models.Index(fields=["token"], name="email_token_idx"),
        ),
        migrations.AddIndex(
            model_name="emailverificationtoken",
            index=models.Index(fields=["user"], name="email_token_user_idx"),
        ),
        migrations.AddIndex(
            model_name="emailverificationtoken",
            index=models.Index(fields=["expires_at"], name="email_token_expires_idx"),
        ),
        migrations.AddIndex(
            model_name="emailverificationtoken",
            index=models.Index(fields=["is_used"], name="email_token_used_idx"),
        ),
        migrations.AddConstraint(
            model_name="emailverificationtoken",
            constraint=models.UniqueConstraint(
                fields=("token",), name="unique_verification_token"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="loginalert",
            unique_together={("ip_address", "alert_type", "is_resolved")},
        ),
        migrations.AddIndex(
            model_name="loginhistory",
            index=models.Index(fields=["timestamp"], name="login_timestamp_idx"),
        ),
        migrations.AddIndex(
            model_name="loginhistory",
            index=models.Index(fields=["ip_address"], name="login_ip_idx"),
        ),
        migrations.AddIndex(
            model_name="loginhistory",
            index=models.Index(fields=["is_successful"], name="login_success_idx"),
        ),
        migrations.AddIndex(
            model_name="passwordhistory",
            index=models.Index(
                fields=["user", "-created_at"], name="password_history_user_date_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="profileprivacysettings",
            index=models.Index(fields=["user"], name="privacy_user_idx"),
        ),
        migrations.AddIndex(
            model_name="profileprivacysettings",
            index=models.Index(
                fields=["profile_visibility"], name="privacy_visibility_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="profileprivacysettings",
            index=models.Index(
                fields=["discoverable_in_search"], name="privacy_discoverable_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="profileverification",
            index=models.Index(fields=["user"], name="verification_user_idx"),
        ),
        migrations.AddIndex(
            model_name="profileverification",
            index=models.Index(
                fields=["verification_type"], name="verification_type_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="profileverification",
            index=models.Index(fields=["status"], name="verification_status_idx"),
        ),
        migrations.AddIndex(
            model_name="profileverification",
            index=models.Index(
                fields=["verified_at"], name="verification_verified_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="profileverification",
            index=models.Index(fields=["expires_at"], name="verification_expires_idx"),
        ),
        migrations.AlterUniqueTogether(
            name="profileverification",
            unique_together={("user", "verification_type")},
        ),
        migrations.AddIndex(
            model_name="serviceproviderprofile",
            index=models.Index(fields=["user"], name="provider_profile_user_idx"),
        ),
        migrations.AddIndex(
            model_name="serviceproviderprofile",
            index=models.Index(fields=["created"], name="provider_profile_created_idx"),
        ),
        migrations.AddIndex(
            model_name="serviceproviderprofile",
            index=models.Index(fields=["state"], name="provider_profile_state_idx"),
        ),
        migrations.AddIndex(
            model_name="serviceproviderprofile",
            index=models.Index(
                fields=["is_public"], name="provider_profile_public_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="teammember",
            index=models.Index(
                fields=["service_provider"], name="team_member_provider_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="teammember",
            index=models.Index(fields=["is_active"], name="team_member_active_idx"),
        ),
        migrations.AddConstraint(
            model_name="teammember",
            constraint=models.UniqueConstraint(
                fields=("service_provider", "name"), name="unique_team_member"
            ),
        ),
        migrations.AddIndex(
            model_name="userpreferences",
            index=models.Index(fields=["user"], name="user_preferences_user_idx"),
        ),
        migrations.AddIndex(
            model_name="userpreferences",
            index=models.Index(
                fields=["created_at"], name="user_preferences_created_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="userprofile",
            index=models.Index(fields=["user"], name="user_profile_user_idx"),
        ),
        migrations.AddIndex(
            model_name="userprofile",
            index=models.Index(fields=["created_at"], name="user_profile_created_idx"),
        ),
        migrations.AddIndex(
            model_name="usersecurity",
            index=models.Index(fields=["user"], name="accounts_ap_user_id_1b425d_idx"),
        ),
        migrations.AddIndex(
            model_name="usersecurity",
            index=models.Index(
                fields=["last_password_change"], name="accounts_ap_last_pa_75a5ac_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="usersecurity",
            index=models.Index(
                fields=["failed_login_attempts"], name="accounts_ap_failed__2948bb_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="usersecurity",
            index=models.Index(
                fields=["account_locked_until"], name="accounts_ap_account_a10d1d_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="usersecurity",
            index=models.Index(
                fields=["last_activity"], name="accounts_ap_last_ac_1f2a62_idx"
            ),
        ),
    ]
