/**
 * CozyWish Form Components JavaScript
 * Enhanced form functionality for better user experience
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize form components
    initializeFormValidation();
    initializeSearchForms();
    initializeFileUploads();
    initializePasswordStrength();
    initializeFormAccessibility();
    
});

/**
 * Initialize form validation with real-time feedback
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            // Real-time validation feedback
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                clearFieldValidation(this);
            });
        });
        
        // Form submission validation
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                showFormErrors(this);
            }
        });
    });
}

/**
 * Validate individual form field
 */
function validateField(field) {
    const value = field.value.trim();
    const type = field.type;
    const required = field.hasAttribute('required');
    
    // Clear previous validation
    clearFieldValidation(field);
    
    // Check if required field is empty
    if (required && !value) {
        showFieldError(field, 'This field is required.');
        return false;
    }
    
    // Email validation
    if (type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            showFieldError(field, 'Please enter a valid email address.');
            return false;
        }
    }
    
    // Phone validation
    if (type === 'tel' && value) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        if (!phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))) {
            showFieldError(field, 'Please enter a valid phone number.');
            return false;
        }
    }
    
    // Password validation
    if (type === 'password' && value) {
        if (value.length < 8) {
            showFieldError(field, 'Password must be at least 8 characters long.');
            return false;
        }
    }
    
    // Show success state for valid fields
    if (value && !field.classList.contains('is-invalid')) {
        showFieldSuccess(field);
    }
    
    return true;
}

/**
 * Show field error state
 */
function showFieldError(field, message) {
    field.classList.add('is-invalid');
    field.classList.remove('is-valid');
    
    // Remove existing feedback
    const existingFeedback = field.parentNode.querySelector('.invalid-feedback');
    if (existingFeedback) {
        existingFeedback.remove();
    }
    
    // Add error message
    const feedback = document.createElement('div');
    feedback.className = 'invalid-feedback';
    feedback.textContent = message;
    field.parentNode.appendChild(feedback);
}

/**
 * Show field success state
 */
function showFieldSuccess(field) {
    field.classList.add('is-valid');
    field.classList.remove('is-invalid');
    
    // Remove existing feedback
    const existingFeedback = field.parentNode.querySelector('.valid-feedback');
    if (existingFeedback) {
        existingFeedback.remove();
    }
    
    // Add success message
    const feedback = document.createElement('div');
    feedback.className = 'valid-feedback';
    feedback.textContent = 'Looks good!';
    field.parentNode.appendChild(feedback);
}

/**
 * Clear field validation state
 */
function clearFieldValidation(field) {
    field.classList.remove('is-valid', 'is-invalid');
    
    // Remove feedback messages
    const feedback = field.parentNode.querySelector('.valid-feedback, .invalid-feedback');
    if (feedback) {
        feedback.remove();
    }
}

/**
 * Validate entire form
 */
function validateForm(form) {
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * Show form errors summary
 */
function showFormErrors(form) {
    const invalidFields = form.querySelectorAll('.is-invalid');
    
    if (invalidFields.length > 0) {
        // Scroll to first error
        invalidFields[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // Focus first error field
        invalidFields[0].focus();
    }
}

/**
 * Initialize search forms with enhanced functionality
 */
function initializeSearchForms() {
    const searchForms = document.querySelectorAll('.input-group-search');
    
    searchForms.forEach(searchForm => {
        const input = searchForm.querySelector('input');
        const icon = searchForm.querySelector('.input-group-text i');
        
        if (input && icon) {
            // Clear search functionality
            input.addEventListener('input', function() {
                if (this.value) {
                    // Add clear button
                    if (!searchForm.querySelector('.clear-search')) {
                        const clearBtn = document.createElement('button');
                        clearBtn.type = 'button';
                        clearBtn.className = 'btn btn-sm btn-outline-secondary clear-search';
                        clearBtn.innerHTML = '<i class="fas fa-times"></i>';
                        clearBtn.style.position = 'absolute';
                        clearBtn.style.right = '10px';
                        clearBtn.style.top = '50%';
                        clearBtn.style.transform = 'translateY(-50%)';
                        clearBtn.style.zIndex = '20';
                        
                        clearBtn.addEventListener('click', function() {
                            input.value = '';
                            input.focus();
                            this.remove();
                        });
                        
                        searchForm.style.position = 'relative';
                        searchForm.appendChild(clearBtn);
                    }
                } else {
                    // Remove clear button
                    const clearBtn = searchForm.querySelector('.clear-search');
                    if (clearBtn) {
                        clearBtn.remove();
                    }
                }
            });
        }
    });
}

/**
 * Initialize file upload enhancements
 */
function initializeFileUploads() {
    const fileInputs = document.querySelectorAll('input[type="file"]');
    
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const files = this.files;
            const maxSize = 5 * 1024 * 1024; // 5MB
            
            if (files.length > 0) {
                const file = files[0];
                
                // Check file size
                if (file.size > maxSize) {
                    showFieldError(this, 'File size must be less than 5MB.');
                    this.value = '';
                    return;
                }
                
                // Check file type
                const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
                if (!allowedTypes.includes(file.type)) {
                    showFieldError(this, 'Please upload a valid image (JPEG, PNG, GIF) or PDF file.');
                    this.value = '';
                    return;
                }
                
                // Show success message
                showFieldSuccess(this);
            }
        });
    });
}

/**
 * Initialize password strength indicator
 */
function initializePasswordStrength() {
    const passwordInputs = document.querySelectorAll('input[type="password"]');
    
    passwordInputs.forEach(input => {
        input.addEventListener('input', function() {
            const password = this.value;
            const strengthIndicator = this.parentNode.querySelector('.password-strength');
            
            if (!strengthIndicator) {
                const indicator = document.createElement('div');
                indicator.className = 'password-strength mt-2';
                indicator.innerHTML = `
                    <div class="progress" style="height: 4px;">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted mt-1 d-block">Password strength: <span class="strength-text">Weak</span></small>
                `;
                this.parentNode.appendChild(indicator);
            }
            
            const strength = calculatePasswordStrength(password);
            updatePasswordStrengthIndicator(this, strength);
        });
    });
}

/**
 * Calculate password strength
 */
function calculatePasswordStrength(password) {
    let score = 0;
    
    if (password.length >= 8) score += 1;
    if (password.match(/[a-z]/)) score += 1;
    if (password.match(/[A-Z]/)) score += 1;
    if (password.match(/[0-9]/)) score += 1;
    if (password.match(/[^a-zA-Z0-9]/)) score += 1;
    
    return Math.min(score, 5);
}

/**
 * Update password strength indicator
 */
function updatePasswordStrengthIndicator(input, strength) {
    const indicator = input.parentNode.querySelector('.password-strength');
    if (!indicator) return;
    
    const progressBar = indicator.querySelector('.progress-bar');
    const strengthText = indicator.querySelector('.strength-text');
    
    const strengthLabels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong', 'Very Strong'];
    const strengthColors = ['danger', 'warning', 'warning', 'info', 'success', 'success'];
    const strengthPercentages = [0, 20, 40, 60, 80, 100];
    
    progressBar.style.width = strengthPercentages[strength] + '%';
    progressBar.className = `progress-bar bg-${strengthColors[strength]}`;
    strengthText.textContent = strengthLabels[strength];
}

/**
 * Initialize form accessibility enhancements
 */
function initializeFormAccessibility() {
    // Add ARIA labels to form elements
    const inputs = document.querySelectorAll('input, select, textarea');
    
    inputs.forEach(input => {
        if (!input.getAttribute('aria-label') && !input.getAttribute('aria-labelledby')) {
            const label = input.parentNode.querySelector('label');
            if (label && label.getAttribute('for') === input.id) {
                input.setAttribute('aria-label', label.textContent);
            }
        }
    });
    
    // Add skip links for keyboard navigation
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        if (!form.querySelector('.skip-link')) {
            const skipLink = document.createElement('a');
            skipLink.href = '#';
            skipLink.className = 'skip-link sr-only sr-only-focusable';
            skipLink.textContent = 'Skip to form content';
            skipLink.style.position = 'absolute';
            skipLink.style.left = '-9999px';
            skipLink.style.zIndex = '9999';
            
            skipLink.addEventListener('focus', function() {
                this.style.left = '10px';
                this.style.top = '10px';
            });
            
            skipLink.addEventListener('blur', function() {
                this.style.left = '-9999px';
            });
            
            form.insertBefore(skipLink, form.firstChild);
        }
    });
}

/**
 * Utility function to show toast notifications
 */
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;
    
    // Create toast container if it doesn't exist
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }
    
    toastContainer.appendChild(toast);
    
    // Initialize and show toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

// Export functions for use in other scripts
window.CozyWishForms = {
    validateField,
    validateForm,
    showToast,
    initializeFormValidation,
    initializeSearchForms,
    initializeFileUploads,
    initializePasswordStrength,
    initializeFormAccessibility
}; 