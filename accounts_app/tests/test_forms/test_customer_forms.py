"""
Simplified MVP tests for customer forms in accounts_app.
Tests basic form functionality without complex validation dependencies.
"""

import pytest
from django.contrib.auth import get_user_model
from django.test import TestCase
from django.core.exceptions import ValidationError
from unittest.mock import patch, MagicMock

from accounts_app.constants import UserR<PERSON><PERSON>, UserStatus, Gender
from accounts_app.forms.customer import (
    CustomerSignupForm, CustomerLoginForm, CustomerProfileForm,
    CustomerPasswordChangeForm
)
from accounts_app.models.profiles import CustomerProfile

User = get_user_model()


class CustomerSignupFormTests(TestCase):
    """Test cases for CustomerSignupForm"""

    def test_customer_signup_form_fields(self):
        """Test that CustomerSignupForm has the expected fields"""
        form = CustomerSignupForm()
        expected_fields = ['email', 'password1', 'password2', 'agree_to_terms']
        
        for field_name in expected_fields:
            self.assertIn(field_name, form.fields)

    @patch('django.contrib.auth.password_validation.validate_password')
    def test_customer_signup_form_valid_data(self, mock_validate_password):
        """Test customer signup form with valid data"""
        # Mock password validation to avoid complex validation issues
        mock_validate_password.return_value = None
        
        form_data = {
            'email': '<EMAIL>',
            'password1': 'SecurePassX!2',
            'password2': 'SecurePassX!2',
            'agree_to_terms': True
        }
        
        form = CustomerSignupForm(data=form_data)
        if not form.is_valid():
            print(f"Form errors: {form.errors}")
        self.assertTrue(form.is_valid())

    @patch('django.contrib.auth.password_validation.validate_password')
    def test_customer_signup_form_invalid_email(self, mock_validate_password):
        """Test customer signup form with invalid email"""
        # Mock password validation to avoid complex validation issues
        mock_validate_password.return_value = None
        
        form_data = {
            'email': 'invalid-email',
            'password1': 'SecurePassX!2',
            'password2': 'SecurePassX!2',
            'agree_to_terms': True
        }
        
        form = CustomerSignupForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)

    @patch('django.contrib.auth.password_validation.validate_password')
    def test_customer_signup_form_password_mismatch(self, mock_validate_password):
        """Test customer signup form with password mismatch"""
        # Mock password validation to avoid complex validation issues
        mock_validate_password.return_value = None
        
        form_data = {
            'email': '<EMAIL>',
            'password1': 'SecurePassX!2',
            'password2': 'DifferentPass123!',
            'agree_to_terms': True
        }
        
        form = CustomerSignupForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('password2', form.errors)

    @patch('django.contrib.auth.password_validation.validate_password')
    def test_customer_signup_form_missing_required_fields(self, mock_validate_password):
        """Test customer signup form with missing required fields"""
        # Mock password validation to avoid complex validation issues
        mock_validate_password.return_value = None
        
        form_data = {
            'email': '<EMAIL>',
            'password1': 'SecurePassX!2',
            'password2': 'SecurePassX!2',
            # Missing agree_to_terms
        }
        
        form = CustomerSignupForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('agree_to_terms', form.errors)

    @patch('django.contrib.auth.password_validation.validate_password')
    def test_customer_signup_form_duplicate_email(self, mock_validate_password):
        """Test customer signup form with duplicate email"""
        # Mock password validation to avoid complex validation issues
        mock_validate_password.return_value = None
        
        # Create existing user
        User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER
        )
        
        form_data = {
            'email': '<EMAIL>',
            'password1': 'SecurePassX!2',
            'password2': 'SecurePassX!2',
            'agree_to_terms': True
        }
        
        form = CustomerSignupForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)

    @patch('django.contrib.auth.password_validation.validate_password')
    def test_customer_signup_form_terms_not_accepted(self, mock_validate_password):
        """Test customer signup form with terms not accepted"""
        # Mock password validation to avoid complex validation issues
        mock_validate_password.return_value = None
        
        form_data = {
            'email': '<EMAIL>',
            'password1': 'SecurePassX!2',
            'password2': 'SecurePassX!2',
            'agree_to_terms': False
        }
        
        form = CustomerSignupForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('agree_to_terms', form.errors)

    @patch('django.contrib.auth.password_validation.validate_password')
    def test_customer_signup_form_save(self, mock_validate_password):
        """Test customer signup form save method"""
        # Mock password validation to avoid complex validation issues
        mock_validate_password.return_value = None
        
        form_data = {
            'email': '<EMAIL>',
            'password1': 'SecurePassX!2',
            'password2': 'SecurePassX!2',
            'agree_to_terms': True
        }
        
        form = CustomerSignupForm(data=form_data)
        self.assertTrue(form.is_valid())
        
        user = form.save()
        
        # Check user was created correctly
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.role, UserRoles.CUSTOMER)
        self.assertFalse(user.email_verified)

    @patch('django.contrib.auth.password_validation.validate_password')
    def test_customer_signup_form_clean_email(self, mock_validate_password):
        """Test customer signup form email cleaning"""
        # Mock password validation to avoid complex validation issues
        mock_validate_password.return_value = None
        
        form_data = {
            'email': '<EMAIL>',
            'password1': 'SecurePassX!2',
            'password2': 'SecurePassX!2',
            'agree_to_terms': True
        }
        
        form = CustomerSignupForm(data=form_data)
        self.assertTrue(form.is_valid())
        
        # Email should be normalized to lowercase
        self.assertEqual(form.cleaned_data['email'], '<EMAIL>')


class CustomerLoginFormTests(TestCase):
    """Test cases for CustomerLoginForm"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER,
            status=UserStatus.ACTIVE,
            email_verified=True
        )

    def test_customer_login_form_fields(self):
        """Test that CustomerLoginForm has the expected fields"""
        form = CustomerLoginForm()
        expected_fields = ['email', 'password']
        
        for field_name in expected_fields:
            self.assertIn(field_name, form.fields)

    def test_customer_login_form_valid_data(self):
        """Test customer login form with valid data"""
        form_data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        
        form = CustomerLoginForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_customer_login_form_invalid_email(self):
        """Test customer login form with invalid email"""
        form_data = {
            'email': 'invalid-email',
            'password': 'testpass123'
        }
        
        form = CustomerLoginForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)

    def test_customer_login_form_missing_fields(self):
        """Test customer login form with missing fields"""
        form_data = {
            'email': '<EMAIL>',
            # Missing password
        }
        
        form = CustomerLoginForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('password', form.errors)

    def test_customer_login_form_empty_data(self):
        """Test customer login form with empty data"""
        form = CustomerLoginForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)
        self.assertIn('password', form.errors)


class CustomerProfileFormTests(TestCase):
    """Test cases for CustomerProfileForm"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER
        )
        self.profile = CustomerProfile.objects.create(user=self.user)

    def test_customer_profile_form_fields(self):
        """Test that CustomerProfileForm has the expected fields"""
        form = CustomerProfileForm(instance=self.profile)
        expected_fields = [
            'first_name', 'last_name', 'phone_number', 'gender',
            'birth_month', 'birth_year', 'address', 'city', 'zip_code',
            'profile_picture'
        ]
        
        for field_name in expected_fields:
            self.assertIn(field_name, form.fields)

    def test_customer_profile_form_valid_data(self):
        """Test customer profile form with valid data"""
        form_data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'phone_number': '+1234567890',
            'gender': Gender.MALE,
            'birth_month': '1',
            'birth_year': '1990',
            'address': '123 Main St',
            'city': 'New York',
            'zip_code': '10001'
        }
        
        form = CustomerProfileForm(data=form_data, instance=self.profile)
        self.assertTrue(form.is_valid())

    def test_customer_profile_form_invalid_phone(self):
        """Test customer profile form with invalid phone number"""
        form_data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'phone_number': 'invalid-phone',
            'gender': Gender.MALE,
            'birth_month': '1',
            'birth_year': '1990',
            'address': '123 Main St',
            'city': 'New York',
            'zip_code': '10001'
        }
        
        form = CustomerProfileForm(data=form_data, instance=self.profile)
        self.assertFalse(form.is_valid())
        self.assertIn('phone_number', form.errors)

    def test_customer_profile_form_invalid_zip_code(self):
        """Test customer profile form with invalid zip code"""
        form_data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'phone_number': '+1234567890',
            'gender': Gender.MALE,
            'birth_month': '1',
            'birth_year': '1990',
            'address': '123 Main St',
            'city': 'New York',
            'zip_code': 'invalid-zip'
        }
        
        form = CustomerProfileForm(data=form_data, instance=self.profile)
        self.assertFalse(form.is_valid())
        self.assertIn('zip_code', form.errors)

    def test_customer_profile_form_save(self):
        """Test customer profile form save method"""
        form_data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'phone_number': '+1234567890',
            'gender': Gender.MALE,
            'birth_month': '1',
            'birth_year': '1990',
            'address': '123 Main St',
            'city': 'New York',
            'zip_code': '10001'
        }
        
        form = CustomerProfileForm(data=form_data, instance=self.profile)
        self.assertTrue(form.is_valid())
        
        profile = form.save()
        
        # Check profile was updated correctly
        self.assertEqual(profile.first_name, 'John')
        self.assertEqual(profile.last_name, 'Doe')
        self.assertEqual(profile.phone_number, '+1234567890')


class CustomerPasswordChangeFormTests(TestCase):
    """Test cases for CustomerPasswordChangeForm"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='oldpass123',
            role=UserRoles.CUSTOMER
        )

    def test_customer_password_change_form_fields(self):
        """Test that CustomerPasswordChangeForm has the expected fields"""
        form = CustomerPasswordChangeForm(user=self.user)
        expected_fields = ['old_password', 'new_password1', 'new_password2']
        
        for field_name in expected_fields:
            self.assertIn(field_name, form.fields)

    @patch('django.contrib.auth.password_validation.validate_password')
    def test_customer_password_change_form_valid_data(self, mock_validate_password):
        """Test customer password change form with valid data"""
        # Mock password validation to avoid complex validation issues
        mock_validate_password.return_value = None
        
        form_data = {
            'old_password': 'oldpass123',
            'new_password1': 'newpass123!',
            'new_password2': 'newpass123!'
        }
        
        form = CustomerPasswordChangeForm(data=form_data, user=self.user)
        self.assertTrue(form.is_valid())

    def test_customer_password_change_form_wrong_old_password(self):
        """Test customer password change form with wrong old password"""
        form_data = {
            'old_password': 'wrongpass123',
            'new_password1': 'newpass123!',
            'new_password2': 'newpass123!'
        }
        
        form = CustomerPasswordChangeForm(data=form_data, user=self.user)
        self.assertFalse(form.is_valid())
        self.assertIn('old_password', form.errors)

    def test_customer_password_change_form_password_mismatch(self):
        """Test customer password change form with password mismatch"""
        form_data = {
            'old_password': 'oldpass123',
            'new_password1': 'newpass123!',
            'new_password2': 'differentpass123!'
        }
        
        form = CustomerPasswordChangeForm(data=form_data, user=self.user)
        self.assertFalse(form.is_valid())
        self.assertIn('new_password2', form.errors)

    def test_customer_password_change_form_missing_fields(self):
        """Test customer password change form with missing fields"""
        form_data = {
            'old_password': 'oldpass123',
            'new_password1': 'newpass123!',
            # Missing new_password2
        }
        
        form = CustomerPasswordChangeForm(data=form_data, user=self.user)
        self.assertFalse(form.is_valid())
        self.assertIn('new_password2', form.errors)


# Pytest-style tests
@pytest.mark.django_db
class TestCustomerFormsPytest(TestCase):
    """Pytest-style tests for customer forms"""

    def test_form_field_attributes(self):
        """Test form field attributes"""
        form = CustomerSignupForm()
        
        # Test email field attributes
        email_field = form.fields['email']
        assert email_field.widget.input_type == 'email'
        assert 'placeholder' in email_field.widget.attrs
        
        # Test password field attributes
        password_field = form.fields['password1']
        assert password_field.widget.input_type == 'password'
        assert 'minlength' in password_field.widget.attrs

    def test_form_help_text(self):
        """Test form help text"""
        form = CustomerSignupForm()
        
        # Test email help text
        email_field = form.fields['email']
        assert email_field.help_text is not None
        
        # Test password help text
        password_field = form.fields['password1']
        assert password_field.help_text is not None

    def test_form_error_messages(self):
        """Test form error messages are properly configured"""
        form = CustomerSignupForm()
        self.assertIn('required', form.fields['email'].error_messages) 