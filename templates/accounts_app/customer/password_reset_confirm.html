{% extends 'base.html' %}

{% block title %}Set New Password - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

{% endblock %}

{% block content %}
<section class="password-reset-confirm-section">
    <div class="password-reset-confirm-container">
        <div class="password-reset-confirm-card">
            {% if validlink %}
                <div class="password-reset-confirm-header">
                    <div class="content">
                        <div class="password-reset-confirm-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <h1 class="password-reset-confirm-title">Set New Password</h1>
                        <p class="password-reset-confirm-subtitle">Please enter your new password twice to confirm</p>
                    </div>
                </div>

                <div class="password-reset-confirm-body">
                    <form method="post" novalidate>
                      {% csrf_token %}

                      {% if form.non_field_errors %}
                      <div class="alert-cw alert-cw-error mb-4">
                        {% for error in form.non_field_errors %}
                        <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                        {% endfor %}
                      </div>
                      {% endif %}

                      <!-- New Password field -->
                      <div class="mb-4">
                        <label for="{{ form.new_password1.id_for_label }}" class="form-label">
                          <i class="fas fa-lock"></i>New Password
                        </label>
                        <div class="input-group">
                          {% if form.new_password1.errors %}
                            {{ form.new_password1|add_class:"form-control-cw is-invalid"|attr:"placeholder:Enter your new password" }}
                          {% else %}
                            {{ form.new_password1|add_class:"form-control-cw"|attr:"placeholder:Enter your new password" }}
                          {% endif %}
                          <button type="button" class="toggle-password" data-target="#{{ form.new_password1.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                            <i class="fas fa-eye" aria-hidden="true"></i>
                          </button>
                        </div>
                        {% if form.new_password1.errors %}
                        <div class="invalid-feedback" role="alert" aria-live="polite">
                          {% for error in form.new_password1.errors %}
                          <i class="fas fa-exclamation-circle"></i>{{ error }}
                          {% endfor %}
                        </div>
                        {% endif %}
                        {% if form.new_password1.help_text %}
                        <div class="form-text">{{ form.new_password1.help_text }}</div>
                        {% endif %}
                      </div>

                      <!-- Confirm Password field -->
                      <div class="mb-4">
                        <label for="{{ form.new_password2.id_for_label }}" class="form-label">
                          <i class="fas fa-lock"></i>Confirm Password
                        </label>
                        <div class="input-group">
                          {% if form.new_password2.errors %}
                            {{ form.new_password2|add_class:"form-control-cw is-invalid"|attr:"placeholder:Confirm your new password" }}
                          {% else %}
                            {{ form.new_password2|add_class:"form-control-cw"|attr:"placeholder:Confirm your new password" }}
                          {% endif %}
                          <button type="button" class="toggle-password" data-target="#{{ form.new_password2.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                            <i class="fas fa-eye" aria-hidden="true"></i>
                          </button>
                        </div>
                        {% if form.new_password2.errors %}
                        <div class="invalid-feedback" role="alert" aria-live="polite">
                          {% for error in form.new_password2.errors %}
                          <i class="fas fa-exclamation-circle"></i>{{ error }}
                          {% endfor %}
                        </div>
                        {% endif %}
                        {% if form.new_password2.help_text %}
                        <div class="form-text">{{ form.new_password2.help_text }}</div>
                        {% endif %}
                      </div>

                      <div class="d-grid mb-4">
                        <button type="submit" class="btn-cw-primary">
                          <i class="fas fa-check-circle"></i>Set New Password
                        </button>
                      </div>
                    </form>
                </div>

            {% else %}
                <div class="invalid-link-header">
                    <div class="content">
                        <div class="invalid-link-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h1 class="invalid-link-title">Invalid Link</h1>
                        <p class="invalid-link-subtitle">The password reset link was invalid, possibly because it has already been used. Please request a new password reset.</p>
                    </div>
                </div>

                <div class="password-reset-confirm-body">
                    <div class="d-grid mb-4">
                      <a href="{% url 'accounts_app:customer_password_reset' %}" class="btn-cw-primary">
                        <i class="fas fa-redo"></i>Request New Reset Link
                      </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</section>

<!-- Password Toggle JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    // Password toggle functionality
    function togglePassword(toggleBtn) {
        const targetSelector = toggleBtn.getAttribute('data-target');
        if (!targetSelector) {
            console.error('No data-target attribute found on toggle button');
            return;
        }

        const input = document.querySelector(targetSelector);
        if (!input) {
            console.error('Target input not found:', targetSelector);
            return;
        }

        // Toggle password visibility
        const isPassword = input.type === 'password';
        input.type = isPassword ? 'text' : 'password';

        // Update icon
        const icon = toggleBtn.querySelector('i');
        if (icon) {
            if (isPassword) {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Update accessibility attributes
        const newTitle = isPassword ? 'Hide password' : 'Show password';
        toggleBtn.title = newTitle;
        toggleBtn.setAttribute('aria-label', newTitle);
    }

    // Click event handler
    document.addEventListener('click', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn) {
            e.preventDefault();
            e.stopPropagation();
            togglePassword(toggleBtn);
        }
    });

    // Keyboard event handler for accessibility
    document.addEventListener('keydown', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn && (e.key === 'Enter' || e.key === ' ' || e.key === 'Spacebar')) {
            e.preventDefault();
            e.stopPropagation();
            togglePassword(toggleBtn);
        }
    });

    // Initialize toggle buttons
    const toggleButtons = document.querySelectorAll('.toggle-password');
    toggleButtons.forEach(function(btn) {
        // Ensure button has proper attributes
        if (!btn.hasAttribute('tabindex')) {
            btn.setAttribute('tabindex', '0');
        }
        
        // Add role for screen readers
        btn.setAttribute('role', 'button');
        
        // Ensure aria-label is set
        if (!btn.hasAttribute('aria-label')) {
            btn.setAttribute('aria-label', 'Toggle password visibility');
        }
    });
});
</script>
{% endblock %}
