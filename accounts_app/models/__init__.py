# Import all models from the different modules for backwards compatibility

# Auth models
from .auth import (
    Custom<PERSON>ser,
    CustomUserManager,
    ActiveUserManager,
    CustomerManager,
    ProviderManager,
    AdminManager,
)

# Profile models
from .profiles import (
    UserProfile,
    CustomerProfile,
    ServiceProviderProfile,
)

# Security models
from .security import (
    UserSecurity,
    LoginHistory,
    LoginAlert,
    AccountLockout,
    PasswordHistory,
)

# Preference models
from .preferences import (
    UserPreferences,
    EmailPreferences,
    ProfilePrivacySettings,
)

# Verification models
from .verification import (
    EmailVerificationToken,
    ProfileVerification,
)

# Team models
from .team import (
    TeamMember,
)

# Make all models available at the package level
__all__ = [
    # Auth models
    'CustomUser',
    'CustomUserManager',
    'ActiveUserManager',
    'CustomerManager',
    'ProviderManager',
    'AdminManager',
    
    # Profile models
    'UserProfile',
    'CustomerProfile',
    'ServiceProviderProfile',
    
    # Security models
    'UserSecurity',
    'LoginHistory',
    'LoginAlert',
    'AccountLockout',
    'PasswordHistory',
    
    # Preference models
    'UserPreferences',
    'EmailPreferences',
    'ProfilePrivacySettings',
    
    # Verification models
    'EmailVerificationToken',
    'ProfileVerification',
    
    # Team models
    'TeamMember',
] 