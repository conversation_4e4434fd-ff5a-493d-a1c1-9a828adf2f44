#!/usr/bin/env python3
"""
Create Django superuser script for CozyWish project.
This script creates a superuser with predefined credentials.
"""

import os
import sys
import django

def create_superuser():
    """Create a Django superuser with predefined credentials."""
    # Add project root to Python path
    import sys
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    sys.path.insert(0, project_root)
    
    # Set up Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project_root.settings')
    django.setup()
    
    from django.contrib.auth import get_user_model
    
    User = get_user_model()
    email = '<EMAIL>'
    password = '123'
    
    try:
        user, created = User.objects.get_or_create(email=email, defaults={'password': password})
        
        if created:
            user.set_password(password)
            user.is_superuser = True
            user.is_staff = True
            user.save()
            print('✅ Superuser created successfully!')
            print(f'   Email: {email}')
            print(f'   Password: {password}')
        else:
            print('ℹ️  Superuser already exists.')
            
    except Exception as e:
        print(f'❌ Failed to create superuser: {e}')
        sys.exit(1)

if __name__ == '__main__':
    create_superuser() 