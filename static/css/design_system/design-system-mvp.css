/**
 * CozyWish Design System - MVP Version
 * Simplified CSS for rapid development
 * Version: 1.0.0-mvp
 */

/* ===== CSS CUSTOM PROPERTIES (DESIGN TOKENS) ===== */
:root {
  /* Brand Colors */
  --cw-brand-primary: #2F160F;
  --cw-brand-light: #4a2a1f;
  --cw-brand-accent: #fae1d7;
  --cw-accent-light: #fef7f0;
  
  /* Neutral Colors */
  --cw-neutral-100: #f5f5f5;
  --cw-neutral-500: #737373;
  --cw-neutral-700: #404040;
  --cw-neutral-900: #171717;
  
  /* Background Colors */
  --cw-bg-primary: #F8F9FA;
  --cw-bg-secondary: #ffffff;
  
  /* Status Colors */
  --cw-success: #22c55e;
  --cw-warning: #f59e0b;
  --cw-error: #ef4444;
  --cw-info: #3b82f6;
  
  /* Typography */
  --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif;
  --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  /* Spacing */
  --cw-spacing-xs: 0.25rem;
  --cw-spacing-sm: 0.5rem;
  --cw-spacing-md: 1rem;
  --cw-spacing-lg: 1.5rem;
  --cw-spacing-xl: 2rem;
  
  /* Shadows */
  --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  
  /* Border Radius */
  --cw-border-radius: 0.375rem;
  --cw-border-radius-lg: 0.75rem;
  
  /* Transitions */
  --cw-transition: all 0.2s ease;
}

/* ===== BASE STYLES ===== */
body {
  font-family: var(--cw-font-primary);
  font-weight: 400;
  line-height: 1.6;
  color: var(--cw-neutral-900);
  background-color: var(--cw-bg-primary);
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--cw-font-heading);
  font-weight: 600;
  color: var(--cw-brand-primary);
}

/* ===== UTILITY CLASSES ===== */
.text-brand-cw { color: var(--cw-brand-primary); }
.text-neutral-cw { color: var(--cw-neutral-500); }
.text-accent-cw { color: var(--cw-brand-accent); }
.bg-brand-cw { background-color: var(--cw-brand-primary); }
.bg-accent-cw { background-color: var(--cw-brand-accent); }

/* ===== BUTTONS ===== */
.btn-cw-primary {
  background-color: var(--cw-brand-primary);
  color: white;
  border: none;
  border-radius: var(--cw-border-radius);
  padding: 0.75rem 1.5rem;
  font-family: var(--cw-font-primary);
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1.25;
  transition: var(--cw-transition);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  border: 2px solid transparent;
}

.btn-cw-primary:hover {
  background-color: var(--cw-brand-light);
  transform: translateY(-1px);
  box-shadow: var(--cw-shadow-md);
  color: white;
}

.btn-cw-primary:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.3);
}

.btn-cw-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-cw-secondary {
  background-color: transparent;
  color: var(--cw-brand-primary);
  border: 2px solid var(--cw-brand-primary);
  border-radius: var(--cw-border-radius);
  padding: 0.75rem 1.5rem;
  font-family: var(--cw-font-primary);
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1.25;
  transition: var(--cw-transition);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.btn-cw-secondary:hover {
  background-color: var(--cw-brand-primary);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--cw-shadow-md);
}

.btn-cw-secondary:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.3);
}

/* Button Sizes */
.btn-cw-sm {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

.btn-cw-lg {
  padding: 1rem 2rem;
  font-size: 1rem;
}

/* ===== CARDS ===== */
.card-cw {
  background-color: var(--cw-bg-secondary);
  border: 1px solid var(--cw-neutral-100);
  border-radius: var(--cw-border-radius-lg);
  box-shadow: var(--cw-shadow-sm);
  transition: var(--cw-transition);
}

.card-cw:hover {
  box-shadow: var(--cw-shadow-md);
  transform: translateY(-2px);
}

.card-cw-brand {
  background: linear-gradient(135deg, #ffffff 0%, #fef7f0 100%);
  border: 1px solid var(--cw-brand-accent);
}

.card-cw-accent {
  background-color: var(--cw-accent-light);
  border: 1px solid var(--cw-brand-accent);
}

/* ===== FORMS ===== */
.form-control-cw {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--cw-neutral-100);
  border-radius: var(--cw-border-radius);
  font-family: var(--cw-font-primary);
  font-size: 0.875rem;
  transition: var(--cw-transition);
  background-color: var(--cw-bg-secondary);
}

.form-control-cw:focus {
  outline: none;
  border-color: var(--cw-brand-primary);
  box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
}

.form-label-cw {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--cw-neutral-700);
}

.form-group-cw {
  margin-bottom: 1rem;
}

/* ===== ALERTS ===== */
.alert-cw {
  padding: 1rem;
  border-radius: var(--cw-border-radius);
  border: 1px solid transparent;
  margin-bottom: 1rem;
}

.alert-cw-success {
  background-color: #f0f9ff;
  border-color: var(--cw-success);
  color: #047857;
}

.alert-cw-warning {
  background-color: #fffbeb;
  border-color: var(--cw-warning);
  color: #92400e;
}

.alert-cw-error {
  background-color: #fef2f2;
  border-color: var(--cw-error);
  color: #dc2626;
}

.alert-cw-info {
  background-color: #eff6ff;
  border-color: var(--cw-info);
  color: #1e40af;
}

/* ===== BADGES ===== */
.badge-cw {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--cw-border-radius);
}

.badge-cw-primary {
  background-color: var(--cw-brand-primary);
  color: white;
}

.badge-cw-success {
  background-color: var(--cw-success);
  color: white;
}

.badge-cw-warning {
  background-color: var(--cw-warning);
  color: white;
}

.badge-cw-error {
  background-color: var(--cw-error);
  color: white;
}

.badge-cw-info {
  background-color: var(--cw-info);
  color: white;
}

/* ===== NAVBAR ===== */
.navbar-cw {
  background-color: var(--cw-bg-secondary);
  border-bottom: 1px solid var(--cw-neutral-100);
  padding: 1rem 0;
}

.navbar-brand-cw {
  font-family: var(--cw-font-heading);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--cw-brand-primary);
  text-decoration: none;
}

.navbar-brand-cw:hover {
  color: var(--cw-brand-light);
}

.nav-link-cw {
  color: var(--cw-neutral-700);
  text-decoration: none;
  padding: 0.5rem 1rem;
  transition: var(--cw-transition);
}

.nav-link-cw:hover {
  color: var(--cw-brand-primary);
}

/* Navbar Buttons */
.btn-cw-nav-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: transparent;
  border: none;
  color: var(--cw-neutral-700);
  font-size: 1.1rem;
  transition: var(--cw-transition);
  text-decoration: none;
  position: relative;
}

.btn-cw-nav-icon:hover {
  background-color: var(--cw-accent-light);
  color: var(--cw-brand-primary);
}

.btn-cw-nav-primary {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: var(--cw-brand-primary);
  color: white;
  border: none;
  border-radius: var(--cw-border-radius);
  font-weight: 600;
  transition: var(--cw-transition);
  text-decoration: none;
}

.btn-cw-nav-primary:hover {
  background-color: var(--cw-brand-light);
  color: white;
}

/* User Avatar */
.user-avatar-cw {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--cw-brand-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

/* Notification Badge */
.badge-cw-notification {
  background-color: var(--cw-error);
  color: white;
  border-radius: 50%;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Menu Badge */
.menu-badge-cw {
  background-color: var(--cw-brand-primary);
  color: white;
  border-radius: 12px;
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: auto;
}

/* Navbar Toggler */
.navbar-toggler-cw {
  border: none;
  padding: 0.25rem;
  background-color: transparent;
  border-radius: var(--cw-border-radius);
}

.navbar-toggler-cw:focus {
  box-shadow: none;
}

.navbar-toggler-icon-cw {
  width: 24px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
}

.nav-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* ===== DROPDOWNS ===== */
.dropdown-menu-cw,
.dropdown-menu-cw.dropdown-menu {
  background-color: var(--cw-bg-secondary);
  border: 1px solid var(--cw-neutral-100);
  border-radius: var(--cw-border-radius-lg);
  box-shadow: var(--cw-shadow-md);
  padding: 0.5rem 0;
  min-width: 200px;
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  margin-top: 0.25rem;
}

.dropdown-menu-cw.show,
.dropdown-menu-cw.dropdown-menu.show {
  display: block;
}

.dropdown-item-cw {
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  color: var(--cw-neutral-700);
  text-decoration: none;
  transition: var(--cw-transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
  background: none;
  cursor: pointer;
}

.dropdown-item-cw:hover {
  background-color: var(--cw-accent-light);
  color: var(--cw-brand-primary);
}

.dropdown-item-cw.danger {
  color: var(--cw-error);
}

.dropdown-item-cw.danger:hover {
  background-color: #fef2f2;
  color: var(--cw-error);
}

.dropdown-header-cw {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  color: var(--cw-brand-primary);
  font-weight: 600;
  font-size: 0.875rem;
  border-bottom: 1px solid var(--cw-neutral-100);
  margin-bottom: 0.5rem;
}

.dropdown-divider-cw {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid var(--cw-neutral-100);
}

/* Custom Dropdown Toggle */
.dropdown-toggle-cw {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: var(--cw-bg-secondary);
  border: 2px solid var(--cw-neutral-100);
  border-radius: var(--cw-border-radius);
  color: var(--cw-neutral-700);
  font-weight: 600;
  transition: var(--cw-transition);
  cursor: pointer;
  position: relative;
}

.dropdown-toggle-cw:hover {
  border-color: var(--cw-brand-primary);
  color: var(--cw-brand-primary);
}

.dropdown-toggle-cw.active {
  border-color: var(--cw-brand-primary);
  color: var(--cw-brand-primary);
}

.dropdown-container {
  position: relative;
  display: inline-block;
}

/* Notification Badge for Dropdown */
.notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--cw-error);
  color: white;
  border-radius: 50%;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ===== MODALS ===== */
.modal-cw .modal-content {
  border: none;
  border-radius: var(--cw-border-radius-lg);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-cw .modal-header {
  background-color: var(--cw-accent-light);
  border-bottom: 1px solid var(--cw-neutral-100);
  border-radius: var(--cw-border-radius-lg) var(--cw-border-radius-lg) 0 0;
}

.modal-cw .modal-title {
  color: var(--cw-brand-primary);
  font-family: var(--cw-font-heading);
  font-weight: 600;
}

/* ===== TABLES ===== */
.table-cw {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--cw-bg-secondary);
  border-radius: var(--cw-border-radius-lg);
  overflow: hidden;
  box-shadow: var(--cw-shadow-sm);
}

.table-cw th {
  background-color: var(--cw-accent-light);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--cw-brand-primary);
  border-bottom: 2px solid var(--cw-neutral-100);
}

.table-cw td {
  padding: 1rem;
  border-bottom: 1px solid var(--cw-neutral-100);
  color: var(--cw-neutral-700);
}

.table-cw tr:hover {
  background-color: var(--cw-accent-light);
}

/* ===== LOADING STATES ===== */
.spinner-cw {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 0.25rem solid var(--cw-neutral-100);
  border-top: 0.25rem solid var(--cw-brand-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-overlay-cw {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .btn-cw-primary,
  .btn-cw-secondary {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }
  
  .card-cw {
    margin-bottom: 1rem;
  }
  
  .table-cw th,
  .table-cw td {
    padding: 0.75rem 0.5rem;
    font-size: 0.875rem;
  }
}

/* ===== INDEX PAGE STYLES ===== */
.ds-hero-section {
  background: linear-gradient(135deg, var(--cw-accent-light) 0%, var(--cw-bg-secondary) 100%);
  padding: 4rem 0;
  text-align: center;
  margin-bottom: 3rem;
}

.ds-component-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.ds-component-card {
  background-color: var(--cw-bg-secondary);
  border: 1px solid var(--cw-neutral-100);
  border-radius: var(--cw-border-radius-lg);
  padding: 2rem;
  text-align: center;
  transition: var(--cw-transition);
  box-shadow: var(--cw-shadow-sm);
}

.ds-component-card:hover {
  box-shadow: var(--cw-shadow-md);
  transform: translateY(-4px);
}

.ds-component-card h3 {
  color: var(--cw-brand-primary);
  margin-bottom: 1rem;
}

.ds-component-card p {
  color: var(--cw-neutral-500);
  margin-bottom: 1.5rem;
}

.ds-component-card a {
  text-decoration: none;
}

.ds-back-to-index {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--cw-brand-primary);
  text-decoration: none;
  font-weight: 600;
  margin-top: 2rem;
  transition: var(--cw-transition);
}

.ds-back-to-index:hover {
  color: var(--cw-brand-light);
  transform: translateX(-4px);
}

/* ===== COMPONENT DEMO STYLES ===== */
.ds-component-section {
  margin-bottom: 3rem;
}

.ds-component-title {
  color: var(--cw-brand-primary);
  margin-bottom: 2rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--cw-brand-accent);
}

.ds-component-demo {
  background-color: var(--cw-bg-secondary);
  border: 1px solid var(--cw-neutral-100);
  border-radius: var(--cw-border-radius-lg);
  padding: 2rem;
  margin-bottom: 2rem;
}

.ds-component-demo h4 {
  color: var(--cw-brand-primary);
  margin-bottom: 1rem;
}

.ds-code-example {
  background-color: var(--cw-neutral-100);
  border-radius: var(--cw-border-radius);
  padding: 1rem;
  margin-top: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  color: var(--cw-neutral-700);
  overflow-x: auto;
  white-space: pre-wrap;
}

/* ===== DEMO STYLES ===== */
.demo-container {
  margin-bottom: 3rem;
}

.demo-header {
  margin-bottom: 2rem;
}

.demo-title {
  color: var(--cw-brand-primary);
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.demo-description {
  color: var(--cw-neutral-500);
  margin-bottom: 0;
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.demo-item {
  background-color: var(--cw-bg-secondary);
  border: 1px solid var(--cw-neutral-100);
  border-radius: var(--cw-border-radius-lg);
  padding: 1.5rem;
}

.demo-item h5 {
  color: var(--cw-brand-primary);
  margin-bottom: 1rem;
}

.navbar-demo {
  background-color: var(--cw-bg-secondary);
  border: 1px solid var(--cw-neutral-100);
  border-radius: var(--cw-border-radius-lg);
  overflow: hidden;
  margin-bottom: 1rem;
}

/* ===== JAVASCRIPT DROPDOWN FUNCTIONALITY ===== */
.dropdown-menu-cw {
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  pointer-events: none;
}

.dropdown-menu-cw.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
  display: block !important;
}

/* Bootstrap dropdown compatibility */
.dropdown-menu-end {
  right: 0;
  left: auto;
}

.dropdown-menu-start {
  left: 0;
  right: auto;
} 