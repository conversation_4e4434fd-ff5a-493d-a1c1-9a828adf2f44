/* Review App - Base Review Styles */

/* CSS Variables for fonts */
:root {
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Review wrapper - clean white background */
.review-wrapper {
    background-color: white;
    min-height: 100vh;
    padding: 2rem 0;
    font-family: var(--font-primary);
}

/* Typography */
.review-wrapper h1, .review-wrapper h2, .review-wrapper h3,
.review-wrapper h4, .review-wrapper h5, .review-wrapper h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    color: black;
    margin-bottom: 1rem;
}

.review-wrapper p, .review-wrapper span, .review-wrapper div {
    color: black;
}

/* Cards - clean white with black border */
.review-wrapper .card {
    background: white;
    border: 2px solid black;
    border-radius: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.review-wrapper .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

/* Buttons - black and white theme */
.review-wrapper .btn {
    font-family: var(--font-primary);
    font-weight: 500;
    border-radius: 0.75rem;
    padding: 0.75rem 1.5rem;
    border: 2px solid black;
    transition: all 0.3s ease;
}

.review-wrapper .btn-primary {
    background-color: white;
    color: black;
    border-color: black;
}

.review-wrapper .btn-primary:hover {
    background-color: #f8f9fa;
    color: black;
    border-color: black;
}

.review-wrapper .btn-outline-primary {
    background-color: white;
    color: black;
    border-color: black;
}

.review-wrapper .btn-outline-primary:hover {
    background-color: #f8f9fa;
    color: black;
    border-color: black;
}

.review-wrapper .btn-success {
    background-color: white;
    color: black;
    border-color: black;
}

.review-wrapper .btn-success:hover {
    background-color: #f8f9fa;
    color: black;
    border-color: black;
}

.review-wrapper .btn-danger {
    background-color: white;
    color: black;
    border-color: black;
}

.review-wrapper .btn-danger:hover {
    background-color: #f8f9fa;
    color: black;
    border-color: black;
}

.review-wrapper .btn-warning {
    background-color: white;
    color: black;
    border-color: black;
}

.review-wrapper .btn-warning:hover {
    background-color: #f8f9fa;
    color: black;
    border-color: black;
}

/* Form elements */
.review-wrapper .form-control, .review-wrapper .form-select {
    font-family: var(--font-primary);
    border: 2px solid black;
    border-radius: 0.75rem;
    padding: 0.75rem 1rem;
    background-color: white;
    color: black;
}

.review-wrapper .form-control:focus, .review-wrapper .form-select:focus {
    border-color: black;
    box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
    background-color: white;
    color: black;
}

/* Labels */
.review-wrapper .form-label {
    font-family: var(--font-primary);
    font-weight: 500;
    color: black;
    margin-bottom: 0.5rem;
}

/* Star ratings */
.review-wrapper .star-rating {
    color: black;
    font-size: 1.25rem;
}

.review-wrapper .star-rating .star {
    color: rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: color 0.2s ease;
}

.review-wrapper .star-rating .star.filled {
    color: black;
}

.review-wrapper .star-rating .star:hover {
    color: black;
}

/* Review cards */
.review-item {
    border: 2px solid black;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    background-color: white;
    transition: all 0.3s ease;
}

.review-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.review-author {
    font-family: var(--font-heading);
    font-weight: 600;
    color: black;
    margin-bottom: 0.5rem;
}

.review-date {
    font-size: 0.875rem;
    color: rgba(0, 0, 0, 0.6);
    font-family: var(--font-primary);
}

/* Review content */
.review-content {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: black;
    margin: 1rem 0;
}

/* Review response */
.review-response {
    background-color: #f8f9fa;
    border: 2px solid black;
    border-radius: 0.75rem;
    padding: 1rem;
    margin-top: 1rem;
    font-family: var(--font-primary);
}

.review-response-header {
    font-family: var(--font-heading);
    font-weight: 600;
    color: black;
    margin-bottom: 0.5rem;
}

/* Pagination */
.review-wrapper .pagination {
    justify-content: center;
    margin-top: 2rem;
}

.review-wrapper .page-link {
    border: 2px solid black;
    color: black;
    background-color: white;
    margin: 0 0.25rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.review-wrapper .page-link:hover {
    background-color: #f8f9fa;
    color: black;
    border-color: black;
}

.review-wrapper .page-item.active .page-link {
    background-color: black;
    color: white;
    border-color: black;
}

/* Alerts */
.review-wrapper .alert {
    border: 2px solid black;
    border-radius: 0.75rem;
    background-color: white;
    color: black;
    font-family: var(--font-primary);
}

.review-wrapper .alert-success {
    border-color: #28a745;
    background-color: #d4edda;
}

.review-wrapper .alert-danger {
    border-color: #dc3545;
    background-color: #f8d7da;
}

.review-wrapper .alert-warning {
    border-color: #ffc107;
    background-color: #fff3cd;
}

.review-wrapper .alert-info {
    border-color: #17a2b8;
    background-color: #d1ecf1;
}

/* Badges */
.review-wrapper .badge {
    font-family: var(--font-primary);
    border: 2px solid black;
    border-radius: 0.5rem;
    padding: 0.5rem 0.75rem;
    background-color: white;
    color: black;
}

/* Progress bars */
.review-wrapper .progress {
    background-color: #e9ecef;
    border: 2px solid black;
    border-radius: 0.5rem;
    height: 1rem;
}

.review-wrapper .progress-bar {
    background-color: black;
    border-radius: 0.25rem;
}

/* Tables */
.review-wrapper .table {
    border: 2px solid black;
    border-radius: 0.75rem;
    overflow: hidden;
}

.review-wrapper .table th,
.review-wrapper .table td {
    border: 1px solid black;
    padding: 1rem;
    background-color: white;
    color: black;
    font-family: var(--font-primary);
}

.review-wrapper .table th {
    background-color: #f8f9fa;
    font-family: var(--font-heading);
    font-weight: 600;
}

/* Modal */
.review-wrapper .modal-content {
    border: 2px solid black;
    border-radius: 1rem;
    background-color: white;
}

.review-wrapper .modal-header {
    border-bottom: 2px solid black;
    background-color: #f8f9fa;
}

.review-wrapper .modal-footer {
    border-top: 2px solid black;
    background-color: #f8f9fa;
}

/* Review Card */
.review-card {
    max-width: 800px;
}

/* Responsive design */
@media (max-width: 768px) {
    .review-wrapper {
        padding: 1rem 0;
    }
    
    .review-wrapper .card {
        margin-bottom: 1rem;
    }
    
    .review-wrapper .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
} 