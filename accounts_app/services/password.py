"""
Password service for accounts_app.

This module contains the PasswordService class for handling
password operations with security best practices.
"""

import logging
from typing import <PERSON>ple
from django.db import transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# Local imports
from ..models import CustomUser, PasswordHistory, UserSecurity
from ..utils.logging import log_user_activity


logger = logging.getLogger(__name__)


class PasswordService:
    """
    Service for handling password operations.
    
    Provides methods for password changes, resets, and validation
    with security best practices and history tracking.
    """
    
    @staticmethod
    def change_password(user: CustomUser, old_password: str, new_password: str, request=None) -> Tuple[bool, str]:
        """
        Change user password with validation.
        
        Args:
            user: User object
            old_password: Current password
            new_password: New password
            request: HTTP request object for logging
            
        Returns:
            Tuple of (success, message)
        """
        try:
            with transaction.atomic():
                # Verify old password
                if not user.check_password(old_password):
                    logger.warning(f"Password change failed - incorrect old password: {user.email}")
                    return False, _("Current password is incorrect")
                
                # Check password history
                if PasswordService._is_password_reused(user, new_password):
                    logger.warning(f"Password change failed - password reused: {user.email}")
                    return False, _("You cannot reuse a recent password")
                
                # Store old password in history
                PasswordHistory.add_password(user, user.password)
                
                # Update password
                user.set_password(new_password)
                user.save()
                
                # Update security record
                security, created = UserSecurity.objects.get_or_create(user=user)
                security.last_password_change = timezone.now()
                security.save()
                
                # Log password change
                log_user_activity(
                    activity_type='password_change',
                    user=user,
                    request=request,
                    details={'change_method': 'user_initiated'}
                )
                
                logger.info(f"Password changed successfully: {user.email}")
                return True, _("Password changed successfully")
                
        except Exception as e:
            logger.error(f"Password change error: {str(e)}", exc_info=True)
            return False, _("Password change failed. Please try again.")
    
    @staticmethod
    def reset_password(user: CustomUser, new_password: str, request=None) -> Tuple[bool, str]:
        """
        Reset user password (for password reset flow).
        
        Args:
            user: User object
            new_password: New password
            request: HTTP request object for logging
            
        Returns:
            Tuple of (success, message)
        """
        try:
            with transaction.atomic():
                # Store old password in history
                PasswordHistory.add_password(user, user.password)
                
                # Update password
                user.set_password(new_password)
                user.save()
                
                # Update security record
                security, created = UserSecurity.objects.get_or_create(user=user)
                security.last_password_change = timezone.now()
                security.save()
                
                # Log password reset
                log_user_activity(
                    activity_type='password_reset',
                    user=user,
                    request=request,
                    details={'reset_method': 'email_verification'}
                )
                
                logger.info(f"Password reset successfully: {user.email}")
                return True, _("Password reset successfully")
                
        except Exception as e:
            logger.error(f"Password reset error: {str(e)}", exc_info=True)
            return False, _("Password reset failed. Please try again.")
    
    @staticmethod
    def _is_password_reused(user: CustomUser, new_password: str) -> bool:
        """Check if password was recently used"""
        try:
            from django.contrib.auth.hashers import check_password
            
            # Check against recent password history
            recent_passwords = PasswordHistory.objects.filter(
                user=user
            ).order_by('-created_at')[:5]  # Check last 5 passwords
            
            for password_record in recent_passwords:
                if check_password(new_password, password_record.password_hash):
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Password reuse check error: {str(e)}", exc_info=True)
            return False 