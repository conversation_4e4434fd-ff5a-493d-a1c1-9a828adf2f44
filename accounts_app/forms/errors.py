"""
Form Error Messages Constants

This module contains standardized error messages for form validation
across the accounts_app. All error messages are translatable and
user-friendly.
"""

from django.utils.translation import gettext_lazy as _


# --- Email Validation Errors ---
EMAIL_ERRORS = {
    'invalid_format': _('Please enter a valid email address.'),
    'email_exists': _('An account with this email address already exists.'),
    'email_not_found': _('No account found with this email address.'),
    'email_required': _('Email address is required.'),
    'email_too_long': _('Email address is too long. Maximum length is 254 characters.'),
    'email_mismatch': _('The email address you entered does not match your account email.'),
}

# --- Password Validation Errors ---
PASSWORD_ERRORS = {
    'too_short': _('Password must be at least 8 characters long.'),
    'too_common': _('This password is too common. Please choose a more unique password.'),
    'too_similar': _('Password is too similar to your personal information.'),
    'entirely_numeric': _('Password cannot be entirely numeric.'),
    'password_mismatch': _('The password confirmation does not match.'),
    'weak_password': _('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character.'),
    'current_password_incorrect': _('Your current password is incorrect.'),
    'password_required': _('Password is required.'),
    'password_too_long': _('Password is too long. Maximum length is 128 characters.'),
    'new_password_same_as_old': _('Your new password must be different from your current password.'),
}

# --- Phone Number Validation Errors ---
PHONE_ERRORS = {
    'invalid_format': _('Please enter a valid phone number.'),
    'too_short': _('Phone number is too short. Please enter at least 10 digits.'),
    'too_long': _('Phone number is too long. Maximum length is 15 digits.'),
    'invalid_characters': _('Phone number can only contain digits, spaces, dashes, parentheses, and plus sign.'),
    'invalid_us_format': _('Please enter a valid US phone number (10 digits) or international number with country code.'),
    'invalid_country_code': _('Please enter a valid country code.'),
    'phone_required': _('Phone number is required.'),
}

# --- Image Validation Errors ---
IMAGE_ERRORS = {
    'file_too_large': _('Image file is too large. Maximum file size is {max_size}MB.'),
    'file_too_small': _('Image file is too small. Minimum file size is {min_size}KB.'),
    'invalid_format': _('Invalid image format. Please upload a PNG, JPG, WebP, or GIF file.'),
    'invalid_extension': _('Invalid file extension. Please use .jpg, .jpeg, .png, .webp, or .gif files.'),
    'image_too_small': _('Image is too small. Minimum size is {min_width}x{min_height} pixels.'),
    'image_too_large': _('Image is too large. Maximum size is {max_width}x{max_height} pixels.'),
    'invalid_image': _('Invalid image file. Please upload a valid image.'),
    'corrupted_image': _('The image file appears to be corrupted. Please try uploading a different image.'),
    'image_required': _('Image is required.'),
    'processing_error': _('Unable to process the image. Please try again or use a different image.'),
}

# --- URL Validation Errors ---
URL_ERRORS = {
    'invalid_format': _('Please enter a valid URL.'),
    'url_too_long': _('URL is too long. Maximum length is 2000 characters.'),
    'unreachable': _('The URL appears to be unreachable. Please check and try again.'),
    'invalid_domain': _('Please enter a valid domain name.'),
    'protocol_required': _('URL must include http:// or https://'),
    'url_required': _('URL is required.'),
}

# --- Business Validation Errors ---
BUSINESS_ERRORS = {
    'ein_invalid': _('Please enter a valid EIN (XX-XXXXXXX format).'),
    'ein_exists': _('An account with this EIN already exists.'),
    'zip_invalid': _('Please enter a valid ZIP code.'),
    'state_required': _('State is required.'),
    'business_name_required': _('Business name is required.'),
    'business_name_too_long': _('Business name is too long. Maximum length is 255 characters.'),
    'license_number_invalid': _('Please enter a valid license number.'),
    'tax_id_invalid': _('Please enter a valid tax ID.'),
}

# --- Date/Time Validation Errors ---
DATE_ERRORS = {
    'invalid_date': _('Please enter a valid date.'),
    'future_date': _('Date cannot be in the future.'),
    'past_date': _('Date cannot be in the past.'),
    'invalid_age': _('You must be at least {min_age} years old.'),
    'invalid_birth_date': _('Please enter a valid birth date.'),
    'invalid_month': _('Please select a valid month.'),
    'invalid_year': _('Please select a valid year.'),
    'date_range_invalid': _('End date must be after start date.'),
}

# --- Terms and Conditions Errors ---
TERMS_ERRORS = {
    'terms_required': _('You must agree to the Terms of Service and Privacy Policy to continue.'),
    'privacy_required': _('You must agree to the Privacy Policy to continue.'),
    'marketing_consent': _('Please indicate your marketing preferences.'),
    'age_verification': _('You must confirm that you are at least 18 years old.'),
}

# --- Authentication Errors ---
AUTH_ERRORS = {
    'invalid_credentials': _('Invalid email or password. Please try again.'),
    'account_disabled': _('This account has been disabled. Please contact support.'),
    'account_locked': _('This account has been temporarily locked due to multiple failed login attempts.'),
    'account_not_verified': _('Please verify your email address before logging in.'),
    'login_required': _('Please log in to access this page.'),
    'permission_denied': _('You do not have permission to access this resource.'),
    'session_expired': _('Your session has expired. Please log in again.'),
    'too_many_attempts': _('Too many login attempts. Please try again later.'),
}

# --- Form Validation Errors ---
FORM_ERRORS = {
    'required_field': _('This field is required.'),
    'invalid_choice': _('Please select a valid option.'),
    'field_too_long': _('This field is too long. Maximum length is {max_length} characters.'),
    'field_too_short': _('This field is too short. Minimum length is {min_length} characters.'),
    'invalid_format': _('Please enter a valid format.'),
    'duplicate_value': _('This value already exists.'),
    'invalid_number': _('Please enter a valid number.'),
    'number_too_large': _('Number is too large. Maximum value is {max_value}.'),
    'number_too_small': _('Number is too small. Minimum value is {min_value}.'),
}

# --- File Upload Errors ---
FILE_ERRORS = {
    'file_too_large': _('File is too large. Maximum file size is {max_size}MB.'),
    'file_too_small': _('File is too small. Minimum file size is {min_size}KB.'),
    'invalid_file_type': _('Invalid file type. Please upload a {allowed_types} file.'),
    'file_required': _('File is required.'),
    'upload_failed': _('File upload failed. Please try again.'),
    'corrupted_file': _('The file appears to be corrupted. Please try uploading a different file.'),
}

# --- ALL ERRORS MAPPING ---
ALL_ERRORS = {
    'email': EMAIL_ERRORS,
    'password': PASSWORD_ERRORS,
    'phone': PHONE_ERRORS,
    'image': IMAGE_ERRORS,
    'url': URL_ERRORS,
    'business': BUSINESS_ERRORS,
    'date': DATE_ERRORS,
    'terms': TERMS_ERRORS,
    'auth': AUTH_ERRORS,
    'form': FORM_ERRORS,
    'file': FILE_ERRORS,
}

# --- SUCCESS MESSAGES ---
SUCCESS_MESSAGES = {
    'profile_updated': _('Your profile has been updated successfully.'),
    'password_changed': _('Your password has been changed successfully.'),
    'account_created': _('Your account has been created successfully.'),
    'email_verified': _('Your email address has been verified successfully.'),
    'settings_saved': _('Your settings have been saved successfully.'),
    'image_uploaded': _('Image uploaded successfully.'),
    'account_deactivated': _('Your account has been deactivated.'),
    'team_member_added': _('Team member has been added successfully.'),
    'team_member_updated': _('Team member has been updated successfully.'),
    'team_member_removed': _('Team member has been removed successfully.'),
    'notification_sent': _('Notification has been sent successfully.'),
    'data_exported': _('Data has been exported successfully.'),
    'backup_created': _('Backup has been created successfully.'),
    'import_completed': _('Import has been completed successfully.'),
    'sync_completed': _('Synchronization has been completed successfully.'),
}


# --- UTILITY FUNCTIONS ---

def get_error_message(category: str, error_key: str, **kwargs) -> str:
    """
    Get a formatted error message from the error dictionaries.
    
    Args:
        category (str): Error category (e.g., 'email', 'password')
        error_key (str): Specific error key within the category
        **kwargs: Format arguments for the error message
    
    Returns:
        str: Formatted error message
    """
    category_errors = ALL_ERRORS.get(category, {})
    message = category_errors.get(error_key, FORM_ERRORS['invalid_format'])
    
    if kwargs:
        try:
            return str(message).format(**kwargs)
        except (KeyError, ValueError):
            return str(message)
    
    return str(message)


def get_success_message(key: str, **kwargs) -> str:
    """
    Get a formatted success message.
    
    Args:
        key (str): Success message key
        **kwargs: Format arguments for the success message
    
    Returns:
        str: Formatted success message
    """
    message = SUCCESS_MESSAGES.get(key, SUCCESS_MESSAGES['profile_updated'])
    
    if kwargs:
        try:
            return str(message).format(**kwargs)
        except (KeyError, ValueError):
            return str(message)
    
    return str(message) 