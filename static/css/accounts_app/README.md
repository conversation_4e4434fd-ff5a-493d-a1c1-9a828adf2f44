# Accounts App Static Files Organization

This document describes the organized structure of static files for the accounts_app Django application.

## Directory Structure

```
static/
├── css/
│   ├── accounts_app/
│   │   ├── base_account.css       # Base account template styles
│   │   ├── cozy_design_system.css # CozyWish Design System base styles
│   │   └── README.md              # This documentation file
│   ├── profile_completion.css     # Profile completion widget styles
│   └── customer_profile.css       # Customer profile specific styles
├── js/
│   ├── accounts_app/
│   │   └── base_account.js        # Base account functionality
│   ├── profile_completion.js      # Profile completion widget scripts
│   └── profile_ajax.js           # Profile AJAX functionality
└── img/
    └── empty_state.svg           # Empty state illustrations
```

## CSS File Descriptions

### `cozy_design_system.css`
Contains the complete CozyWish Design System including:
- CSS custom properties/variables for colors, typography, shadows, and gradients
- Base typography and global styles
- Component styles for buttons, forms, alerts, badges, and cards
- Responsive design breakpoints
- Profile page specific styles

**Usage**: Include this file in any template that needs CozyWish Design System styling.

### `base_account.css`
Contains styles specific to the base account template:
- Account wrapper and card layouts
- Form styling with black & white theme
- Button styling matching homepage design
- Password toggle functionality styles
- Bootstrap override styles
- Responsive adjustments for mobile devices

**Usage**: Automatically included in `base_account.html` template.

### `profile_completion.css`
Contains styles for the profile completion progress widget:
- Progress bar animations
- Completion percentage display
- Priority action indicators
- Responsive design for mobile

**Usage**: Include in templates that display profile completion progress.

### `customer_profile.css`
Contains styles specific to customer profile pages:
- Profile information display
- Profile edit forms
- Customer-specific styling variations

**Usage**: Include in customer profile templates.

## JavaScript File Descriptions

### `base_account.js`
Contains core account functionality:
- Password toggle functionality with accessibility support
- Form validation enhancement
- Keyboard navigation support
- ARIA attributes management
- Error field handling

**Usage**: Automatically included in `base_account.html` template.

### `profile_completion.js`
Contains profile completion widget functionality:
- Progress calculation and display
- Priority action handling
- Animation controls
- User interaction handling

**Usage**: Include in templates with profile completion widgets.

### `profile_ajax.js`
Contains AJAX functionality for profile operations:
- Profile form submission handling
- Dynamic content loading
- Progress updates
- Error handling

**Usage**: Include in profile edit and management templates.

## Template Integration

### Base Account Template
The `base_account.html` template automatically includes:
- `cozy_design_system.css` for design system styles
- `base_account.css` for base account styles
- `base_account.js` for core account functionality

### Other Templates
For other templates, include relevant CSS/JS files using Django's static tag:

```html
{% load static %}

<!-- CSS -->
<link rel="stylesheet" href="{% static 'css/accounts_app/cozy_design_system.css' %}">

<!-- JavaScript -->
<script src="{% static 'js/accounts_app/base_account.js' %}"></script>
```

## Design System Usage

### CSS Variables
The design system provides CSS custom properties for consistent styling:

```css
/* Brand colors */
--cw-brand-primary: #2F160F;
--cw-brand-accent: #fae1d7;

/* Typography */
--cw-font-primary: 'Inter', sans-serif;
--cw-font-heading: 'Poppins', sans-serif;

/* Shadows */
--cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
```

### Component Classes
Use standardized component classes for consistent styling:

```html
<!-- Buttons -->
<button class="btn-cw-primary">Primary Button</button>
<button class="btn-cw-secondary">Secondary Button</button>

<!-- Forms -->
<input class="form-control-cw" type="text">
<label class="form-label-cw">Label</label>

<!-- Cards -->
<div class="card-cw">
  <div class="card-header-cw">
    <h3 class="card-title-cw">Card Title</h3>
  </div>
  <div class="card-body-cw">
    Content here
  </div>
</div>

<!-- Alerts -->
<div class="alert-cw alert-cw-success">Success message</div>
<div class="alert-cw alert-cw-error">Error message</div>
```

## Responsive Design

All styles include responsive breakpoints:
- Mobile: `@media (max-width: 576px)`
- Tablet: `@media (max-width: 768px)`
- Desktop: Default styles

## Performance Considerations

- CSS files are organized by functionality for optimal loading
- Common styles are centralized in `cozy_design_system.css`
- Template-specific styles are separated into individual files
- JavaScript is modularized for better maintainability

## Migration Notes

The organization extracts inline styles from templates into external files for:
- Better maintainability
- Consistent styling across templates
- Improved performance through caching
- Easier theme customization

### Before (Inline Styles)
```html
<style>
/* Inline CSS in template */
.account-wrapper { ... }
</style>
```

### After (External Files)
```html
<link rel="stylesheet" href="{% static 'css/accounts_app/base_account.css' %}">
```

This organization improves code maintainability, performance, and consistency across the accounts_app application. 