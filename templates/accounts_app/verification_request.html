{% extends 'accounts_app/base_account.html' %}
{% load crispy_forms_tags %}

{% block page_title %}Request Verification{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12 col-lg-8 mx-auto">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-certificate me-2"></i>Request Profile Verification
                    </h4>
                    <p class="mb-0 small">Get verified badges to build trust with other users</p>
                </div>
                
                <div class="card-body">
                    <!-- Current Verification Status -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-check-circle me-2"></i>Current Verification Status
                            </h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-left-primary">
                                        <div class="card-body">
                                            <h6 class="card-title">Completed Verifications</h6>
                                            <div id="verified-badges">
                                                {% for verification in verified_items %}
                                                    <span class="badge bg-success me-2 mb-2">
                                                        <i class="fas fa-check me-1"></i>{{ verification.get_verification_type_display }}
                                                    </span>
                                                {% empty %}
                                                    <p class="text-muted small">No verifications completed yet</p>
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-left-warning">
                                        <div class="card-body">
                                            <h6 class="card-title">Pending Verifications</h6>
                                            <div id="pending-badges">
                                                {% for verification in pending_items %}
                                                    <span class="badge bg-warning me-2 mb-2">
                                                        <i class="fas fa-clock me-1"></i>{{ verification.get_verification_type_display }}
                                                    </span>
                                                {% empty %}
                                                    <p class="text-muted small">No pending verifications</p>
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Verification Form -->
                    <form method="post" enctype="multipart/form-data" id="verification-form">
                        {% csrf_token %}
                        
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-plus-circle me-2"></i>Request New Verification
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        {{ form.verification_type|as_crispy_field }}
                                    </div>
                                    <div class="col-md-6">
                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-info-circle me-2"></i>Verification Types</h6>
                                            <ul class="mb-0 small">
                                                <li><strong>Email:</strong> Verify your email address</li>
                                                <li><strong>Phone:</strong> Verify your phone number</li>
                                                <li><strong>Identity:</strong> Verify your identity with ID</li>
                                                <li><strong>Business:</strong> Verify your business registration</li>
                                                <li><strong>Address:</strong> Verify your physical address</li>
                                                <li><strong>Payment:</strong> Verify payment method</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Supporting Document -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-file-upload me-2"></i>Supporting Documents
                                </h5>
                                {{ form.verification_document|as_crispy_field }}
                                
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Document Requirements</h6>
                                    <ul class="mb-0 small">
                                        <li>Upload clear, high-quality images or PDFs</li>
                                        <li>Ensure all information is clearly visible</li>
                                        <li>Maximum file size: 10MB</li>
                                        <li>Supported formats: JPG, PNG, PDF</li>
                                        <li>Do not upload sensitive information like full SSN</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Additional Notes -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-comment me-2"></i>Additional Information
                                </h5>
                                {{ form.additional_notes|as_crispy_field }}
                            </div>
                        </div>
                        
                        <!-- Terms and Conditions -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-file-contract me-2"></i>Terms & Conditions
                                </h5>
                                {{ form.accept_terms|as_crispy_field }}
                                
                                <div class="alert alert-secondary">
                                    <h6><i class="fas fa-gavel me-2"></i>Verification Terms</h6>
                                    <ul class="mb-0 small">
                                        <li>By submitting this verification request, you agree to our verification process</li>
                                        <li>We may contact you for additional information if needed</li>
                                        <li>Processing time is typically 1-3 business days</li>
                                        <li>False or fraudulent information may result in account suspension</li>
                                        <li>We reserve the right to reject verification requests</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <button type="submit" class="btn btn-success btn-lg">
                                            <i class="fas fa-paper-plane me-2"></i>Submit Verification Request
                                        </button>
                                        <a href="{% url 'accounts_app:verification_status' %}" class="btn btn-secondary ms-2">
                                            <i class="fas fa-eye me-2"></i>View Status
                                        </a>
                                    </div>
                                    <div>
                                        <a href="{% url 'accounts_app:privacy_settings' %}" class="btn btn-outline-primary">
                                            <i class="fas fa-arrow-left me-2"></i>Back to Privacy Settings
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Verification Benefits -->
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>Verification Benefits
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-shield-alt text-primary fa-3x mb-3"></i>
                                <h6>Build Trust</h6>
                                <p class="small">Verified badges show other users that your profile is authentic</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-search text-primary fa-3x mb-3"></i>
                                <h6>Better Visibility</h6>
                                <p class="small">Verified profiles are prioritized in search results</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-handshake text-primary fa-3x mb-3"></i>
                                <h6>More Bookings</h6>
                                <p class="small">Customers prefer to book with verified service providers</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('verification-form').addEventListener('submit', function(e) {
    const acceptTerms = document.querySelector('input[name="accept_terms"]');
    if (!acceptTerms.checked) {
        e.preventDefault();
        alert('Please accept the terms and conditions to proceed');
        return false;
    }
    
    const fileInput = document.querySelector('input[name="verification_document"]');
    if (fileInput.files.length === 0) {
        const confirmWithoutFile = confirm('No supporting document was uploaded. Are you sure you want to continue?');
        if (!confirmWithoutFile) {
            e.preventDefault();
            return false;
        }
    }
    
    // Show loading state
    const submitButton = document.querySelector('button[type="submit"]');
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Submitting...';
    
    // Re-enable button after 30 seconds as fallback
    setTimeout(() => {
        submitButton.disabled = false;
        submitButton.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Submit Verification Request';
    }, 30000);
});

// File upload preview
document.querySelector('input[name="verification_document"]').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const fileName = file.name;
        const fileSize = (file.size / 1024 / 1024).toFixed(2);
        
        // Show file info
        const fileInfo = document.createElement('div');
        fileInfo.className = 'mt-2 alert alert-info';
        fileInfo.innerHTML = `
            <i class="fas fa-file me-2"></i>
            Selected: ${fileName} (${fileSize} MB)
        `;
        
        // Remove existing file info
        const existingInfo = document.querySelector('.file-info');
        if (existingInfo) {
            existingInfo.remove();
        }
        
        fileInfo.classList.add('file-info');
        e.target.parentNode.appendChild(fileInfo);
    }
});
</script>
{% endblock %} 