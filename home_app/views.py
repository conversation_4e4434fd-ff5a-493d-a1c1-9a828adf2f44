"""Home page view for the CozyWish application."""

# --- Standard Library Imports ---
import logging

# --- Third-Party Imports ---
from django.contrib.auth import get_user_model
from django.shortcuts import render

# Get the custom user model
User = get_user_model()

# Set up logging
logger = logging.getLogger(__name__)


def home_view(request):
    """
    Display a simplified home page for development - only accounts_app and home_app are enabled.
    """
    logger.info("Displaying simplified home page - only accounts_app and home_app enabled")
    
    # Basic home page context without dependencies on disabled apps
    context = {
        'hero_section': True,
        'categories': [],
        'popular_categories': [],
        'popular_service_types': [],
        'search_form': None,
        'top_venues': [],
        'trending_venues': [],
        'discounted_venues': [],
        'development_mode': True,
        'message': 'Welcome to CozyWish! Currently in development mode with accounts_app and home_app only.',
    }
    
    return render(request, 'home_app/home.html', context)


def button_test_view(request):
    """Display the button test page for the design system."""
    return render(request, "button-test.html")


def design_system_showcase_view(request):
    """
    Display the complete design system showcase.
    """
    return render(request, 'design-system-showcase.html')


def card_test_view(request):
    """
    Display the card components test page for the design system.
    """
    return render(request, 'card-test.html')


def forms_test_view(request):
    """
    Display the forms components test page for the design system.
    """
    return render(request, 'forms-test.html')


def navigation_test_view(request):
    """
    Display the navigation components test page for the design system.
    """
    return render(request, 'navigation-test.html')


def navbar_variants_demo_view(request):
    """
    Display the navbar variants demo page for the design system.
    """
    return render(request, 'navbar-variants-demo.html')


def booking_test_view(request):
    """
    Display the booking components test page for the design system.
    """
    return render(request, 'booking-test.html')
