# --- Development Settings ---
from .base import *
import sys
from decouple import config

# --- Debug Mode ---
DEBUG = config("DEBUG", default=True, cast=bool)
ENABLE_TEST_VIEW = DEBUG

# --- Allowed Hosts ---
ALLOWED_HOSTS = ['localhost', '127.0.0.1', 'testserver']

# --- CSRF Configuration ---
CSRF_TRUSTED_ORIGINS = [
    'http://localhost:8000',
    'http://127.0.0.1:8000',
]

# --- Database Configuration ---
# Uses SQLite from base.py by default
# Can be overridden with DATABASE_URL environment variable
DATABASE_URL = os.environ.get('DATABASE_URL')
if DATABASE_URL:
    import dj_database_url
    DATABASES = {'default': dj_database_url.parse(DATABASE_URL, conn_max_age=600)}

# --- Media Files Configuration ---
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# --- Static Files Configuration ---
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# --- Storage Configuration ---
STORAGES = {
    "default": {
        "BACKEND": "django.core.files.storage.FileSystemStorage",
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
}

# --- Site Configuration ---
SITE_URL = config('SITE_URL', default='http://localhost:8000')

# --- Email Configuration ---
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Development can optionally use SendGrid for testing
EMAIL_HOST_PASSWORD = config('EMAIL_HOST_PASSWORD', default='')
FORCE_EMAIL_BACKEND = config('FORCE_EMAIL_BACKEND', default=False, cast=bool)

if FORCE_EMAIL_BACKEND and EMAIL_HOST_PASSWORD:
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
    EMAIL_HOST = config('EMAIL_HOST', default='smtp.sendgrid.net')
    EMAIL_PORT = config('EMAIL_PORT', default=587, cast=int)
    EMAIL_USE_TLS = config('EMAIL_USE_TLS', default=True, cast=bool)
    EMAIL_HOST_USER = config('EMAIL_HOST_USER', default='apikey')
    EMAIL_TIMEOUT = 30

DEFAULT_FROM_EMAIL = config('DEFAULT_FROM_EMAIL', default='<EMAIL>')
SERVER_EMAIL = config('SERVER_EMAIL', default='<EMAIL>')

# --- Celery Configuration Removed ---
# Celery dependencies have been removed from this project

# --- Security Settings (Development) ---
# Most security settings are disabled in development
SECURE_SSL_REDIRECT = False
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False
SECURE_HSTS_SECONDS = 0
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False
X_FRAME_OPTIONS = 'SAMEORIGIN'  # Allow framing for development tools

# --- Development Tools ---
# Enable Django Debug Toolbar if available
try:
    import debug_toolbar
    INSTALLED_APPS.append('debug_toolbar')
    MIDDLEWARE.insert(0, 'debug_toolbar.middleware.DebugToolbarMiddleware')
    INTERNAL_IPS = ['127.0.0.1', 'localhost']
except ImportError:
    pass

# --- Logging Configuration ---
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': LOG_LEVEL,
        },
        'project_root': {
            'handlers': ['console'],
            'level': LOG_LEVEL,
        },
    },
} 