"""
Customer forms for account management.

This module contains forms for customer signup, login, profile management,
and password changes. All forms include accessibility features and proper
validation.
"""

import re
from datetime import date
from django import forms
from django.contrib.auth import get_user_model
from django.contrib.auth.forms import User<PERSON>reationForm, PasswordChangeForm
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django_countries.fields import CountryField
from phonenumber_field.formfields import PhoneNumberField
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field, Submit, Div, HTML, Row, Column
from crispy_forms.bootstrap import FormActions

from accounts_app.constants import Gender
from accounts_app.models import CustomerProfile
from accounts_app.forms.common import AccessibleFormMixin
from accounts_app.mixins import (
    EmailValidationMixin, PasswordValidationMixin, TermsValidationMixin,
    PhoneValidationMixin, ImageValidationMixin
)

CustomUser = get_user_model()


# --- Customer signup form ---

class CustomerSignupForm(AccessibleFormMixin, EmailValidationMixin, PasswordValidationMixin, TermsValidationMixin, UserCreationForm):
    """
    Sign up a new customer with email and password.

    Features:
    - Email uniqueness validation
    - Password confirmation
    - Accessible form styling
    - Password strength validation with proper error display
    """
    email = forms.EmailField(
        label=_('Email Address'),
        max_length=254,
        widget=forms.EmailInput(
            attrs={
                'placeholder': _('Enter your email address'),
                'autocomplete': 'email',
                'required': True,
                'type': 'email',
                'maxlength': '254',
            }
        ),
        help_text=_('We\'ll never share your email with anyone else.')
    )
    password1 = forms.CharField(
        label=_('Password'),
        widget=forms.PasswordInput(
            attrs={
                'placeholder': _('Create a strong password'),
                'autocomplete': 'new-password',
                'required': True,
                'type': 'password',
                'minlength': '8',
                'pattern': r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$',
                'title': _('Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
            }
        ),
        help_text=_(
            'Your password must contain at least 8 characters, '
            'cannot be entirely numeric, and should not be too common.'
        )
    )
    password2 = forms.CharField(
        label=_('Confirm Password'),
        widget=forms.PasswordInput(
            attrs={
                'placeholder': _('Confirm your password'),
                'autocomplete': 'new-password',
                'required': True,
                'type': 'password',
                'minlength': '8',
            }
        ),
        help_text=_('Enter the same password as before, for verification.')
    )
    agree_to_terms = forms.BooleanField(
        label=_('I agree to the Terms of Service and Privacy Policy'),
        required=True,
        widget=forms.CheckboxInput(
            attrs={
                'required': True,
            }
        ),
        error_messages={
            'required': _('You must agree to the Terms of Service and Privacy Policy to create an account.'),
        }
    )

    class Meta:
        model = CustomUser
        fields = ('email', 'password1', 'password2', 'agree_to_terms')

    def __init__(self, *args, **kwargs):
        """
        Initialize form with crispy-forms helper and accessibility features.
        
        Sets up the form layout using crispy-forms with:
        - Bootstrap styling
        - Accessibility attributes
        - Validation classes
        - Responsive layout
        
        Args:
            *args: Variable length argument list
            **kwargs: Arbitrary keyword arguments
        """
        super().__init__(*args, **kwargs)
        
        # Configure crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        self.helper.layout = Layout(
            Field('email', css_class='form-control'),
            Field('password1', css_class='form-control'),
            Field('password2', css_class='form-control'),
            Div(
                Field('agree_to_terms', css_class='form-check-input'),
                css_class='form-check mb-3'
            ),
            FormActions(
                Submit('signup', _('Create Account'), css_class='btn-primary'),
                css_class='d-grid gap-2'
            )
        )

    def clean_email(self):
        """
        Ensure the email address is unique and normalize to lowercase.

        Raises:
            ValidationError: If the email already exists.

        Returns:
            str: The cleaned email address.
        """
        email = self.cleaned_data.get('email')
        if email:
            email = email.lower()
            if CustomUser.objects.filter(email=email).exists():
                raise ValidationError(
                    _('A user with this email already exists.'),
                    code='email_exists'
                )
        return email

    def clean_password1(self):
        """
        Validate password strength and complexity.

        Returns:
            str: The cleaned password1.

        Raises:
            ValidationError: If password doesn't meet requirements.
        """
        password1 = self.cleaned_data.get('password1')
        if password1:
            try:
                # Create a temporary user instance for validation if none exists
                user = self.instance
                if user is None or not user.pk:
                    # Create a temporary user with the email for validation
                    email = self.cleaned_data.get('email')
                    if email:
                        user = CustomUser(email=email)
                    else:
                        user = None
                validate_password(password1, user)
            except ValidationError as error:
                raise ValidationError(error.messages)
        return password1

    def clean_password2(self):
        """
        Override the default clean_password2 to avoid duplicate validation errors.

        Only check if passwords match, not password strength (already done in clean_password1).

        Returns:
            str: The cleaned password2.

        Raises:
            ValidationError: If passwords don't match.
        """
        password1 = self.cleaned_data.get('password1')
        password2 = self.cleaned_data.get('password2')

        if password1 and password2 and password1 != password2:
            raise ValidationError(
                _("The two password fields didn't match."),
                code='password_mismatch',
            )
        return password2

    def _post_clean(self):
        """
        Override _post_clean to prevent duplicate password validation.

        UserCreationForm's _post_clean validates passwords and adds errors to password2.
        Since we already validate in clean_password1, we skip the password validation
        but still call the parent's _post_clean for other processing.
        """
        # Call ModelForm's _post_clean, but skip UserCreationForm's password validation
        super(UserCreationForm, self)._post_clean()
        # Note: We don't call UserCreationForm's _post_clean to avoid duplicate validation

    def validate_password_for_user(self, user):
        """
        Override to prevent password validation errors from being added to password2.

        Since we already validate passwords in clean_password1, we don't need
        to validate them again here. This prevents duplicate validation and
        ensures errors appear on the password1 field.
        """
        # Do nothing - password validation is handled in clean_password1
        # This prevents Django from running validation again and adding errors to password2
        pass

    def clean_agree_to_terms(self):
        """
        Ensure the user has agreed to the terms of service.

        Raises:
            ValidationError: If the terms checkbox is not checked.

        Returns:
            bool: The cleaned agree_to_terms value.
        """
        agree_to_terms = self.cleaned_data.get('agree_to_terms')
        if not agree_to_terms:
            raise ValidationError(
                _('You must agree to the Terms of Service and Privacy Policy to create an account.'),
                code='terms_required'
            )
        return agree_to_terms

    def save(self, commit=True):
        """
        Save the new customer user instance.
        """
        user = super().save(commit=False)
        from accounts_app.constants import UserRoles
        user.role = UserRoles.CUSTOMER
        user.email = self.cleaned_data['email']
        user.email_verified = False
        if commit:
            user.save()
        return user



# --- Customer login form ---

class CustomerLoginForm(AccessibleFormMixin, forms.Form):
    """
    Authenticate a customer using email and password.

    Features:
    - Role validation
    - Active account check
    """
    email = forms.EmailField(
        label=_('Email Address'),
        max_length=254,
        widget=forms.EmailInput(
            attrs={
                'placeholder': _('Enter your email address'),
                'autocomplete': 'email',
                'required': True,
                'type': 'email',
                'maxlength': '254',
            }
        )
    )
    password = forms.CharField(
        label=_('Password'),
        widget=forms.PasswordInput(
            attrs={
                'placeholder': _('Enter your password'),
                'autocomplete': 'current-password',
                'required': True,
                'type': 'password',
                'minlength': '1',
            }
        )
    )

    def __init__(self, request=None, *args, **kwargs):
        """Initialize form with crispy-forms helper."""
        super().__init__(*args, **kwargs)
        self.request = request
        self.user_cache = None
        
        # Configure crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        self.helper.layout = Layout(
            Field('email', css_class='form-control'),
            Field('password', css_class='form-control'),
            FormActions(
                Submit('login', _('Sign In'), css_class='btn-primary'),
                css_class='d-grid gap-2'
            )
        )

    def clean(self):
        """
        Basic field validation - authentication is handled by the view.

        Returns:
            dict: The cleaned data.
        """
        cleaned = super().clean()
        # Just return cleaned data - authentication will be handled by the view using AuthenticationService
        return cleaned

    def get_user(self):
        """
        Retrieve the authenticated user after clean().

        Returns:
            CustomUser or None: The authenticated user.
        """
        return getattr(self, 'user_cache', None)



# --- Customer password change form ---

class CustomerPasswordChangeForm(AccessibleFormMixin, PasswordChangeForm):
    """
    Allow customers to change their password.

    Inherits built-in PasswordChangeForm with custom styling.
    """
    old_password = forms.CharField(
        label=_('Current Password'),
        widget=forms.PasswordInput(
            attrs={
                'placeholder': _('Enter your current password'),
                'autocomplete': 'current-password',
            }
        )
    )
    new_password1 = forms.CharField(
        label=_('New Password'),
        widget=forms.PasswordInput(
            attrs={
                'placeholder': _('Enter your new password'),
                'autocomplete': 'new-password',
            }
        ),
        help_text=_(
            'Your password must contain at least 8 characters and '
            'cannot be entirely numeric.'
        )
    )
    new_password2 = forms.CharField(
        label=_('Confirm New Password'),
        widget=forms.PasswordInput(
            attrs={
                'placeholder': _('Confirm your new password'),
                'autocomplete': 'new-password',
            }
        ),
        help_text=_('Enter the same password as before, for verification.')
    )

    def __init__(self, *args, **kwargs):
        """Initialize form with crispy-forms helper."""
        super().__init__(*args, **kwargs)
        
        # Configure crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        self.helper.layout = Layout(
            Field('old_password', css_class='form-control'),
            Field('new_password1', css_class='form-control'),
            Field('new_password2', css_class='form-control'),
            FormActions(
                Submit('change_password', _('Change Password'), css_class='btn-primary'),
                css_class='d-grid gap-2'
            )
        )



# --- Customer profile form ---

class CustomerProfileForm(AccessibleFormMixin, PhoneValidationMixin, ImageValidationMixin, forms.ModelForm):
    """
    Edit customer profile personal information.

    Features:
    - Date of birth selection
    - Phone number normalization
    - Profile picture processing
    """
    MONTH_CHOICES = [
        ('', _('Select Month')),
        (1, _('January')), (2, _('February')), (3, _('March')),
        (4, _('April')), (5, _('May')), (6, _('June')),
        (7, _('July')), (8, _('August')), (9, _('September')),
        (10, _('October')), (11, _('November')), (12, _('December')),
    ]
    YEAR_CHOICES = [('', _('Select Year'))] + [
        (year, str(year)) for year in range(1920, 2010)
    ]

    first_name = forms.CharField(
        label=_('First Name'),
        max_length=100, required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter your first name'),
            }
        )
    )
    last_name = forms.CharField(
        label=_('Last Name'),
        max_length=100, required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter your last name'),
            }
        )
    )
    phone_number = PhoneNumberField(
        label=_('Phone Number'),
        max_length=20, required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': '+****************',
                'type': 'tel',
            }
        ),
        help_text=_('Format: +****************')
    )
    gender = forms.ChoiceField(
        label=_('Gender'),
        choices=[('', _('Select Gender'))] +
                list(Gender.CHOICES),
        required=False,
        widget=forms.Select()
    )
    birth_month = forms.ChoiceField(
        label=_('Birth Month'),
        choices=MONTH_CHOICES, required=False,
        widget=forms.Select()
    )
    birth_year = forms.ChoiceField(
        label=_('Birth Year'),
        choices=YEAR_CHOICES, required=False,
        widget=forms.Select()
    )
    address = forms.CharField(
        label=_('Address'), max_length=255, required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter your address'),
            }
        )
    )
    city = forms.CharField(
        label=_('City'), max_length=100, required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter your city'),
            }
        )
    )
    zip_code = forms.CharField(
        label=_('ZIP Code'), max_length=10, required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter your ZIP code'),
            }
        )
    )
    profile_picture = forms.ImageField(
        label=_('Profile Picture'), required=False,
        widget=forms.FileInput(
            attrs={
                'accept': 'image/jpeg,image/png',
            }
        ),
        help_text=_(
            'Upload a profile picture (JPG or PNG only, max 5MB). '
            'Image will be resized to 800x800 pixels.'
        )
    )

    class Meta:
        model = CustomerProfile
        fields = [
            'first_name', 'last_name', 'phone_number', 'gender',
            'birth_month', 'birth_year', 'address', 'city',
            'zip_code', 'profile_picture',
        ]

    def __init__(self, *args, **kwargs):
        """Initialize form with crispy-forms helper."""
        super().__init__(*args, **kwargs)
        
        # Configure crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        self.helper.layout = Layout(
            HTML('<h5 class="mb-3">Personal Information</h5>'),
            Row(
                Column(
                    Field('first_name', css_class='form-control'),
                    css_class='col-md-6'
                ),
                Column(
                    Field('last_name', css_class='form-control'),
                    css_class='col-md-6'
                ),
                css_class='mb-3'
            ),
            Row(
                Column(
                    Field('phone_number', css_class='form-control'),
                    css_class='col-md-6'
                ),
                Column(
                    Field('gender', css_class='form-select'),
                    css_class='col-md-6'
                ),
                css_class='mb-3'
            ),
            HTML('<h5 class="mb-3">Date of Birth</h5>'),
            Row(
                Column(
                    Field('birth_month', css_class='form-select'),
                    css_class='col-md-6'
                ),
                Column(
                    Field('birth_year', css_class='form-select'),
                    css_class='col-md-6'
                ),
                css_class='mb-3'
            ),
            HTML('<h5 class="mb-3">Address</h5>'),
            Field('address', css_class='form-control'),
            Row(
                Column(
                    Field('city', css_class='form-control'),
                    css_class='col-md-6'
                ),
                Column(
                    Field('zip_code', css_class='form-control'),
                    css_class='col-md-6'
                ),
                css_class='mb-3'
            ),
            HTML('<h5 class="mb-3">Profile Picture</h5>'),
            Field('profile_picture', css_class='form-control'),
            FormActions(
                Submit('save_profile', _('Save Profile'), css_class='btn-primary'),
                css_class='d-grid gap-2'
            )
        )

    def clean_birth_month(self):
        """
        Convert empty birth_month selection to None.
        """
        month = self.cleaned_data.get('birth_month')
        return None if month == '' else month

    def clean_birth_year(self):
        """
        Convert empty birth_year selection to None.
        """
        year = self.cleaned_data.get('birth_year')
        return None if year == '' else year

    def clean_profile_picture(self):
        """
        Validate and process profile picture upload.

        Returns:
            ImageFieldFile: The cleaned profile picture.

        Raises:
            ValidationError: If the image is invalid.
        """
        image = self.cleaned_data.get('profile_picture')
        
        if image:
            # Basic validation
            if image.size > 5 * 1024 * 1024:  # 5MB limit
                raise ValidationError(
                    _('Profile picture must be smaller than 5MB.'),
                    code='file_too_large'
                )
            
            # Check file extension
            allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif']
            file_extension = image.name.lower()
            if not any(file_extension.endswith(ext) for ext in allowed_extensions):
                raise ValidationError(
                    _('Please upload a valid image file (JPG, PNG, or GIF).'),
                    code='invalid_file_type'
                )
        
        return image

    def save(self, commit=True):
        """
        Save the customer profile with proper data processing.

        Args:
            commit (bool): Whether to save to database.

        Returns:
            CustomerProfile: The saved profile instance.
        """
        profile = super().save(commit=False)
        
        # Process phone number
        if profile.phone_number:
            profile.phone_number = str(profile.phone_number)
        
        # Process birth date
        birth_month = self.cleaned_data.get('birth_month')
        birth_year = self.cleaned_data.get('birth_year')
        
        if birth_month and birth_year:
            try:
                profile.birth_date = date(int(birth_year), int(birth_month), 1)
            except (ValueError, TypeError):
                # If date creation fails, leave birth_date as None
                pass
        
        # Process profile picture
        if self.cleaned_data.get('profile_picture'):
            profile.profile_picture = self.cleaned_data['profile_picture']
        
        if commit:
            profile.save()
        return profile


# --- Backward Compatibility Alias ---
# For backward compatibility with existing tests
CustomerRegistrationForm = CustomerSignupForm


class CustomerEmailChangeForm(AccessibleFormMixin, EmailValidationMixin, forms.Form):
    """
    Allow customers to change their email address with password verification.
    
    Features:
    - Password verification for security
    - Email uniqueness validation
    - Automatic email verification reset
    """
    new_email = forms.EmailField(
        label=_('New Email Address'),
        max_length=254,
        widget=forms.EmailInput(
            attrs={
                'placeholder': _('Enter your new email address'),
                'autocomplete': 'email',
                'required': True,
                'type': 'email',
                'maxlength': '254',
            }
        ),
        help_text=_('We\'ll send a verification email to this address.')
    )
    password = forms.CharField(
        label=_('Current Password'),
        widget=forms.PasswordInput(
            attrs={
                'placeholder': _('Enter your current password'),
                'autocomplete': 'current-password',
                'required': True,
                'type': 'password',
            }
        ),
        help_text=_('Enter your current password to verify this change.')
    )

    def __init__(self, user=None, *args, **kwargs):
        """
        Initialize form with user instance for validation.
        
        Args:
            user: The user whose email is being changed
            *args: Variable length argument list
            **kwargs: Arbitrary keyword arguments
        """
        self.user = user
        super().__init__(*args, **kwargs)
        
        # Configure crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        self.helper.layout = Layout(
            Field('new_email', css_class='form-control'),
            Field('password', css_class='form-control'),
            FormActions(
                Submit('change_email', _('Change Email'), css_class='btn-primary'),
                css_class='d-grid gap-2'
            )
        )

    def clean_new_email(self):
        """
        Validate the new email address.
        
        Returns:
            str: The cleaned email address.
            
        Raises:
            ValidationError: If email is invalid, duplicate, or same as current.
        """
        new_email = self.cleaned_data.get('new_email')
        if not new_email:
            return new_email
            
        # Normalize email to lowercase
        new_email = new_email.lower()
        
        # Check if email is the same as current
        if self.user and new_email == self.user.email.lower():
            raise ValidationError(
                _('This is already your current email address.'),
                code='same_email'
            )
        
        # Check if email already exists
        if CustomUser.objects.filter(email=new_email).exists():
            raise ValidationError(
                _('A user with this email already exists.'),
                code='email_exists'
            )
        
        return new_email

    def clean_password(self):
        """
        Verify the current password.
        
        Returns:
            str: The cleaned password.
            
        Raises:
            ValidationError: If password is incorrect.
        """
        password = self.cleaned_data.get('password')
        if not password:
            return password
            
        if self.user and not self.user.check_password(password):
            raise ValidationError(
                _('Incorrect password.'),
                code='incorrect_password'
            )
        
        return password

    def save(self, commit=True):
        """
        Update the user's email address and reset verification status.
        
        Args:
            commit: Whether to save the user to database
            
        Returns:
            CustomUser: The updated user instance
        """
        if not self.user:
            raise ValueError("User instance is required to save email change.")
            
        new_email = self.cleaned_data['new_email']
        
        # Update email and reset verification
        self.user.email = new_email
        self.user.email_verified = False
        
        if commit:
            self.user.save()
        
        return self.user
