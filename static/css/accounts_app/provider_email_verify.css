/* CozyWish Design System - Provider Email Verify */
:root {
    --cw-brand-primary: #2F160F;
    --cw-brand-light: #4a2a1f;
    --cw-brand-accent: #fae1d7;
    --cw-accent-light: #fef7f0;
    --cw-accent-dark: #f1d4c4;
    --cw-neutral-600: #525252;
    --cw-neutral-700: #404040;
    --cw-neutral-800: #262626;
    --cw-success: #22c55e;
}

.email-verify-section {
    padding: 5rem 0;
    background: white;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.email-verify-container {
    max-width: 700px;
    width: 100%;
    margin: 0 auto;
    padding: 0 2rem;
}

.email-verify-card {
    background: white;
    border: 1px solid rgba(250, 225, 215, 0.3);
    border-radius: 1rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    width: 100%;
}

.email-verify-header {
    background: linear-gradient(135deg, #fef7f0 0%, #fae1d7 100%);
    padding: 3rem 3rem 2rem;
    text-align: center;
    position: relative;
    border-bottom: 1px solid #fae1d7;
}

.email-verify-header .content {
    position: relative;
    z-index: 2;
}

.email-verify-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%);
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(47, 22, 15, 0.2);
}

.email-verify-icon.warning {
    background: linear-gradient(135deg, #fef7f0 0%, #fae1d7 100%);
    color: #ffc107;
}

.email-verify-title {
    font-family: 'Playfair Display', Georgia, 'Times New Roman', serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--cw-brand-primary);
    margin-bottom: 1rem;
    line-height: 1.2;
}

.email-verify-subtitle {
    font-size: 1.125rem;
    color: var(--cw-neutral-600);
    margin-bottom: 0;
    line-height: 1.6;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.email-verify-body {
    padding: 3rem;
}

.alert-cw {
    border-radius: 0.5rem;
    padding: 1rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.alert-cw-success {
    background: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

.alert-cw-warning {
    background: #fffbeb;
    border-color: #fed7aa;
    color: #92400e;
}

.d-grid {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
}

.btn-cw-primary {
    background: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%);
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    padding: 1rem 2rem;
    color: white;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    letter-spacing: 0.025em;
    width: 100%;
    font-size: 1.125rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-cw-secondary {
    border: 2px solid #2F160F;
    color: #2F160F;
    border-radius: 0.5rem;
    font-weight: 600;
    padding: 1rem 2rem;
    background: white;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    letter-spacing: 0.025em;
    gap: 0.5rem;
    width: 100%;
}

.btn-cw-primary:hover, .btn-cw-secondary:hover {
    background: #4a2a1f;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(47, 22, 15, 0.2);
}

.tips-card {
    background: #fef7f0;
    border: 1px solid #fae1d7;
    border-radius: 1rem;
    padding: 2rem;
    margin-top: 2rem;
}

.tips-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2F160F;
    margin-bottom: 1rem;
}

.tips-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tips-list li {
    color: var(--cw-neutral-700);
    margin-bottom: 0.75rem;
    padding-left: 1.5rem;
    position: relative;
    font-size: 0.9rem;
    line-height: 1.5;
}

.tips-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--cw-success);
    font-weight: bold;
}

.next-steps-title {
    color: var(--cw-brand-primary);
    margin-bottom: 1.5rem;
}

.help-title {
    color: var(--cw-brand-primary);
    margin-bottom: 1.5rem;
}

.tips-help {
    color: var(--cw-neutral-700);
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.support-divider {
    border-top: 1px solid var(--cw-brand-accent);
}

.support-note {
    color: var(--cw-neutral-600);
    margin-bottom: 0;
}

.support-link {
    font-weight: 600;
}

@media (max-width: 768px) {
    .email-verify-section {
        padding: 3rem 0;
    }
    .email-verify-container {
        max-width: 600px;
        padding: 0 1.5rem;
    }
    .email-verify-header {
        padding: 2rem 2rem 1.5rem;
    }
    .email-verify-title {
        font-size: 2rem;
    }
    .email-verify-subtitle {
        font-size: 1rem;
    }
    .email-verify-body {
        padding: 2rem;
    }
    .tips-card {
        padding: 1.5rem;
    }
}

@media (max-width: 576px) {
    .email-verify-container {
        padding: 0 1rem;
    }
    .email-verify-header {
        padding: 1.5rem 1.5rem 1rem;
    }
    .email-verify-body {
        padding: 1.5rem;
    }
    .email-verify-title {
        font-size: 1.75rem;
    }
    .email-verify-icon {
        width: 64px;
        height: 64px;
        font-size: 1.5rem;
    }
    .tips-card {
        padding: 1rem;
    }
} 