### accounts_app


### Task 1: Split Large Models File
Refactor the accounts_app/models.py file (736 lines) by splitting it into separate model files for better organization. Create the following structure:

1. Create a models/ directory in accounts_app/
2. Split models.py into these files:
   - models/__init__.py - Import all models for backward compatibility
   - models/user.py - CustomUser and CustomUserManager models
   - models/profiles.py - CustomerProfile and ServiceProviderProfile models
   - models/security.py - LoginHistory and LoginAlert models
   - models/team.py - TeamMember model

Requirements:
- Maintain all existing functionality and relationships
- Keep all imports, validators, and custom methods
- Ensure backward compatibility by importing all models in __init__.py
- Preserve all Meta classes, indexes, and constraints
- Keep all docstrings and comments
- Update any internal imports within the models
- Test that all existing functionality works after refactoring

The current models.py contains:
- CustomUser (lines 61-156)
- LoginHistory (lines 158-186) 
- LoginAlert (lines 188-279)
- CustomerProfile (lines 281-428)
- ServiceProviderProfile (lines 430-667)
- TeamMember (lines 669-736)

Please create each file with proper imports and ensure the models maintain their relationships and functionality.


### Task 2: Refactor Large Customer Views File
Refactor the accounts_app/views/customer.py file (853 lines) by splitting it into smaller, more focused modules. Create the following structure:

1. Create a views/customer/ directory
2. Split customer.py into these files:
   - views/customer/__init__.py - Import all views for backward compatibility
   - views/customer/auth.py - Authentication views (signup, login, logout, password reset)
   - views/customer/profile.py - Profile management views (view, edit)
   - views/customer/account.py - Account management views (password change, deactivation)

Requirements:
- Maintain all existing functionality and URL patterns
- Keep all imports, decorators, and custom methods
- Preserve all docstrings and comments
- Ensure backward compatibility by importing all views in __init__.py
- Keep all form handling, validation, and error handling logic
- Maintain all logging and performance monitoring
- Preserve all template references and success URLs

The current customer.py contains:
- CustomerSignupView (lines 45-127)
- customer_login_view (lines 129-227)
- customer_logout_view (lines 228-252)
- unified_logout_view (lines 253-318)
- CustomerProfileView (lines 319-344)
- CustomerProfileEditView (lines 345-371)
- customer_change_password_view (lines 536-617)
- customer_deactivate_account_view (lines 618-700)
- Password reset views (lines 701-853)

Please organize related functionality together and ensure all imports and dependencies are properly maintained.

### Task 3: Refactor Large Provider Views File
Refactor the accounts_app/views/provider.py file (744 lines) by splitting it into smaller, more focused modules. Create the following structure:

1. Create a views/provider/ directory
2. Split provider.py into these files:
   - views/provider/__init__.py - Import all views for backward compatibility
   - views/provider/auth.py - Authentication views (signup, login, logout, email verification, password reset)
   - views/provider/profile.py - Profile management views (view, edit)
   - views/provider/account.py - Account management views (password change, deactivation, premium upgrade)

Requirements:
- Maintain all existing functionality and URL patterns
- Keep all imports, decorators, and custom methods
- Preserve all docstrings and comments
- Ensure backward compatibility by importing all views in __init__.py
- Keep all form handling, validation, and error handling logic
- Maintain all logging and performance monitoring
- Preserve all template references and success URLs
- Keep all email verification and business logic

The current provider.py contains:
- ServiceProviderSignupView (lines 35-207)
- provider_signup_done_view (lines 208-207)
- provider_email_verify_view (lines 208-273)
- service_provider_login_view (lines 274-343)
- service_provider_logout_view (lines 344-378)
- ServiceProviderProfileView (lines 379-432)
- ServiceProviderProfileEditView (lines 433-509)
- service_provider_change_password_view (lines 510-557)
- service_provider_deactivate_account_view (lines 558-600)
- Password reset views (lines 601-713)
- premium_upgrade (lines 714-744)

Please organize related functionality together and ensure all imports and dependencies are properly maintained.





### Task 4: Update Import Statements
After refactoring the models and views, update all import statements throughout the project to use the new modular structure. This includes:

1. Update accounts_app/admin.py imports
2. Update accounts_app/urls.py imports
3. Update all test files in accounts_app/tests/
4. Update any other files that import from accounts_app.models or accounts_app.views
5. Update any external apps that import these models or views

Requirements:
- Find all files that import from accounts_app.models or accounts_app.views
- Update imports to use the new modular structure
- Ensure all functionality continues to work
- Test that no import errors occur
- Maintain backward compatibility where possible

Use grep or search to find all import statements that need updating.



### Task 5: Install and Configure django-allauth
Install django-allauth and configure it in your CozyWish Django project. 

1. Install the required packages:
   - django-allauth
   - django-allauth[social] (for social authentication)

2. Update project_root/settings.py:
   - Add allauth apps to INSTALLED_APPS
   - Configure AUTHENTICATION_BACKENDS
   - Add allauth-specific settings
   - Configure email settings for allauth

3. Update project_root/urls.py to include allauth URLs

Requirements:
- Add allauth apps: 'allauth', 'allauth.account', 'allauth.socialaccount'
- Add social providers: 'allauth.socialaccount.providers.google', 'allauth.socialaccount.providers.apple'
- Configure AUTHENTICATION_BACKENDS to include allauth
- Set ACCOUNT_EMAIL_REQUIRED = True
- Set ACCOUNT_USERNAME_REQUIRED = False
- Set ACCOUNT_AUTHENTICATION_METHOD = 'email'
- Set ACCOUNT_EMAIL_VERIFICATION = 'mandatory'
- Set ACCOUNT_UNIQUE_EMAIL = True
- Set ACCOUNT_EMAIL_SUBJECT_PREFIX = '[CozyWish] '
- Set ACCOUNT_DEFAULT_HTTP_PROTOCOL = 'https'
- Set ACCOUNT_LOGIN_ATTEMPTS_LIMIT = 5
- Set ACCOUNT_LOGIN_ATTEMPTS_TIMEOUT = 300
- Set ACCOUNT_LOGOUT_REDIRECT_URL = '/'
- Set ACCOUNT_LOGIN_REDIRECT_URL = '/dashboard/'
- Set ACCOUNT_SIGNUP_REDIRECT_URL = '/dashboard/'
- Set SOCIALACCOUNT_AUTO_SIGNUP = False
- Set SOCIALACCOUNT_EMAIL_REQUIRED = True
- Set SOCIALACCOUNT_EMAIL_VERIFICATION = 'mandatory'
- Set SOCIALACCOUNT_PROVIDERS for Google and Apple
- Add allauth URLs to main URL configuration

The current settings.py already has:
- AUTH_USER_MODEL = 'accounts_app.CustomUser'
- Email configuration with SendGrid
- Security settings for production

Ensure allauth works with your existing CustomUser model and email configuration.




### Task 6: Update CustomUser Model for allauth Compatibility
Update the CustomUser model in accounts_app/models.py to be fully compatible with django-allauth while maintaining your existing role-based functionality.

Requirements:
1. Ensure the model inherits from AbstractUser correctly
2. Keep the existing role field and role-based properties
3. Add any required fields or methods that allauth expects
4. Ensure email field is properly configured as USERNAME_FIELD
5. Maintain backward compatibility with existing code
6. Add any missing methods that allauth might need

Current CustomUser model has:
- username = None (uses email as username)
- email field as unique identifier
- role field with choices: customer, service_provider, admin
- role properties: is_customer, is_service_provider, is_admin
- CustomUserManager with create_user method

Add any missing methods or fields that allauth requires while preserving all existing functionality. Ensure the model works seamlessly with both your existing authentication system and allauth.


### Task 7: Create Custom Allauth Adapters
Create custom allauth adapters to handle role-based user creation for customers and service providers in your CozyWish project.

Create the following files:

1. accounts_app/allauth_adapters.py - Custom adapters for allauth
2. accounts_app/allauth_forms.py - Custom forms for allauth

Requirements:

**Custom Account Adapter (allauth_adapters.py):**
- Create CozyWishAccountAdapter class inheriting from DefaultAccountAdapter
- Override save_user method to handle role assignment
- Implement custom logic for customer vs service provider signup
- Handle profile creation (CustomerProfile, ServiceProviderProfile)
- Maintain existing business logic for email verification
- Add custom validation for role-based signup
- Handle social account linking with role assignment

**Custom Social Account Adapter (allauth_adapters.py):**
- Create CozyWishSocialAccountAdapter class inheriting from DefaultSocialAccountAdapter
- Override populate_user method to extract user data from social providers
- Handle role assignment for social signups
- Implement custom logic for profile creation from social data
- Add validation for social account role assignment

**Custom Forms (allauth_forms.py):**
- Create CozyWishSignupForm inheriting from allauth.account.forms.SignupForm
- Add role selection field (customer/service_provider)
- Add business-specific fields for service providers
- Implement custom validation for role-based signup
- Handle profile creation in form save method
- Maintain existing form styling and accessibility features

The adapters should:
- Work with your existing CustomUser model
- Handle both regular and social authentication
- Maintain your existing role-based access control
- Preserve all existing business logic
- Support your existing profile models
- Handle email verification workflow
- Maintain security and validation requirements


### Task 8: Create Custom Allauth Forms
Create custom allauth forms to replace your existing CustomerSignupForm and ServiceProviderSignupForm while maintaining all functionality and design.

Create accounts_app/allauth_forms.py with the following forms:

**CozyWishSignupForm:**
- Inherit from allauth.account.forms.SignupForm
- Add role selection field (customer/service_provider)
- Include all fields from your existing CustomerSignupForm
- Add business-specific fields for service providers
- Maintain existing validation logic
- Keep existing form styling and accessibility
- Handle profile creation in save method

**CozyWishSocialSignupForm:**
- Inherit from allauth.socialaccount.forms.SignupForm
- Add role selection for social signups
- Handle profile creation for social users
- Maintain existing validation and styling

**CozyWishLoginForm:**
- Inherit from allauth.account.forms.LoginForm
- Maintain existing login validation
- Keep role-based access control
- Preserve existing styling and accessibility

**CozyWishPasswordChangeForm:**
- Inherit from allauth.account.forms.ChangePasswordForm
- Maintain existing password validation
- Keep existing styling and accessibility

**CozyWishPasswordResetForm:**
- Inherit from allauth.account.forms.ResetPasswordForm
- Maintain existing email validation
- Keep existing styling and accessibility

Requirements:
- Maintain all existing form validation logic
- Keep existing field styling and accessibility features
- Preserve role-based access control
- Handle profile creation for both customer and service provider roles
- Maintain existing error handling and user feedback
- Keep existing form field widgets and attributes
- Preserve existing help text and labels
- Maintain existing phone number normalization
- Keep existing terms of service agreement handling
- Handle business-specific fields for service providers
- Maintain existing password strength validation
- Preserve existing email uniqueness validation

The forms should seamlessly replace your existing forms while working with allauth's authentication system.


### Task 9: Update Allauth Templates
Create custom allauth templates that match your CozyWish design system and branding. Create templates in templates/allauth/ directory.

Required templates:

**Account Templates:**
- templates/allauth/account/login.html - Login page
- templates/allauth/account/signup.html - Signup page
- templates/allauth/account/email_confirm.html - Email confirmation
- templates/allauth/account/password_change.html - Password change
- templates/allauth/account/password_reset.html - Password reset
- templates/allauth/account/password_reset_done.html - Password reset done
- templates/allauth/account/password_reset_from_key.html - Password reset from key
- templates/allauth/account/password_reset_from_key_done.html - Password reset complete
- templates/allauth/account/logout.html - Logout confirmation

**Social Account Templates:**
- templates/allauth/socialaccount/login.html - Social login
- templates/allauth/socialaccount/signup.html - Social signup
- templates/allauth/socialaccount/connections.html - Social connections

**Email Templates:**
- templates/allauth/account/email/email_confirmation_message.txt
- templates/allauth/account/email/email_confirmation_subject.txt
- templates/allauth/account/email/password_reset_key_message.txt
- templates/allauth/account/email/password_reset_key_subject.txt

Requirements:
- Use your existing CozyWish design system (CSS variables, fonts, colors)
- Match the styling of your existing authentication templates
- Include your existing form styling classes
- Use your existing button styles and layouts
- Maintain accessibility features
- Include proper error handling and validation display
- Use your existing navigation and footer components
- Match your existing responsive design
- Include proper CSRF protection
- Use your existing message display system
- Maintain your existing branding and logo placement
- Include proper meta tags and SEO elements
- Use your existing favicon and icons
- Match your existing typography and spacing
- Include proper loading states and form validation
- Maintain your existing color scheme and gradients
- Use your existing shadow and border styles
- Include proper focus states and hover effects

The templates should look and feel exactly like your existing CozyWish authentication pages while working with allauth's form system.


## Wait, this prompt need to work manualy first
### Task 10: Configure Social Authentication Providers
Configure social authentication providers (Google and Apple) for your CozyWish project with django-allauth.

Requirements:

**Google OAuth2 Configuration:**
- Set up Google OAuth2 credentials in Google Cloud Console
- Configure SOCIALACCOUNT_PROVIDERS for Google in settings.py
- Set up proper redirect URIs
- Configure scopes for email and profile access
- Handle Google user data mapping to your CustomUser model

**Apple Sign-In Configuration:**
- Set up Apple Sign-In in Apple Developer Console
- Configure SOCIALACCOUNT_PROVIDERS for Apple in settings.py
- Set up proper redirect URIs
- Configure scopes for email and name access
- Handle Apple user data mapping to your CustomUser model

**Settings Configuration:**
- Add SOCIALACCOUNT_PROVIDERS configuration to settings.py
- Configure proper scopes for each provider
- Set up field mapping for user data extraction
- Configure proper callback URLs
- Set up email verification requirements
- Configure auto signup settings

**Environment Variables:**
- Add Google OAuth2 credentials to environment variables
- Add Apple Sign-In credentials to environment variables
- Configure proper secret management
- Set up development vs production configurations

**Custom Provider Settings:**
- Configure Google provider with proper scopes
- Configure Apple provider with proper scopes
- Set up field mapping for user data
- Configure email verification requirements
- Set up proper callback handling
- Configure auto signup behavior

The configuration should:
- Work seamlessly with your existing CustomUser model
- Handle role assignment for social users
- Maintain your existing profile creation logic
- Preserve your existing email verification workflow
- Handle proper error cases and edge scenarios
- Maintain security best practices
- Support both development and production environments
- Handle proper user data extraction and mapping
- Maintain your existing business logic for user creation


### Task 11: Update URL Configuration and Views
Update your URL configuration and views to work with django-allauth while maintaining backward compatibility with your existing authentication system.

Requirements:

**URL Configuration Updates:**
- Update project_root/urls.py to include allauth URLs
- Ensure proper URL precedence (allauth vs custom URLs)
- Handle URL conflicts between allauth and existing auth URLs
- Set up proper redirect URLs for allauth
- Configure custom URL patterns for role-based signup

**View Updates:**
- Update existing views to work with allauth
- Create custom views for role-based signup flows
- Handle allauth signal integration
- Update existing authentication views
- Maintain backward compatibility with existing URLs

**Custom URL Patterns:**
- Create custom URL patterns for customer signup
- Create custom URL patterns for service provider signup
- Handle role-based redirects after authentication
- Maintain existing URL structure where possible
- Set up proper URL namespacing

**Signal Integration:**
- Set up allauth signal handlers
- Handle user creation signals
- Handle email verification signals
- Handle social account connection signals
- Maintain existing logging and monitoring

**Backward Compatibility:**
- Ensure existing URLs still work
- Maintain existing view functionality
- Preserve existing form handling
- Keep existing error handling
- Maintain existing user feedback
- Preserve existing security measures

The integration should:
- Work seamlessly with your existing authentication system
- Maintain all existing functionality
- Preserve your existing URL structure
- Keep existing view logic and business rules
- Maintain existing security and validation
- Handle proper role-based access control
- Preserve existing logging and monitoring
- Maintain existing error handling and user feedback
- Support both allauth and custom authentication flows
- Handle proper redirects and URL routing


### Task 12: Testing and Migration Strategy
Create a comprehensive testing and migration strategy for integrating django-allauth into your CozyWish project.

Requirements:

Testing Strategy:
- Create unit tests for custom allauth adapters
- Test custom forms with allauth integration
- Test social authentication flows
- Test role-based user creation
- Test email verification workflows
- Test password reset functionality
- Test login/logout flows
- Test profile creation for different roles
- Test error handling and edge cases
- Test template rendering and styling

Security Testing:
- Test authentication security with allauth
- Verify CSRF protection
- Test session security
- Verify password security
- Test social authentication security
- Verify email verification security
- Test rate limiting and brute force protection
- Verify data privacy and GDPR compliance


Task 13:
Google OAuth Integration:
Install and configure django-allauth with Google provider settings.
Set up Google OAuth credentials in Google Cloud Console and add to environment variables.
Configure Google provider in allauth settings with proper callback URLs.
Create custom social account adapter to handle Google login role assignment.
Update login templates to include Google login button with proper styling.
Test Google OAuth flow for both customer and provider registration.

Task 14:
Crispy Forms Implementation
Upgrade to crispy-bootstrap5 and update settings configuration.
Replace all manual form rendering with crispy forms tags in templates.
Create custom crispy form layouts for better user experience.
Update form widgets to use crispy forms styling consistently.
Implement form validation messages using crispy forms error handling.

Task 15:
Template Refactoring
Break down large template files into smaller, reusable components using template includes.
Create base templates for authentication pages with consistent styling.
Extract common form components into separate template files.
Implement proper template inheritance hierarchy for better maintainability.
Create responsive design templates that work on all device sizes.


Task 16:
Form System Modernization
Replace custom form classes with allauth-compatible forms.
Implement proper form validation with better error messages.
Add client-side validation using JavaScript for better UX.
Create form mixins for common functionality across different user types.
Update form styling to use modern CSS frameworks and design patterns.

Task 17:
Authentication Flow Enhancement
Implement proper email verification workflow using allauth.
Add social account linking functionality for existing users.
Create account recovery and password reset flows using allauth.
Implement proper session management and security features.

Task 18:
User Profile Management
Update profile models to work seamlessly with allauth.
Implement profile completion workflows for new users.
Add profile picture upload functionality with proper validation.
Create profile editing forms with crispy forms integration.
Implement profile privacy settings and data export functionality.


Task 19:
Security and Validation
Implement proper CSRF protection and security headers.
Add rate limiting for authentication attempts.
Create comprehensive input validation and sanitization.
Implement proper password strength requirements.
Add account lockout functionality for failed login attempts.


 Task 20:
Testing and Quality Assurance
Write comprehensive tests for allauth integration.
Create tests for Google OAuth flow and social authentication.
Implement integration tests for complete user registration flows.
Add performance tests for authentication endpoints.
Create accessibility tests for all authentication forms and templates.

Task 21:
Documentation and Maintenance
Create comprehensive documentation for the new authentication system.
Create user guides for Google login and account management.
Implement proper logging and monitoring for authentication events.
Create maintenance scripts for user data management and cleanup.



