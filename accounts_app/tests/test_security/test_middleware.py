"""
Tests for security middleware functionality.
"""

import pytest
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.contrib.auth.signals import user_login_failed, user_logged_in
from django.http import HttpResponse
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from unittest.mock import patch, MagicMock

from accounts_app.middleware.security import (
    AccountLockoutMiddleware,
    PasswordHistoryMiddleware,
    handle_failed_login,
    handle_successful_login,
    get_client_ip_from_request
)
from accounts_app.models import AccountLockout, PasswordHistory
from accounts_app.constants import UserRoles

User = get_user_model()


class AccountLockoutMiddlewareTests(TestCase):
    """Test cases for AccountLockoutMiddleware"""

    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()
        self.middleware = AccountLockoutMiddleware(lambda request: HttpResponse(b"OK"))
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER
        )
        self.ip_address = '***********'

    def test_get_client_ip_direct(self):
        """Test getting client IP from REMOTE_ADDR"""
        request = self.factory.get('/')
        request.META['REMOTE_ADDR'] = '***********00'
        
        ip = self.middleware.get_client_ip(request)
        self.assertEqual(ip, '***********00')

    def test_get_client_ip_forwarded(self):
        """Test getting client IP from X-Forwarded-For"""
        request = self.factory.get('/')
        request.META['HTTP_X_FORWARDED_FOR'] = '***********, ***********'
        request.META['REMOTE_ADDR'] = '***********00'
        
        ip = self.middleware.get_client_ip(request)
        self.assertEqual(ip, '***********')

    def test_middleware_normal_request(self):
        """Test middleware with normal request (no lockout)"""
        request = self.factory.get('/')
        request.META['REMOTE_ADDR'] = self.ip_address
        
        response = self.middleware(request)
        self.assertEqual(response.status_code, 200)

    def test_middleware_locked_ip(self):
        """Test middleware with locked IP address"""
        # Create a lockout record
        lockout = AccountLockout.objects.create(  # type: ignore
            ip_address=self.ip_address,
            failed_attempts=5,
            locked_until=timezone.now() + timedelta(minutes=30)
        )
        
        request = self.factory.get('/')
        request.META['REMOTE_ADDR'] = self.ip_address
        
        response = self.middleware(request)
        self.assertEqual(response.status_code, 423)  # Locked status

    def test_middleware_expired_lockout(self):
        """Test middleware with expired lockout"""
        # Create an expired lockout record
        lockout = AccountLockout.objects.create(
            ip_address=self.ip_address,
            failed_attempts=5,
            locked_until=timezone.now() - timedelta(minutes=30)
        )
        
        request = self.factory.get('/')
        request.META['REMOTE_ADDR'] = self.ip_address
        
        response = self.middleware(request)
        self.assertEqual(response.status_code, 200)  # Should allow access

    def test_render_lockout_response_template(self):
        """Test lockout response with template"""
        lockout = AccountLockout.objects.create(
            ip_address=self.ip_address,
            failed_attempts=5,
            locked_until=timezone.now() + timedelta(minutes=30)
        )
        
        request = self.factory.get('/')
        request.META['REMOTE_ADDR'] = self.ip_address
        
        response = self.middleware.render_lockout_response(request, lockout)
        self.assertEqual(response.status_code, 423)

    @patch('accounts_app.middleware.security.TemplateResponse')
    def test_render_lockout_response_fallback(self, mock_template_response):
        """Test lockout response fallback when template fails"""
        mock_template_response.side_effect = Exception("Template error")
        
        lockout = AccountLockout.objects.create(
            ip_address=self.ip_address,
            failed_attempts=5,
            locked_until=timezone.now() + timedelta(minutes=30)
        )
        
        request = self.factory.get('/')
        request.META['REMOTE_ADDR'] = self.ip_address
        
        response = self.middleware.render_lockout_response(request, lockout)
        self.assertEqual(response.status_code, 423)
        self.assertEqual(getattr(response, 'content_type', 'text/plain'), 'text/plain')


class PasswordHistoryMiddlewareTests(TestCase):
    """Test cases for PasswordHistoryMiddleware"""

    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()
        self.middleware = PasswordHistoryMiddleware(lambda request: HttpResponse(b"OK"))
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER
        )

    def test_middleware_no_password_change(self):
        """Test middleware when password doesn't change"""
        request = self.factory.get('/')
        request.user = self.user
        original_password = self.user.password
        
        response = self.middleware(request)
        
        self.assertEqual(response.status_code, 200)
        # Password history should not be updated
        self.assertEqual(PasswordHistory.objects.count(), 0)

    def test_middleware_password_change(self):
        """Test middleware when password changes"""
        request = self.factory.get('/')
        request.user = self.user
        original_password = self.user.password
        
        # Simulate password change by modifying the user object after middleware processes
        # This mimics what happens when Django's password change process occurs
        response = self.middleware(request)
        
        # Now change the password to simulate the change that would occur during request processing
        self.user.password = 'new_hashed_password'
        self.user.save()
        
        # The middleware should not detect this change since it happens after the middleware processes
        # This test verifies the middleware doesn't interfere with normal requests
        self.assertEqual(response.status_code, 200)
        # Password history should not be updated in this case since the change happens after middleware
        self.assertEqual(PasswordHistory.objects.count(), 0)  # type: ignore

    def test_middleware_unauthenticated_user(self):
        """Test middleware with unauthenticated user"""
        request = self.factory.get('/')
        request.user = MagicMock()
        request.user.is_authenticated = False
        
        response = self.middleware(request)
        self.assertEqual(response.status_code, 200)

    def test_middleware_no_user_attribute(self):
        """Test middleware when request has no user attribute"""
        request = self.factory.get('/')
        
        response = self.middleware(request)
        self.assertEqual(response.status_code, 200)


class SecuritySignalHandlerTests(TestCase):
    """Test cases for security signal handlers"""

    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER
        )
        self.ip_address = '***********'

    def test_handle_failed_login_with_user(self):
        """Test handling failed login with known user"""
        request = self.factory.post('/login/')
        request.META['REMOTE_ADDR'] = self.ip_address
        
        credentials = {'email': self.user.email, 'password': 'wrongpass'}
        
        handle_failed_login(None, credentials, request)
        
        # Check that lockout record was created
        lockout = AccountLockout.objects.get(ip_address=self.ip_address)
        self.assertEqual(lockout.failed_attempts, 1)
        self.assertEqual(lockout.user, self.user)

    def test_handle_failed_login_unknown_user(self):
        """Test handling failed login with unknown user"""
        request = self.factory.post('/login/')
        request.META['REMOTE_ADDR'] = self.ip_address
        
        credentials = {'email': '<EMAIL>', 'password': 'wrongpass'}
        
        handle_failed_login(None, credentials, request)
        
        # Check that lockout record was created
        lockout = AccountLockout.objects.get(ip_address=self.ip_address)
        self.assertEqual(lockout.failed_attempts, 1)
        self.assertIsNone(lockout.user)

    def test_handle_failed_login_multiple_attempts(self):
        """Test handling multiple failed login attempts"""
        request = self.factory.post('/login/')
        request.META['REMOTE_ADDR'] = self.ip_address
        
        credentials = {'email': self.user.email, 'password': 'wrongpass'}
        
        # Multiple failed attempts
        for _ in range(5):
            handle_failed_login(None, credentials, request)
        
        # Check that account is locked
        lockout = AccountLockout.objects.get(ip_address=self.ip_address)
        self.assertEqual(lockout.failed_attempts, 5)
        self.assertTrue(lockout.is_locked)

    def test_handle_successful_login(self):
        """Test handling successful login"""
        # Create a lockout record first
        lockout = AccountLockout.objects.create(
            ip_address=self.ip_address,
            user=self.user,
            failed_attempts=3
        )
        
        request = self.factory.post('/login/')
        request.META['REMOTE_ADDR'] = self.ip_address
        
        handle_successful_login(None, self.user, request)
        
        # Check that lockout was reset
        lockout.refresh_from_db()
        self.assertEqual(lockout.failed_attempts, 0)
        self.assertIsNone(lockout.locked_until)

    def test_handle_failed_login_no_request(self):
        """Test handling failed login without request"""
        credentials = {'email': self.user.email, 'password': 'wrongpass'}
        
        # Should not raise an exception
        handle_failed_login(None, credentials, None)

    def test_handle_successful_login_no_request(self):
        """Test handling successful login without request"""
        # Should not raise an exception
        handle_successful_login(None, self.user, None)


class SecurityUtilityTests(TestCase):
    """Test cases for security utility functions"""

    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()

    def test_get_client_ip_from_request_direct(self):
        """Test getting client IP from REMOTE_ADDR"""
        request = self.factory.get('/')
        request.META['REMOTE_ADDR'] = '***********00'
        
        ip = get_client_ip_from_request(request)
        self.assertEqual(ip, '***********00')

    def test_get_client_ip_from_request_forwarded(self):
        """Test getting client IP from X-Forwarded-For"""
        request = self.factory.get('/')
        request.META['HTTP_X_FORWARDED_FOR'] = '***********, ***********'
        request.META['REMOTE_ADDR'] = '***********00'
        
        ip = get_client_ip_from_request(request)
        self.assertEqual(ip, '***********')

    def test_get_client_ip_from_request_no_forwarded(self):
        """Test getting client IP when X-Forwarded-For is not present"""
        request = self.factory.get('/')
        request.META['REMOTE_ADDR'] = '***********00'
        
        ip = get_client_ip_from_request(request)
        self.assertEqual(ip, '***********00')


@pytest.mark.django_db
class SecurityMiddlewarePytestTests:
    """Pytest-style tests for security middleware"""

    def test_middleware_integration(self, rf):
        """Test middleware integration with Django"""
        middleware = AccountLockoutMiddleware(lambda request: HttpResponse("OK"))
        
        request = rf.get('/')
        request.META['REMOTE_ADDR'] = '***********'
        
        response = middleware(request)
        assert response.status_code == 200

    def test_password_history_integration(self, rf):
        """Test password history middleware integration"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER
        )
        
        middleware = PasswordHistoryMiddleware(lambda request: HttpResponse("OK"))
        
        request = rf.get('/')
        request.user = user
        
        response = middleware(request)
        assert response.status_code == 200 