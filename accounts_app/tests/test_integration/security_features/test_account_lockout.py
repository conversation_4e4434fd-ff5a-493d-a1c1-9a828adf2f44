"""
Account lockout security integration tests.

These tests cover the complete account lockout workflow including
brute force protection, failed login tracking, and lockout recovery.
"""

import pytest
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from unittest.mock import patch
from freezegun import freeze_time

from accounts_app.models import (
    LoginHistory, UserSecurity, AccountLockout, 
    CustomerProfile, ServiceProviderProfile
)
from accounts_app.constants import UserRoles, UserStatus, ModelConstants
from accounts_app.services import AuthenticationService
from ..fixtures.test_data import CustomerTestData, ServiceProviderTestData, SecurityTestData

User = get_user_model()


class AccountLockoutWorkflowTest(TestCase):
    """Test complete account lockout workflow."""
    
    def setUp(self):
        """Set up test client and data."""
        self.client = Client()
        self.customer_data = CustomerTestData()
        self.security_data = SecurityTestData()
        
    def test_customer_account_lockout_after_failed_attempts(self):
        """Test customer account gets locked after multiple failed attempts."""
        # Create customer user
        user = self.customer_data.create_active_customer()
        login_url = reverse('accounts_app:customer_login')
        
        # Step 1: Attempt login with wrong password multiple times
        for i in range(ModelConstants.MAX_LOGIN_ATTEMPTS):
            response = self.client.post(login_url, {
                'email': user.email,
                'password': 'WrongPassword123!'
            })
            
            # Should stay on login page
            self.assertEqual(response.status_code, 200)
            self.assertNotIn('_auth_user_id', self.client.session)
            
            # Verify failed attempt recorded
            failed_attempts = LoginHistory.objects.filter(
                user=user, 
                is_successful=False
            ).count()
            self.assertEqual(failed_attempts, i + 1)
            
        # Step 2: Verify account is locked
        security = UserSecurity.objects.get(user=user)
        self.assertEqual(security.failed_login_attempts, ModelConstants.MAX_LOGIN_ATTEMPTS)
        self.assertTrue(security.is_account_locked)
        
        # Step 3: Try to login with correct password (should fail due to lockout)
        response = self.client.post(login_url, {
            'email': user.email,
            'password': 'TestPass123!'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertNotIn('_auth_user_id', self.client.session)
        self.assertContains(response, 'locked')
        
    def test_provider_account_lockout_after_failed_attempts(self):
        """Test service provider account gets locked after multiple failed attempts."""
        # Create provider user
        provider_data = ServiceProviderTestData()
        user = provider_data.create_active_provider()
        login_url = reverse('accounts_app:service_provider_login')
        
        # Attempt login with wrong password multiple times
        for i in range(ModelConstants.MAX_LOGIN_ATTEMPTS):
            response = self.client.post(login_url, {
                'email': user.email,
                'password': 'WrongPassword123!'
            })
            
            self.assertEqual(response.status_code, 200)
            self.assertNotIn('_auth_user_id', self.client.session)
            
        # Verify account is locked
        security = UserSecurity.objects.get(user=user)
        self.assertTrue(security.is_account_locked)
        
        # Try to login with correct password (should fail due to lockout)
        response = self.client.post(login_url, {
            'email': user.email,
            'password': 'TestPass123!'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertNotIn('_auth_user_id', self.client.session)
        self.assertContains(response, 'locked')
        
    def test_account_lockout_automatic_recovery(self):
        """Test account lockout automatically recovers after timeout."""
        user = self.customer_data.create_active_customer()
        login_url = reverse('accounts_app:customer_login')
        
        # Lock the account
        security = UserSecurity.objects.get(user=user)
        security.failed_login_attempts = ModelConstants.MAX_LOGIN_ATTEMPTS
        security.lock_account(ModelConstants.LOCKOUT_DURATION_MINUTES)
        
        # Verify account is locked
        self.assertTrue(security.is_account_locked)
        
        # Simulate time passing beyond lockout duration
        with freeze_time(timezone.now() + timedelta(minutes=ModelConstants.LOCKOUT_DURATION_MINUTES + 1)):
            # Try to login - should succeed as lockout expired
            response = self.client.post(login_url, {
                'email': user.email,
                'password': 'TestPass123!'
            })
            
            self.assertEqual(response.status_code, 302)
            self.assertEqual(str(user.pk), self.client.session['_auth_user_id'])
            
            # Verify security record is reset
            security.refresh_from_db()
            self.assertEqual(security.failed_login_attempts, 0)
            self.assertFalse(security.is_account_locked)
            
    def test_successful_login_resets_failed_attempts(self):
        """Test successful login resets failed attempt counter."""
        user = self.customer_data.create_active_customer()
        login_url = reverse('accounts_app:customer_login')
        
        # Make some failed attempts
        for i in range(2):
            self.client.post(login_url, {
                'email': user.email,
                'password': 'WrongPassword123!'
            })
            
        # Verify failed attempts recorded
        security = UserSecurity.objects.get(user=user)
        self.assertEqual(security.failed_login_attempts, 2)
        
        # Successful login
        response = self.client.post(login_url, {
            'email': user.email,
            'password': 'TestPass123!'
        })
        
        self.assertEqual(response.status_code, 302)
        self.assertEqual(str(user.pk), self.client.session['_auth_user_id'])
        
        # Verify failed attempts reset
        security.refresh_from_db()
        self.assertEqual(security.failed_login_attempts, 0)
        
    def test_lockout_prevents_brute_force_attacks(self):
        """Test lockout effectively prevents brute force attacks."""
        user = self.customer_data.create_active_customer()
        login_url = reverse('accounts_app:customer_login')
        
        # Lock account
        security = UserSecurity.objects.get(user=user)
        security.lock_account(ModelConstants.LOCKOUT_DURATION_MINUTES)
        
        # Attempt many logins while locked
        for i in range(20):
            response = self.client.post(login_url, {
                'email': user.email,
                'password': 'TestPass123!'
            })
            
            # Should always fail
            self.assertEqual(response.status_code, 200)
            self.assertNotIn('_auth_user_id', self.client.session)
            
        # Verify account is still locked
        security.refresh_from_db()
        self.assertTrue(security.is_account_locked)


class IPBasedLockoutTest(TestCase):
    """Test IP-based lockout functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.customer_data = CustomerTestData()
        
    def test_ip_lockout_after_multiple_failed_attempts(self):
        """Test IP gets locked after multiple failed attempts from different accounts."""
        # Create multiple users
        users = []
        for i in range(3):
            user = self.customer_data.create_active_customer(
                email=f'user{i}@example.com'
            )
            users.append(user)
            
        login_url = reverse('accounts_app:customer_login')
        
        # Attempt login from same IP with different accounts
        for user in users:
            for _ in range(2):
                response = self.client.post(login_url, {
                    'email': user.email,
                    'password': 'WrongPassword123!'
                }, HTTP_X_FORWARDED_FOR='*************')
                
                self.assertEqual(response.status_code, 200)
                
        # Check if IP lockout exists
        ip_lockout = AccountLockout.objects.filter(
            ip_address='*************'
        ).first()
        
        if ip_lockout:
            self.assertTrue(ip_lockout.is_locked)
            
    def test_ip_lockout_affects_all_users_from_ip(self):
        """Test IP lockout affects all users from the locked IP."""
        # Create user and lock IP
        user = self.customer_data.create_active_customer()
        AccountLockout.objects.create(
            ip_address='*************',
            failed_attempts=ModelConstants.MAX_LOGIN_ATTEMPTS,
            locked_until=timezone.now() + timedelta(minutes=30)
        )
        
        login_url = reverse('accounts_app:customer_login')
        
        # Try to login from locked IP
        response = self.client.post(login_url, {
            'email': user.email,
            'password': 'TestPass123!'
        }, HTTP_X_FORWARDED_FOR='*************')
        
        # Should be blocked
        self.assertEqual(response.status_code, 200)
        self.assertNotIn('_auth_user_id', self.client.session)
        
        # Should be able to login from different IP
        response = self.client.post(login_url, {
            'email': user.email,
            'password': 'TestPass123!'
        }, HTTP_X_FORWARDED_FOR='*************')
        
        self.assertEqual(response.status_code, 302)
        self.assertEqual(str(user.pk), self.client.session['_auth_user_id'])


class FailedLoginTrackingTest(TestCase):
    """Test failed login tracking functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.customer_data = CustomerTestData()
        
    def test_failed_login_history_tracking(self):
        """Test failed login attempts are properly tracked."""
        user = self.customer_data.create_active_customer()
        login_url = reverse('accounts_app:customer_login')
        
        # Make failed login attempts
        for i in range(3):
            response = self.client.post(login_url, {
                'email': user.email,
                'password': 'WrongPassword123!'
            }, HTTP_X_FORWARDED_FOR='*************')
            
            # Verify login history created
            login_history = LoginHistory.objects.filter(
                user=user,
                is_successful=False
            ).order_by('-attempt_time')
            
            self.assertEqual(login_history.count(), i + 1)
            
            # Verify latest attempt details
            latest_attempt = login_history.first()
            self.assertEqual(latest_attempt.ip_address, '*************')
            self.assertFalse(latest_attempt.is_successful)
            self.assertEqual(latest_attempt.failure_reason, 'invalid_credentials')
            
    def test_successful_login_history_tracking(self):
        """Test successful login attempts are properly tracked."""
        user = self.customer_data.create_active_customer()
        login_url = reverse('accounts_app:customer_login')
        
        # Make successful login
        response = self.client.post(login_url, {
            'email': user.email,
            'password': 'TestPass123!'
        }, HTTP_X_FORWARDED_FOR='*************')
        
        self.assertEqual(response.status_code, 302)
        
        # Verify login history created
        login_history = LoginHistory.objects.filter(
            user=user,
            is_successful=True
        ).first()
        
        self.assertIsNotNone(login_history)
        self.assertEqual(login_history.ip_address, '*************')
        self.assertTrue(login_history.is_successful)
        self.assertIsNone(login_history.failure_reason)
        
    def test_login_history_includes_user_agent(self):
        """Test login history includes user agent information."""
        user = self.customer_data.create_active_customer()
        login_url = reverse('accounts_app:customer_login')
        
        # Make login with custom user agent
        response = self.client.post(login_url, {
            'email': user.email,
            'password': 'TestPass123!'
        }, HTTP_USER_AGENT='Mozilla/5.0 (Test Browser)')
        
        self.assertEqual(response.status_code, 302)
        
        # Verify user agent recorded
        login_history = LoginHistory.objects.filter(user=user).first()
        self.assertIsNotNone(login_history)
        self.assertIn('Mozilla/5.0 (Test Browser)', login_history.user_agent)


class SecurityServiceIntegrationTest(TestCase):
    """Test integration with security services."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.customer_data = CustomerTestData()
        
    @patch('accounts_app.services.AuthenticationService.authenticate_user')
    def test_authentication_service_security_integration(self, mock_auth):
        """Test authentication service properly handles security features."""
        user = self.customer_data.create_active_customer()
        
        # Mock authentication service to return security error
        from accounts_app.services import AuthenticationError
        mock_auth.side_effect = AuthenticationError("Account is locked")
        
        login_url = reverse('accounts_app:customer_login')
        response = self.client.post(login_url, {
            'email': user.email,
            'password': 'TestPass123!'
        })
        
        # Should handle security error gracefully
        self.assertEqual(response.status_code, 200)
        self.assertNotIn('_auth_user_id', self.client.session)
        self.assertContains(response, 'locked')
        
    def test_security_middleware_integration(self):
        """Test security features work with middleware."""
        user = self.customer_data.create_active_customer()
        
        # Lock account
        security = UserSecurity.objects.get(user=user)
        security.lock_account(30)
        
        # Try to access protected page (should be blocked before reaching view)
        response = self.client.get(reverse('accounts_app:customer_profile'))
        
        # Should redirect to login
        self.assertEqual(response.status_code, 302)
        self.assertIn('login', response.url)


@pytest.mark.django_db
class SecurityFeaturesPytestTest:
    """Pytest-style security features tests."""
    
    def test_concurrent_lockout_handling(self):
        """Test handling of concurrent lockout scenarios."""
        customer_data = CustomerTestData()
        user = customer_data.create_active_customer()
        
        # Create multiple clients to simulate concurrent access
        clients = [Client() for _ in range(3)]
        login_url = reverse('accounts_app:customer_login')
        
        # Each client makes failed attempts
        for client in clients:
            for _ in range(2):
                response = client.post(login_url, {
                    'email': user.email,
                    'password': 'WrongPassword123!'
                })
                assert response.status_code == 200
                
        # Verify account is locked
        security = UserSecurity.objects.get(user=user)
        assert security.failed_login_attempts >= ModelConstants.MAX_LOGIN_ATTEMPTS
        assert security.is_account_locked
        
    def test_security_logging_integration(self):
        """Test security events are properly logged."""
        customer_data = CustomerTestData()
        user = customer_data.create_active_customer()
        
        client = Client()
        login_url = reverse('accounts_app:customer_login')
        
        # Make failed login (should be logged)
        response = client.post(login_url, {
            'email': user.email,
            'password': 'WrongPassword123!'
        })
        
        assert response.status_code == 200
        
        # Verify login history exists
        login_history = LoginHistory.objects.filter(user=user, is_successful=False)
        assert login_history.exists()
        
        # Verify security record updated
        security = UserSecurity.objects.get(user=user)
        assert security.failed_login_attempts > 0
        
    def test_security_features_across_user_types(self):
        """Test security features work consistently across user types."""
        customer_data = CustomerTestData()
        provider_data = ServiceProviderTestData()
        
        customer = customer_data.create_active_customer()
        provider = provider_data.create_active_provider()
        
        # Test lockout for both user types
        for user, login_url in [
            (customer, reverse('accounts_app:customer_login')),
            (provider, reverse('accounts_app:service_provider_login'))
        ]:
            client = Client()
            
            # Make failed attempts
            for _ in range(ModelConstants.MAX_LOGIN_ATTEMPTS):
                response = client.post(login_url, {
                    'email': user.email,
                    'password': 'WrongPassword123!'
                })
                assert response.status_code == 200
                
            # Verify account is locked
            security = UserSecurity.objects.get(user=user)
            assert security.is_account_locked
            
            # Try correct password (should still fail)
            response = client.post(login_url, {
                'email': user.email,
                'password': 'TestPass123!'
            })
            assert response.status_code == 200
            assert '_auth_user_id' not in client.session 