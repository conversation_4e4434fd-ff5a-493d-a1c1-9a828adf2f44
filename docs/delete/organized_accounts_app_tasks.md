# CozyWish Accounts App Modernization - Organized Task List

## Overview
This document contains a well-organized task list for modernizing the CozyWish accounts app. Tasks are broken down into 3 main phases with clear, AI-friendly prompts for each task.

## Modern Package Versions to Use
- `django-allauth==65.9.0` (latest stable)
- `crispy-bootstrap5==2025.6` (latest)
- `django-crispy-forms==2.4` (latest)
- `django-environ==0.12.0` (latest)
- `python-dotenv==1.1.1` (latest)

---

# PHASE 1: CODE ORGANIZATION

## Task 1: Split Large Models File
**Prompt for AI:**
```
Refactor the accounts_app/models.py file (736 lines) by creating a models/ directory structure. 

Create these files:
- accounts_app/models/__init__.py (import all models for backward compatibility)
- accounts_app/models/user.py (CustomUser and CustomUserManager - lines 61-156)
- accounts_app/models/profiles.py (CustomerProfile and ServiceProviderProfile - lines 281-667)
- accounts_app/models/security.py (LoginHistory and LoginAlert - lines 158-279)
- accounts_app/models/team.py (TeamMember - lines 669-736)

Requirements:
- Maintain all existing functionality, relationships, and imports
- Keep all validators, custom methods, Meta classes, indexes, and constraints
- Preserve all docstrings and comments
- Ensure backward compatibility by importing all models in __init__.py
- Update any internal imports within the models
```

## Task 2: Refactor Customer Views
**Prompt for AI:**
```
Refactor accounts_app/views/customer.py (853 lines) by creating a views/customer/ directory structure.

Create these files:
- accounts_app/views/customer/__init__.py (import all views for backward compatibility)
- accounts_app/views/customer/auth.py (CustomerSignupView, customer_login_view, customer_logout_view, unified_logout_view, password reset views - lines 45-318, 701-853)
- accounts_app/views/customer/profile.py (CustomerProfileView, CustomerProfileEditView - lines 319-371)
- accounts_app/views/customer/account.py (customer_change_password_view, customer_deactivate_account_view - lines 536-700)

Requirements:
- Maintain all existing functionality, URL patterns, and imports
- Keep all decorators, custom methods, form handling, and validation logic
- Preserve all docstrings, comments, logging, and performance monitoring
- Ensure backward compatibility by importing all views in __init__.py
- Maintain all template references and success URLs
```

## Task 3: Refactor Provider Views
**Prompt for AI:**
```
Refactor accounts_app/views/provider.py (744 lines) by creating a views/provider/ directory structure.

Create these files:
- accounts_app/views/provider/__init__.py (import all views for backward compatibility)
- accounts_app/views/provider/auth.py (ServiceProviderSignupView, provider_signup_done_view, provider_email_verify_view, service_provider_login_view, service_provider_logout_view, password reset views - lines 35-343, 601-713)
- accounts_app/views/provider/profile.py (ServiceProviderProfileView, ServiceProviderProfileEditView - lines 379-509)
- accounts_app/views/provider/account.py (service_provider_change_password_view, service_provider_deactivate_account_view, premium_upgrade - lines 510-600, 714-744)

Requirements:
- Maintain all existing functionality, URL patterns, and imports
- Keep all decorators, custom methods, form handling, validation logic, and email verification
- Preserve all docstrings, comments, logging, and performance monitoring
- Ensure backward compatibility by importing all views in __init__.py
- Maintain all template references and success URLs
```

## Task 4: Update Import Statements
**Prompt for AI:**
```
Update all import statements throughout the project to use the new modular structure.

Search and update imports in:
- accounts_app/admin.py
- accounts_app/urls.py
- All test files in accounts_app/tests/
- Any other files importing from accounts_app.models or accounts_app.views
- External apps that import these models or views

Requirements:
- Use grep/search to find all import statements that need updating
- Update imports to use the new modular structure
- Ensure all functionality continues to work
- Test that no import errors occur
- Maintain backward compatibility where possible

Example: Change `from accounts_app.models import CustomUser` to `from accounts_app.models.user import CustomUser`
```

---

# PHASE 2: AUTHENTICATION SYSTEM

## Task 5: Install and Configure django-allauth
**Prompt for AI:**
```
Install and configure django-allauth==0.63.6 in the CozyWish Django project.

Steps:
1. Install packages: django-allauth==0.63.6
2. Update settings.py:
   - Add to INSTALLED_APPS: 'allauth', 'allauth.account', 'allauth.socialaccount', 'allauth.socialaccount.providers.google'
   - Add to AUTHENTICATION_BACKENDS: 'allauth.account.auth_backends.AuthenticationBackend'
   - Configure allauth settings:
     * ACCOUNT_EMAIL_REQUIRED = True
     * ACCOUNT_USERNAME_REQUIRED = False
     * ACCOUNT_AUTHENTICATION_METHOD = 'email'
     * ACCOUNT_EMAIL_VERIFICATION = 'mandatory'
     * ACCOUNT_UNIQUE_EMAIL = True
     * ACCOUNT_EMAIL_SUBJECT_PREFIX = '[CozyWish] '
     * ACCOUNT_DEFAULT_HTTP_PROTOCOL = 'https'
     * ACCOUNT_LOGIN_ATTEMPTS_LIMIT = 5
     * ACCOUNT_LOGIN_ATTEMPTS_TIMEOUT = 300
     * ACCOUNT_LOGOUT_REDIRECT_URL = '/'
     * ACCOUNT_LOGIN_REDIRECT_URL = '/dashboard/'
     * ACCOUNT_SIGNUP_REDIRECT_URL = '/dashboard/'

3. Update main urls.py to include: path('accounts/', include('allauth.urls'))

Requirements:
- Ensure compatibility with existing AUTH_USER_MODEL = 'accounts_app.CustomUser'
- Maintain existing email configuration with SendGrid
- Preserve existing security settings
```

## Task 6: Update CustomUser Model
**Prompt for AI:**
```
Update the CustomUser model to be fully compatible with django-allauth while maintaining existing role-based functionality.

Current CustomUser model has:
- username = None (uses email as username)
- email field as unique identifier
- role field with choices: customer, service_provider, admin
- role properties: is_customer, is_service_provider, is_admin
- CustomUserManager with create_user method

Requirements:
- Ensure model inherits from AbstractUser correctly
- Keep existing role field and role-based properties
- Add any required fields/methods that allauth expects
- Ensure email field is properly configured as USERNAME_FIELD
- Maintain backward compatibility with existing code
- Add any missing methods that allauth might need
- Preserve all existing functionality and relationships
```

## Task 7: Create Custom Allauth Adapters
**Prompt for AI:**
```
Create custom allauth adapters for role-based user creation in accounts_app/allauth_adapters.py.

Create these classes:
1. CozyWishAccountAdapter(DefaultAccountAdapter):
   - Override save_user method to handle role assignment
   - Implement custom logic for customer vs service provider signup
   - Handle profile creation (CustomerProfile, ServiceProviderProfile)
   - Maintain existing business logic for email verification
   - Add custom validation for role-based signup

2. CozyWishSocialAccountAdapter(DefaultSocialAccountAdapter):
   - Override populate_user method to extract user data from social providers
   - Handle role assignment for social signups
   - Implement custom logic for profile creation from social data
   - Add validation for social account role assignment

Requirements:
- Work with existing CustomUser model
- Handle both regular and social authentication
- Maintain existing role-based access control
- Preserve all existing business logic
- Support existing profile models
- Handle email verification workflow
- Maintain security and validation requirements

Update settings.py to use these adapters:
- ACCOUNT_ADAPTER = 'accounts_app.allauth_adapters.CozyWishAccountAdapter'
- SOCIALACCOUNT_ADAPTER = 'accounts_app.allauth_adapters.CozyWishSocialAccountAdapter'
```

## Task 8: Create Custom Allauth Forms
**Prompt for AI:**
```
Create custom allauth forms in accounts_app/allauth_forms.py that replace existing forms while maintaining all functionality.

Create these forms:
1. CozyWishSignupForm(allauth.account.forms.SignupForm):
   - Add role selection field (customer/service_provider)
   - Include all fields from existing CustomerSignupForm
   - Add business-specific fields for service providers
   - Maintain existing validation logic
   - Handle profile creation in save method

2. CozyWishSocialSignupForm(allauth.socialaccount.forms.SignupForm):
   - Add role selection for social signups
   - Handle profile creation for social users
   - Maintain existing validation and styling

3. CozyWishLoginForm(allauth.account.forms.LoginForm):
   - Maintain existing login validation
   - Keep role-based access control
   - Preserve existing styling and accessibility

Requirements:
- Maintain all existing form validation logic
- Keep existing field styling and accessibility features
- Preserve role-based access control
- Handle profile creation for both customer and service provider roles
- Maintain existing error handling and user feedback
- Keep existing form field widgets and attributes
- Preserve existing help text and labels
- Maintain existing phone number normalization
- Keep existing terms of service agreement handling
- Handle business-specific fields for service providers

Update settings.py:
- ACCOUNT_FORMS = {'signup': 'accounts_app.allauth_forms.CozyWishSignupForm'}
```

## Task 9: Update Allauth Templates
**Prompt for AI:**
```
Create custom allauth templates in templates/allauth/ directory that match CozyWish design system.

Create these templates:
1. Account templates:
   - templates/allauth/account/login.html
   - templates/allauth/account/signup.html
   - templates/allauth/account/email_confirm.html
   - templates/allauth/account/password_change.html
   - templates/allauth/account/password_reset.html
   - templates/allauth/account/password_reset_done.html
   - templates/allauth/account/password_reset_from_key.html
   - templates/allauth/account/password_reset_from_key_done.html
   - templates/allauth/account/logout.html

2. Social account templates:
   - templates/allauth/socialaccount/login.html
   - templates/allauth/socialaccount/signup.html
   - templates/allauth/socialaccount/connections.html

3. Email templates:
   - templates/allauth/account/email/email_confirmation_message.txt
   - templates/allauth/account/email/email_confirmation_subject.txt
   - templates/allauth/account/email/password_reset_key_message.txt
   - templates/allauth/account/email/password_reset_key_subject.txt

Requirements:
- Use existing CozyWish design system (CSS variables, fonts, colors)
- Match styling of existing authentication templates
- Include existing form styling classes
- Use existing button styles and layouts
- Maintain accessibility features
- Include proper error handling and validation display
- Use existing navigation and footer components
- Match existing responsive design
- Include proper CSRF protection
- Use existing message display system
- Maintain existing branding and logo placement
```

## Task 10: Configure Google OAuth2
**Prompt for AI:**
```
Configure Google OAuth2 integration for CozyWish project with django-allauth.

Steps:
1. Update settings.py with Google provider configuration:

SOCIALACCOUNT_PROVIDERS = {
    'google': {
        'SCOPE': [
            'profile',
            'email',
        ],
        'AUTH_PARAMS': {
            'access_type': 'online',
        },
        'OAUTH_PKCE_ENABLED': True,
    }
}

2. Add environment variables to .env:
   - GOOGLE_OAUTH2_CLIENT_ID=your_client_id
   - GOOGLE_OAUTH2_CLIENT_SECRET=your_client_secret

3. Update settings.py to use environment variables:

SOCIALACCOUNT_PROVIDERS['google']['APP'] = {
    'client_id': env('GOOGLE_OAUTH2_CLIENT_ID'),
    'secret': env('GOOGLE_OAUTH2_CLIENT_SECRET'),
    'key': ''
}

4. Configure additional settings:
   - SOCIALACCOUNT_AUTO_SIGNUP = False
   - SOCIALACCOUNT_EMAIL_REQUIRED = True
   - SOCIALACCOUNT_EMAIL_VERIFICATION = 'mandatory'

Requirements:
- Work seamlessly with existing CustomUser model
- Handle role assignment for social users
- Maintain existing profile creation logic
- Preserve existing email verification workflow
- Handle proper error cases and edge scenarios
- Maintain security best practices
- Support both development and production environments

Note: Manual setup of Google Cloud Console credentials will be done separately.
```

## Task 11: Update URL Configuration
**Prompt for AI:**
```
Update URL configuration to work with django-allauth while maintaining backward compatibility.

Steps:
1. Update main project urls.py:
   - Add: path('accounts/', include('allauth.urls'))
   - Ensure proper URL precedence (allauth vs custom URLs)
   - Handle URL conflicts between allauth and existing auth URLs

2. Update accounts_app/urls.py:
   - Create custom URL patterns for role-based signup flows
   - Handle role-based redirects after authentication
   - Maintain existing URL structure where possible
   - Set up proper URL namespacing

3. Create custom views for role-based flows:
   - Customer signup view that pre-sets role
   - Service provider signup view that pre-sets role
   - Custom redirect views based on user role

Requirements:
- Ensure existing URLs still work
- Maintain existing view functionality
- Preserve existing form handling
- Keep existing error handling
- Maintain existing user feedback
- Preserve existing security measures
- Handle proper role-based access control
- Preserve existing logging and monitoring
- Support both allauth and custom authentication flows
- Handle proper redirects and URL routing

Example URL patterns:
- /accounts/customer/signup/ -> Customer signup with role pre-set
- /accounts/provider/signup/ -> Provider signup with role pre-set
- /accounts/login/ -> Unified login for all user types
```

## Task 12: Implement Crispy Forms
**Prompt for AI:**
```
Upgrade to crispy-bootstrap5==2024.2 and modernize form rendering throughout the accounts app.

Steps:
1. Install packages:
   - crispy-bootstrap5==2024.2
   - django-crispy-forms==2.1

2. Update settings.py:
   - Add 'crispy_forms' and 'crispy_bootstrap5' to INSTALLED_APPS
   - Set CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
   - Set CRISPY_TEMPLATE_PACK = "bootstrap5"

3. Update all form templates:
   - Replace manual form rendering with {% crispy form %}
   - Create custom crispy form layouts for better UX
   - Update form widgets to use crispy forms styling consistently
   - Implement form validation messages using crispy forms error handling

4. Create custom crispy form layouts:
   - Create layouts for signup forms with role selection
   - Create layouts for profile forms with proper grouping
   - Create layouts for password change forms
   - Create layouts for login forms with social login buttons

Requirements:
- Maintain existing form styling and accessibility
- Preserve existing form validation logic
- Keep existing error handling and user feedback
- Maintain existing form field widgets and attributes
- Preserve existing help text and labels
- Update all authentication forms to use crispy forms
- Create consistent form styling across all user types
- Maintain responsive design for all device sizes
```

---

# PHASE 3: ENHANCEMENT & TESTING

## Task 13: Template Refactoring
**Prompt for AI:**
```
Break down large template files into smaller, reusable components using template includes and modern design patterns.

Steps:
1. Create base templates:
   - templates/accounts/base_auth.html (base for all authentication pages)
   - templates/accounts/components/form_field.html (reusable form field component)
   - templates/accounts/components/social_login.html (social login buttons)
   - templates/accounts/components/auth_header.html (authentication page header)
   - templates/accounts/components/auth_footer.html (authentication page footer)

2. Extract common components:
   - Form validation error display
   - Success message display
   - Loading states and spinners
   - Social login button groups
   - Form field groups with labels and help text

3. Implement proper template inheritance:
   - Create consistent layout hierarchy
   - Use template blocks for customization
   - Implement responsive design patterns
   - Add proper meta tags and SEO elements

Requirements:
- Maintain existing CozyWish design system
- Preserve existing styling and accessibility
- Keep existing responsive design
- Maintain existing branding and logo placement
- Use existing CSS variables and design tokens
- Preserve existing navigation and footer components
- Maintain existing color scheme and typography
- Keep existing form styling and validation display
- Implement proper template caching for performance
```

## Task 14: Form System Modernization
**Prompt for AI:**
```
Implement modern form patterns with client-side validation and enhanced user experience.

Steps:
1. Add client-side validation:
   - Real-time email validation
   - Password strength indicator
   - Phone number formatting
   - Form field validation on blur
   - Submit button state management

2. Create form mixins:
   - Common validation mixin for all user types
   - Role-based form mixin for different user roles
   - Profile completion mixin for new users
   - Security validation mixin for sensitive operations

3. Implement modern UX patterns:
   - Progressive form disclosure
   - Smart form defaults
   - Auto-save for long forms
   - Form step indicators
   - Contextual help and tooltips

4. Add JavaScript enhancements:
   - Form validation without page reload
   - Dynamic field visibility based on selections
   - Auto-complete for common fields
   - File upload with progress indicators
   - Form submission with loading states

Requirements:
- Maintain existing server-side validation
- Preserve existing form styling and accessibility
- Keep existing error handling and user feedback
- Maintain existing security measures
- Use modern JavaScript (ES6+) without jQuery
- Implement proper CSRF protection
- Maintain existing form field widgets and attributes
- Preserve existing help text and labels
- Keep existing phone number normalization
- Maintain existing terms of service agreement handling
```

## Task 15: Authentication Flow Enhancement
**Prompt for AI:**
```
Implement enhanced authentication flows using allauth with improved user experience.

Steps:
1. Email verification workflow:
   - Custom email verification templates
   - Resend verification email functionality
   - Email verification status tracking
   - Custom verification success/error pages

2. Account recovery flows:
   - Enhanced password reset with security questions
   - Account recovery via email
   - Account lockout and unlock procedures
   - Security notification emails

3. Session management:
   - Remember me functionality
   - Session timeout warnings
   - Multiple device session management
   - Secure session handling

4. Social account linking:
   - Link/unlink social accounts for existing users
   - Social account management page
   - Conflict resolution for duplicate emails
   - Social account data synchronization

Requirements:
- Maintain existing security standards
- Preserve existing email configuration
- Keep existing user feedback and messaging
- Maintain existing logging and monitoring
- Implement proper rate limiting
- Handle edge cases and error scenarios
- Maintain existing business logic
- Preserve existing template styling
- Keep existing accessibility features
- Implement proper CSRF protection
```

## Task 16: User Profile Management
**Prompt for AI:**
```
Update profile models and implement modern profile management workflows.

Steps:
1. Profile model enhancements:
   - Add profile completion tracking
   - Implement profile picture upload with validation
   - Add profile privacy settings
   - Create profile data export functionality

2. Profile completion workflows:
   - Progressive profile completion for new users
   - Profile completion progress indicators
   - Smart suggestions for incomplete profiles
   - Profile completion rewards/incentives

3. Profile editing enhancements:
   - Real-time profile preview
   - Bulk profile data import/export
   - Profile change history tracking
   - Profile verification status

4. Privacy and data management:
   - Profile visibility settings
   - Data export in multiple formats
   - Account deletion with data cleanup
   - GDPR compliance features

Requirements:
- Maintain existing profile model relationships
- Preserve existing profile functionality
- Keep existing validation and security measures
- Maintain existing template styling
- Preserve existing accessibility features
- Keep existing form handling and error display
- Maintain existing file upload security
- Preserve existing image processing logic
- Keep existing profile completion logic
- Maintain existing business rules and constraints
```

## Task 17: Security and Validation
**Prompt for AI:**
```
Implement comprehensive security measures and validation throughout the authentication system.

Steps:
1. CSRF protection:
   - Ensure CSRF tokens in all forms
   - Implement CSRF failure handling
   - Add CSRF protection to AJAX requests
   - Configure proper CSRF settings

2. Rate limiting:
   - Implement login attempt rate limiting
   - Add signup rate limiting
   - Configure password reset rate limiting
   - Add email verification rate limiting

3. Input validation and sanitization:
   - Comprehensive input validation for all forms
   - XSS protection for user-generated content
   - SQL injection prevention
   - File upload security validation

4. Password security:
   - Strong password requirements
   - Password history tracking
   - Password breach checking
   - Secure password storage validation

5. Account security:
   - Account lockout for failed attempts
   - Suspicious activity detection
   - Security notification emails
   - Two-factor authentication preparation

Requirements:
- Maintain existing security configurations
- Preserve existing validation logic
- Keep existing error handling
- Maintain existing logging and monitoring
- Implement proper security headers
- Use secure session configurations
- Maintain existing HTTPS enforcement
- Preserve existing email security
- Keep existing database security measures
- Implement proper audit logging
```

## Task 18: Testing and Quality Assurance
**Prompt for AI:**
```
Write comprehensive tests for allauth integration and authentication flows.

Steps:
1. Unit tests:
   - Test custom allauth adapters
   - Test custom forms with allauth integration
   - Test model methods and properties
   - Test custom validators and utilities

2. Integration tests:
   - Test complete user registration flows
   - Test login/logout functionality
   - Test password reset workflows
   - Test email verification processes
   - Test social authentication flows

3. Security tests:
   - Test authentication security measures
   - Verify CSRF protection
   - Test session security
   - Verify password security
   - Test rate limiting functionality

4. Performance tests:
   - Test authentication endpoint performance
   - Test database query optimization
   - Test template rendering performance
   - Test email sending performance

5. Accessibility tests:
   - Test form accessibility
   - Test keyboard navigation
   - Test screen reader compatibility
   - Test color contrast and visibility

Requirements:
- Achieve 90%+ test coverage for authentication code
- Test all user roles and permissions
- Test error handling and edge cases
- Test both successful and failed scenarios
- Test with different browsers and devices
- Test email functionality with test backends
- Test social authentication with mock providers
- Test database migrations and data integrity
- Test security measures and validation
- Test performance under load
```

## Task 19: Documentation and Maintenance
**Prompt for AI:**
```
Create comprehensive documentation and maintenance tools for the new authentication system.

Steps:
1. Technical documentation:
   - API documentation for custom adapters and forms
   - Configuration guide for allauth settings
   - Deployment guide for production environments
   - Troubleshooting guide for common issues

2. User documentation:
   - User guide for account registration and login
   - Guide for Google OAuth login
   - Account management and profile completion guide
   - Privacy and security settings guide

3. Developer documentation:
   - Code architecture and design patterns
   - Testing strategy and test execution guide
   - Contributing guidelines for authentication features
   - Security best practices and guidelines

4. Maintenance tools:
   - User data management scripts
   - Account cleanup and maintenance scripts
   - Performance monitoring and alerting
   - Security audit and compliance checking

5. Monitoring and logging:
   - Authentication event logging
   - Performance monitoring dashboards
   - Security incident tracking
   - User behavior analytics

Requirements:
- Create clear, comprehensive documentation
- Include code examples and usage patterns
- Provide troubleshooting guides and FAQs
- Create maintenance scripts for common tasks
- Implement proper logging and monitoring
- Include security best practices
- Provide deployment and configuration guides
- Create user-friendly guides for end users
- Include performance optimization tips
- Provide backup and recovery procedures
```

---

## Summary

This organized task list provides a clear roadmap for modernizing the CozyWish accounts app in three phases:

1. **Phase 1 (Code Organization)**: Restructure existing code for better maintainability
2. **Phase 2 (Authentication System)**: Implement modern authentication with django-allauth and social login
3. **Phase 3 (Enhancement & Testing)**: Enhance user experience, security, and implement comprehensive testing

Each task includes detailed, AI-friendly prompts that specify exactly what needs to be done, what packages to use, and what requirements to maintain. The tasks are designed to be implemented sequentially within each phase, with clear dependencies and backward compatibility requirements.
