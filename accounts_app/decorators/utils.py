"""
Utility decorators for accounts_app.

This module provides utility decorators for common view patterns
like AJAX requirements, rate limiting, caching, etc.
"""

from functools import wraps
from django.http import HttpResponseForbidden, JsonResponse
from django.core.exceptions import PermissionDenied
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_control as django_cache_control
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
import json
import time
from collections import defaultdict

# Simple in-memory rate limiting (for testing purposes)
_rate_limit_storage = defaultdict(list)


def email_verified_required(view_func):
    """
    Decorator to require that the user's email is verified.
    
    Usage:
        @email_verified_required
        def my_view(request):
            ...
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return HttpResponseForbidden(str(_('Authentication required.')))
        
        if not request.user.email_verified:
            messages.error(request, _('Please verify your email address to access this feature.'))
            return HttpResponseForbidden(str(_('Email verification required.')))
        
        return view_func(request, *args, **kwargs)
    return wrapper


def profile_complete_required(view_func):
    """
    Decorator to require that the user's profile is complete.
    
    Usage:
        @profile_complete_required
        def my_view(request):
            ...
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return HttpResponseForbidden(_('Authentication required.'))
        
        # Check if profile exists and is complete
        if hasattr(request.user, 'customer_profile'):
            profile = request.user.customer_profile
            if not profile.first_name or not profile.last_name:
                messages.warning(request, _('Please complete your profile to access this feature.'))
                return HttpResponseForbidden(str(_('Profile completion required.')))
        
        return view_func(request, *args, **kwargs)
    return wrapper


def rate_limit(limit=60, period=60, by_ip=False):
    """
    Decorator to limit the rate of requests.
    
    Args:
        limit: Maximum number of requests allowed
        period: Time period in seconds
        by_ip: Whether to limit by IP address instead of user
    
    Usage:
        @rate_limit(limit=10, period=60)
        def my_view(request):
            ...
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Get identifier (user ID or IP)
            if by_ip or not request.user.is_authenticated:
                identifier = request.META.get('REMOTE_ADDR', 'unknown')
            else:
                identifier = str(request.user.id)
            
            # Get current timestamp
            now = time.time()
            
            # Clean old entries
            _rate_limit_storage[identifier] = [
                timestamp for timestamp in _rate_limit_storage[identifier]
                if now - timestamp < period
            ]
            
            # Check if limit exceeded
            if len(_rate_limit_storage[identifier]) >= limit:
                return HttpResponseForbidden(str(_('Rate limit exceeded. Please try again later.')))
            
            # Add current request
            _rate_limit_storage[identifier].append(now)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def ajax_required(view_func):
    """
    Decorator to require that the request is an AJAX request.
    
    Usage:
        @ajax_required
        def my_view(request):
            ...
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return HttpResponseForbidden(_('AJAX request required.'))
        
        return view_func(request, *args, **kwargs)
    return wrapper


def json_response(view_func):
    """
    Decorator to automatically wrap the response in JSON.
    
    Usage:
        @json_response
        def my_view(request):
            return {'status': 'success'}
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        try:
            result = view_func(request, *args, **kwargs)
            
            # If result is already a response, return it
            if hasattr(result, 'status_code'):
                return result
            
            # Otherwise, wrap in JSON response
            return JsonResponse(result, safe=False)
            
        except Exception as e:
            return JsonResponse({
                'error': str(e),
                'status': 'error'
            }, status=500)
    
    return wrapper


def cache_control(**kwargs):
    """
    Decorator to set cache control headers.
    
    Usage:
        @cache_control(max_age=3600, private=True)
        def my_view(request):
            ...
    """
    return django_cache_control(**kwargs)


def permission_required_custom(permission_func):
    """
    Decorator to require custom permission check.
    
    Args:
        permission_func: Function that takes request and returns bool
    
    Usage:
        @permission_required_custom(lambda r: r.user.is_superuser)
        def my_view(request):
            ...
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not permission_func(request):
                raise PermissionDenied(_('Permission denied.'))
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def two_factor_required(view_func):
    """
    Decorator to require two-factor authentication.
    
    Usage:
        @two_factor_required
        def my_view(request):
            ...
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return HttpResponseForbidden(_('Authentication required.'))
        
        # For now, just check if 2FA is enabled (placeholder)
        # In a real implementation, you'd check if 2FA is verified
        if hasattr(request.user, 'two_factor_enabled') and request.user.two_factor_enabled:
            # Check if 2FA is verified for this session
            if not request.session.get('2fa_verified', False):
                messages.error(request, _('Two-factor authentication required.'))
                return HttpResponseForbidden(_('Two-factor authentication required.'))
        
        return view_func(request, *args, **kwargs)
    return wrapper 