# --- Settings Module Loader ---
"""
Django settings for CozyWish project.

This module automatically loads the appropriate settings based on the environment.
"""

import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# Environment detection
ENVIRONMENT = os.environ.get('DJANGO_ENVIRONMENT', 'development').lower()

if ENVIRONMENT == 'production':
    from .production import *
elif ENVIRONMENT == 'testing':
    from .testing import *
else:
    from .development import * 