// CozyWish Password Toggle Functionality
(function() {
    'use strict';

    // Password toggle functionality - using event delegation (immediate setup)
    document.addEventListener('click', function(e) {
        // Check if clicked element is a toggle password button or its child
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn) {
            // Prevent any default behavior and stop propagation
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();

            const targetSelector = toggleBtn.getAttribute('data-target');
            if (!targetSelector) return;

            const input = document.querySelector(targetSelector);
            if (!input) return;

            // Toggle password visibility
            const isPassword = input.type === 'password';
            input.type = isPassword ? 'text' : 'password';

            // Update icon
            const icon = toggleBtn.querySelector('i');
            if (icon) {
                if (isPassword) {
                    // Showing password - change to eye-slash
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    // Hiding password - change to eye
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            }

            // Update accessibility attributes
            const newTitle = isPassword ? 'Hide password' : 'Show password';
            toggleBtn.title = newTitle;
            toggleBtn.setAttribute('aria-label', newTitle);

            return false;
        }
    }, true); // Use capture phase to ensure we get the event first

    // Add keyboard navigation support for accessibility
    document.addEventListener('keydown', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn && (e.key === 'Enter' || e.key === ' ' || e.key === 'Space')) {
            e.preventDefault();
            e.stopPropagation();
            
            // Trigger the same toggle functionality as click
            const targetSelector = toggleBtn.getAttribute('data-target');
            if (!targetSelector) return;

            const input = document.querySelector(targetSelector);
            if (!input) return;

            // Toggle password visibility
            const isPassword = input.type === 'password';
            input.type = isPassword ? 'text' : 'password';

            // Update icon
            const icon = toggleBtn.querySelector('i');
            if (icon) {
                if (isPassword) {
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            }

            // Update accessibility attributes
            const newTitle = isPassword ? 'Hide password' : 'Show password';
            toggleBtn.title = newTitle;
            toggleBtn.setAttribute('aria-label', newTitle);

            return false;
        }
    });

    function initializePasswordToggles() {
        // Enhanced accessibility and functionality for form fields
        const errorFields = document.querySelectorAll('.form-control, .form-select');
        errorFields.forEach(field => {
            const errorDiv = field.parentElement.querySelector('.invalid-feedback') ||
                            field.closest('.form-floating, .mb-3, .col-md-6, .col-12').querySelector('.invalid-feedback');
            if (errorDiv && errorDiv.textContent.trim()) {
                field.setAttribute('aria-invalid', 'true');
                field.classList.add('is-invalid');

                // Link field to error message
                if (!errorDiv.id) {
                    errorDiv.id = field.id + '_error';
                }
                const describedBy = field.getAttribute('aria-describedby') || '';
                if (!describedBy.includes(errorDiv.id)) {
                    field.setAttribute('aria-describedby', (describedBy + ' ' + errorDiv.id).trim());
                }
            }
        });

        // Add aria-describedby for help text
        const helpTexts = document.querySelectorAll('.form-text');
        helpTexts.forEach(helpText => {
            if (!helpText.id) {
                const field = helpText.closest('.form-floating, .mb-3, .col-md-6, .col-12').querySelector('.form-control, .form-select');
                if (field) {
                    helpText.id = field.id + '_help';
                    const describedBy = field.getAttribute('aria-describedby') || '';
                    if (!describedBy.includes(helpText.id)) {
                        field.setAttribute('aria-describedby', (describedBy + ' ' + helpText.id).trim());
                    }
                }
            }
        });

        // Initialize accessibility attributes and direct event listeners for all toggle buttons
        const toggleButtons = document.querySelectorAll('.toggle-password');

        toggleButtons.forEach(function(btn) {
            btn.setAttribute('type', 'button');
            btn.setAttribute('tabindex', '0');
            btn.title = btn.title || 'Show password';
            btn.setAttribute('aria-label', btn.getAttribute('aria-label') || 'Show password');

            // Add direct click event listener as backup
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const targetSelector = this.getAttribute('data-target');
                if (!targetSelector) return;

                const input = document.querySelector(targetSelector);
                if (!input) return;

                // Toggle password visibility
                const isPassword = input.type === 'password';
                input.type = isPassword ? 'text' : 'password';

                // Update icon
                const icon = this.querySelector('i');
                if (icon) {
                    if (isPassword) {
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    } else {
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    }
                }

                // Update accessibility attributes
                const newTitle = isPassword ? 'Hide password' : 'Show password';
                this.title = newTitle;
                this.setAttribute('aria-label', newTitle);
            });

            // Add keyboard event listener for accessibility
            btn.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ' || e.key === 'Space') {
                    e.preventDefault();
                    this.click(); // Trigger click event
                }
            });
        });
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializePasswordToggles);
    } else {
        // DOM is already ready
        initializePasswordToggles();
    }

    // Also initialize after a short delay to handle dynamic content
    setTimeout(initializePasswordToggles, 100);
})(); 