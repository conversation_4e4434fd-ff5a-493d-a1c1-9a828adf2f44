# Venue Edit Wizard

## Overview

The Venue Edit Wizard is the **primary and only way** to edit existing venues, providing a step-by-step interface similar to the venue creation wizard. This ensures a consistent experience for service providers when managing their venue information.

## Features

### Multi-Step Process
The wizard is divided into 5 steps:

1. **Basic Information** - Venue name, description, and categories
2. **Location & Contact** - Address, phone, email, website, and social media
3. **Services & Pricing** - Service management (redirects to services page)
4. **Gallery & Images** - Image management (redirects to images page)
5. **Details & Policies** - Operating hours, amenities, FAQs, and policies

### Progress Tracking
- Visual progress bar showing completion percentage
- Step-by-step navigation with validation
- Auto-save functionality to preserve progress
- Session-based progress storage

### Pre-populated Data
- All existing venue information is pre-filled in the forms
- Categories, amenities, and other relationships are preserved
- Current venue status is maintained

## Usage

### Accessing the Wizard

1. **From Venue Detail Page**: Click "Edit Venue" button in the action buttons
2. **Direct URL**: Navigate to `/venues/provider/edit/wizard/`
3. **Any Edit Link**: All edit links now redirect to the wizard

### Navigation

- **Previous/Next**: Use the navigation buttons to move between steps
- **Step Validation**: Each step validates required fields before allowing progression
- **Auto-save**: Progress is automatically saved as you fill out forms
- **Cancel**: Click "Cancel" to return to venue details without saving

### Step Details

#### Step 1: Basic Information
- Venue name (required)
- Short description (required)
- Categories (required, up to 3)

#### Step 2: Location & Contact
- Complete address information
- Phone number
- Email address
- Website URL
- Social media links (Instagram, Facebook, Twitter, LinkedIn)

#### Step 3: Services & Pricing
- Redirects to the main services management page
- Allows adding, editing, and managing venue services

#### Step 4: Gallery & Images
- Redirects to the main image management page
- Allows uploading, reordering, and managing venue images

#### Step 5: Details & Policies
- Cancellation policy
- Booking policy
- Special instructions
- Venue status (draft/pending approval)

## Technical Implementation

### Files Modified/Created

1. **Forms** (`venues_app/forms/venue.py`):
   - `VenueEditWizardMixin` - Base mixin for edit wizard forms
   - `VenueEditBasicInfoForm` - Step 1 form
   - `VenueEditLocationForm` - Step 2 form
   - `VenueEditServicesForm` - Step 3 form
   - `VenueEditGalleryForm` - Step 4 form
   - `VenueEditDetailsForm` - Step 5 form

2. **Views** (`venues_app/views/provider.py`):
   - `VenueEditWizardView` - Main wizard class
   - `venue_edit_wizard_view` - View function
   - `handle_edit_wizard_ajax` - AJAX handler
   - `update_venue_from_wizard` - Venue update logic

3. **URLs** (`venues_app/urls.py`):
   - Added wizard URL patterns

4. **Templates**:
   - `templates/venues_app/venue_edit_wizard.html` - Main wizard template
   - Updated existing templates to include wizard links

### Key Features

#### Session Management
- Progress data stored in Django session
- Unique session key per venue: `venue_edit_wizard_{venue_id}`
- Auto-cleanup on successful completion

#### Form Pre-population
- All existing venue data is loaded into form initial values
- Relationships (categories, amenities, FAQs) are properly handled
- JSON fields for complex data (services, images, operating hours)

#### Validation
- Step-by-step validation
- Required field checking
- Business logic validation
- Error handling and user feedback

#### AJAX Support
- Real-time progress saving
- Auto-save on form changes
- Progress bar updates

## Benefits

1. **Consistent Experience**: Same interface as venue creation wizard
2. **Guided Process**: Step-by-step editing prevents confusion
3. **Data Integrity**: Validation at each step ensures complete updates
4. **Progress Preservation**: Users can save progress and return later
5. **Completeness**: Ensures all venue information is properly updated
6. **Simplified Workflow**: Single editing method eliminates confusion

## Future Enhancements

1. **Image Upload**: Direct image upload in wizard (currently redirects)
2. **Service Management**: Inline service editing (currently redirects)
3. **Preview Mode**: Live preview of changes
4. **Bulk Operations**: Batch updates for multiple fields
5. **Change Tracking**: Detailed change history and comparison

## Troubleshooting

### Common Issues

1. **Session Data Lost**: Progress is stored in session, clearing browser data will reset progress
2. **Validation Errors**: Each step validates required fields before allowing progression
3. **Redirect Loops**: Ensure all required fields are completed in previous steps

### Debug Information

- Check Django logs for detailed error information
- Verify venue permissions and ownership
- Ensure all required models and relationships exist

## Testing

To test the venue edit wizard:

1. Create a venue using the creation wizard
2. Navigate to the venue detail page
3. Click "Edit Wizard" button
4. Go through each step and verify data is pre-populated
5. Make changes and verify they are saved correctly
6. Test validation by leaving required fields empty
7. Test navigation between steps
8. Verify final venue update works correctly 