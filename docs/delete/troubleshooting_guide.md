# CozyWish Troubleshooting Guide

## Table of Contents
1. [Authentication Issues](#authentication-issues)
2. [Profile Management Problems](#profile-management-problems)
3. [Email and Verification Issues](#email-and-verification-issues)
4. [Database Problems](#database-problems)
5. [Static Files and Media Issues](#static-files-and-media-issues)
6. [Deployment Issues](#deployment-issues)
7. [Performance Problems](#performance-problems)
8. [Security Issues](#security-issues)
9. [Form Validation Errors](#form-validation-errors)
10. [Development Environment Issues](#development-environment-issues)

## Authentication Issues

### Login Problems

#### Issue: "Invalid email or password" Error
**Symptoms:**
- User cannot log in with correct credentials
- Error message appears on login form
- Account exists but authentication fails

**Possible Causes:**
1. Incorrect email format or case sensitivity
2. Password changes not reflected
3. Account lockout due to failed attempts
4. Database synchronization issues

**Solutions:**
```bash
# Check user exists in database
python manage.py shell
>>> from accounts_app.models import CustomUser
>>> user = CustomUser.objects.get(email='<EMAIL>')
>>> print(user.is_active, user.status)

# Reset password
python manage.<NAME_EMAIL>

# Check account lockout
>>> from accounts_app.models import AccountLockout
>>> lockouts = AccountLockout.objects.filter(user=user)
>>> for lockout in lockouts:
>>>     print(lockout.is_locked, lockout.locked_until)

# Reset failed attempts
>>> AccountLockout.reset_failed_attempts('user-ip-address')
```

#### Issue: Account Lockout
**Symptoms:**
- "Account is temporarily locked" message
- User cannot log in after failed attempts
- IP address is blocked

**Solutions:**
```bash
# Check lockout status
python manage.py shell
>>> from accounts_app.models import AccountLockout
>>> AccountLockout.objects.filter(ip_address='*************')

# Unlock account manually
>>> lockout = AccountLockout.objects.get(ip_address='*************')
>>> lockout.unlock()

# Clean up old lockout records
>>> AccountLockout.cleanup_old_records(days=1)
```

#### Issue: Role-Based Access Problems
**Symptoms:**
- User redirected to wrong dashboard
- Permission denied errors
- Incorrect role assignments

**Solutions:**
```bash
# Check user role
python manage.py shell
>>> from accounts_app.models import CustomUser
>>> user = CustomUser.objects.get(email='<EMAIL>')
>>> print(user.role, user.is_customer, user.is_service_provider)

# Update user role
>>> user.role = 'customer'  # or 'service_provider'
>>> user.save()

# Verify role-based redirects
>>> from django.urls import reverse
>>> print(reverse('accounts_app:customer_profile'))
```

### Registration Issues

#### Issue: Email Already Exists
**Symptoms:**
- Registration form shows "A user with this email already exists"
- User cannot complete registration
- Duplicate email error

**Solutions:**
```bash
# Check for existing users
python manage.py shell
>>> from accounts_app.models import CustomUser
>>> existing = CustomUser.objects.filter(email='<EMAIL>')
>>> print(existing.count())

# Delete inactive duplicate accounts
>>> inactive_users = CustomUser.objects.filter(email='<EMAIL>', is_active=False)
>>> inactive_users.delete()

# Reactivate existing account
>>> user = CustomUser.objects.get(email='<EMAIL>')
>>> user.activate()
```

#### Issue: Password Validation Errors
**Symptoms:**
- Registration fails with password requirements
- Password strength errors
- Password history conflicts

**Solutions:**
```bash
# Check password validators
python manage.py shell
>>> from django.contrib.auth.password_validation import validate_password
>>> from accounts_app.models import CustomUser
>>> user = CustomUser(email='<EMAIL>')
>>> validate_password('newpassword', user)

# Clear password history
>>> from accounts_app.models import PasswordHistory
>>> PasswordHistory.cleanup_old_passwords(user, keep_count=0)
```

## Profile Management Problems

### Profile Not Found Errors

#### Issue: Profile Does Not Exist
**Symptoms:**
- "Profile not found" error
- Empty profile pages
- Profile creation failures

**Solutions:**
```bash
# Check profile existence
python manage.py shell
>>> from accounts_app.models import CustomUser, CustomerProfile, ServiceProviderProfile
>>> user = CustomUser.objects.get(email='<EMAIL>')
>>> 
>>> # For customers
>>> profile, created = CustomerProfile.objects.get_or_create(user=user)
>>> 
>>> # For service providers
>>> profile, created = ServiceProviderProfile.objects.get_or_create(user=user)
```

#### Issue: Profile Completion Stuck
**Symptoms:**
- Profile completion percentage not updating
- Missing fields not recognized
- Profile completion suggestions incorrect

**Solutions:**
```bash
# Recalculate profile completion
python manage.py shell
>>> from accounts_app.models import CustomUser
>>> user = CustomUser.objects.get(email='<EMAIL>')
>>> completion_data = user.get_profile_completion()
>>> print(completion_data)

# Check profile completion utility
>>> from accounts_app.utils import calculate_customer_profile_completion
>>> completion = calculate_customer_profile_completion(user)
>>> print(completion['missing_fields'])
```

### Image Upload Issues

#### Issue: Profile Picture Upload Fails
**Symptoms:**
- Image upload returns errors
- Images not displaying
- File size or format errors

**Solutions:**
```bash
# Check image validation
python manage.py shell
>>> from accounts_app.utils import validate_image_file
>>> # Test image validation
>>> validate_image_file(image_file)

# Check S3 configuration
>>> from django.conf import settings
>>> print(settings.AWS_STORAGE_BUCKET_NAME)
>>> print(settings.AWS_ACCESS_KEY_ID)

# Test S3 connectivity
>>> import boto3
>>> s3 = boto3.client('s3')
>>> s3.list_buckets()
```

#### Issue: Image Processing Errors
**Symptoms:**
- Images uploaded but not processed
- Thumbnail generation fails
- Image corruption

**Solutions:**
```bash
# Check image processing
python manage.py shell
>>> from accounts_app.utils import process_profile_image, create_thumbnail
>>> # Test image processing functions
>>> processed_image = process_profile_image(image_file)
>>> thumbnail = create_thumbnail(processed_image)
```

## Email and Verification Issues

### Email Not Sending

#### Issue: Verification Emails Not Delivered
**Symptoms:**
- Users don't receive verification emails
- Email backend errors
- SMTP connection failures

**Solutions:**
```bash
# Test email configuration
python manage.py shell
>>> from django.core.mail import send_mail
>>> send_mail('Test', 'Test message', '<EMAIL>', ['<EMAIL>'])

# Check email backend
>>> from django.conf import settings
>>> print(settings.EMAIL_BACKEND)
>>> print(settings.EMAIL_HOST_PASSWORD)

# Test SendGrid configuration
>>> import sendgrid
>>> sg = sendgrid.SendGridAPIClient(api_key=settings.EMAIL_HOST_PASSWORD)
>>> print(sg.client.mail.send.post)
```

#### Issue: Email Verification Tokens Invalid
**Symptoms:**
- "Invalid verification token" error
- Expired token messages
- Token not found errors

**Solutions:**
```bash
# Check token validity
python manage.py shell
>>> from accounts_app.models import EmailVerificationToken
>>> token = EmailVerificationToken.objects.get(token='token-string')
>>> print(token.is_valid, token.is_expired)

# Generate new token
>>> from accounts_app.models import CustomUser
>>> user = CustomUser.objects.get(email='<EMAIL>')
>>> new_token = EmailVerificationToken.generate_token(user)
>>> print(new_token.token)

# Clean up expired tokens
>>> EmailVerificationToken.cleanup_expired_tokens()
```

### Email Preferences Issues

#### Issue: Email Preferences Not Saving
**Symptoms:**
- Preference changes not persisting
- Users still receiving unwanted emails
- Unsubscribe not working

**Solutions:**
```bash
# Check email preferences
python manage.py shell
>>> from accounts_app.models import CustomUser, EmailPreferences
>>> user = CustomUser.objects.get(email='<EMAIL>')
>>> prefs, created = EmailPreferences.objects.get_or_create(user=user)
>>> print(prefs.can_send_email('marketing'))

# Update preferences
>>> prefs.marketing_emails = False
>>> prefs.save()

# Bulk unsubscribe
>>> prefs.disable_all_emails()
```

## Database Problems

### Migration Issues

#### Issue: Migration Conflicts
**Symptoms:**
- Migration dependency errors
- Database schema mismatches
- "Migration already applied" errors

**Solutions:**
```bash
# Check migration status
python manage.py showmigrations

# Fake migration application
python manage.py migrate --fake accounts_app 0001

# Reset migrations (development only)
python manage.py migrate accounts_app zero
python manage.py migrate accounts_app

# Resolve conflicts
python manage.py migrate --merge
```

#### Issue: Database Connection Errors
**Symptoms:**
- "Database connection failed" errors
- Timeout errors
- Permission denied errors

**Solutions:**
```bash
# Test database connection
python manage.py check --database

# Check database URL
echo $DATABASE_URL

# Test direct database connection
psql $DATABASE_URL

# Check database permissions
python manage.py shell
>>> from django.db import connection
>>> cursor = connection.cursor()
>>> cursor.execute("SELECT version();")
>>> print(cursor.fetchone())
```

### Data Integrity Issues

#### Issue: Orphaned Records
**Symptoms:**
- Profiles without users
- Incomplete data relationships
- Foreign key constraint errors

**Solutions:**
```bash
# Find orphaned profiles
python manage.py shell
>>> from accounts_app.models import CustomerProfile, ServiceProviderProfile
>>> orphaned_customers = CustomerProfile.objects.filter(user__isnull=True)
>>> orphaned_providers = ServiceProviderProfile.objects.filter(user__isnull=True)
>>> print(orphaned_customers.count(), orphaned_providers.count())

# Clean up orphaned records
>>> orphaned_customers.delete()
>>> orphaned_providers.delete()

# Verify data integrity
>>> from django.core.management import call_command
>>> call_command('check')
```

## Static Files and Media Issues

### Static Files Not Loading

#### Issue: CSS/JS Files Not Found
**Symptoms:**
- Styling not applied
- JavaScript errors
- 404 errors for static files

**Solutions:**
```bash
# Collect static files
python manage.py collectstatic --no-input --clear

# Check static file configuration
python manage.py check --deploy

# Verify static file paths
python manage.py shell
>>> from django.conf import settings
>>> print(settings.STATIC_URL)
>>> print(settings.STATIC_ROOT)
>>> print(settings.STATICFILES_DIRS)
```

#### Issue: Media Files Not Accessible
**Symptoms:**
- Images not displaying
- File download failures
- S3 access errors

**Solutions:**
```bash
# Check media configuration
python manage.py shell
>>> from django.conf import settings
>>> print(settings.MEDIA_URL)
>>> print(settings.DEFAULT_FILE_STORAGE)

# Test S3 access
>>> import boto3
>>> s3 = boto3.client('s3')
>>> response = s3.list_objects_v2(Bucket=settings.AWS_STORAGE_BUCKET_NAME)
>>> print(response.get('Contents', []))

# Check file permissions
>>> from storages.backends.s3boto3 import S3Boto3Storage
>>> storage = S3Boto3Storage()
>>> storage.exists('test-file.jpg')
```

## Deployment Issues

### Environment Configuration

#### Issue: Environment Variables Not Loading
**Symptoms:**
- Configuration errors
- Default values used instead of environment variables
- KeyError for missing variables

**Solutions:**
```bash
# Check environment variables
env | grep -E "(SECRET_KEY|DATABASE_URL|AWS_)"

# Test environment loading
python manage.py shell
>>> from django.conf import settings
>>> print(settings.SECRET_KEY[:10])
>>> print(settings.DEBUG)

# Verify .env file
cat .env | grep -v "^#"
```

#### Issue: Secret Key Errors
**Symptoms:**
- "SECRET_KEY must be set" error
- Django configuration errors
- Security warnings

**Solutions:**
```bash
# Generate new secret key
python manage.py shell
>>> from django.core.management.utils import get_random_secret_key
>>> print(get_random_secret_key())

# Set environment variable
export SECRET_KEY="your-secret-key-here"

# Update .env file
echo "SECRET_KEY=your-secret-key-here" >> .env
```

### Server Configuration

#### Issue: Gunicorn/WSGI Errors
**Symptoms:**
- Application not starting
- Worker process errors
- Module import errors

**Solutions:**
```bash
# Test WSGI application
python manage.py check --deploy

# Test Gunicorn configuration
gunicorn --check-config project_root.wsgi:application

# Run with debug
gunicorn --bind 0.0.0.0:8000 --workers 1 --timeout 60 project_root.wsgi:application

# Check worker processes
ps aux | grep gunicorn
```

#### Issue: Nginx Configuration Problems
**Symptoms:**
- 502 Bad Gateway errors
- Static files not serving
- Proxy errors

**Solutions:**
```bash
# Test Nginx configuration
sudo nginx -t

# Check Nginx logs
sudo tail -f /var/log/nginx/error.log

# Restart Nginx
sudo systemctl restart nginx

# Check proxy configuration
curl -I http://localhost:8000
```

## Performance Problems

### Slow Database Queries

#### Issue: Database Query Performance
**Symptoms:**
- Slow page loads
- Database timeout errors
- High CPU usage

**Solutions:**
```bash
# Enable query logging
python manage.py shell
>>> from django.db import connection
>>> from django.conf import settings
>>> settings.DEBUG = True
>>> # Run problematic code
>>> print(len(connection.queries))

# Check slow queries
>>> from django.db import connection
>>> for query in connection.queries:
>>>     if float(query['time']) > 0.5:
>>>         print(query['sql'])

# Optimize queries
>>> from accounts_app.models import CustomUser
>>> users = CustomUser.objects.select_related('customer_profile').all()
```

#### Issue: Memory Usage Problems
**Symptoms:**
- Out of memory errors
- Slow response times
- Server crashes

**Solutions:**
```bash
# Monitor memory usage
free -h
ps aux --sort=-%mem | head

# Check Django memory usage
python manage.py shell
>>> import psutil
>>> process = psutil.Process()
>>> print(process.memory_info())

# Optimize querysets
>>> from accounts_app.models import CustomUser
>>> users = CustomUser.objects.only('email', 'role')
>>> users = users.iterator(chunk_size=100)
```

## Security Issues

### Authentication Security

#### Issue: Security Alerts
**Symptoms:**
- Multiple failed login attempts
- Suspicious IP addresses
- Brute force attacks

**Solutions:**
```bash
# Check security alerts
python manage.py shell
>>> from accounts_app.models import LoginAlert
>>> alerts = LoginAlert.objects.filter(is_resolved=False)
>>> for alert in alerts:
>>>     print(alert.alert_type, alert.severity, alert.ip_address)

# Block suspicious IPs
>>> from accounts_app.models import AccountLockout
>>> AccountLockout.record_failed_attempt('suspicious-ip')

# Review login history
>>> from accounts_app.models import LoginHistory
>>> recent_logins = LoginHistory.objects.filter(is_successful=False)[:10]
```

#### Issue: Password Security
**Symptoms:**
- Weak password acceptance
- Password reuse allowed
- Security warnings

**Solutions:**
```bash
# Check password validators
python manage.py shell
>>> from django.contrib.auth.password_validation import get_default_password_validators
>>> validators = get_default_password_validators()
>>> for validator in validators:
>>>     print(validator.__class__.__name__)

# Test password strength
>>> from django.contrib.auth.password_validation import validate_password
>>> validate_password('testpassword123')

# Update password history
>>> from accounts_app.models import PasswordHistory
>>> PasswordHistory.cleanup_old_passwords(user, keep_count=5)
```

## Form Validation Errors

### Form Submission Issues

#### Issue: CSRF Token Errors
**Symptoms:**
- "CSRF token missing" errors
- Form submission failures
- Security warnings

**Solutions:**
```bash
# Check CSRF middleware
python manage.py shell
>>> from django.conf import settings
>>> print('django.middleware.csrf.CsrfViewMiddleware' in settings.MIDDLEWARE)

# Test CSRF token
>>> from django.middleware.csrf import get_token
>>> from django.test import RequestFactory
>>> factory = RequestFactory()
>>> request = factory.get('/')
>>> token = get_token(request)
>>> print(token)

# Clear CSRF issues
# Clear browser cookies
# Check CSRF_TRUSTED_ORIGINS setting
```

#### Issue: Form Field Validation
**Symptoms:**
- Custom validation not working
- Field-specific error messages
- Clean method errors

**Solutions:**
```bash
# Test form validation
python manage.py shell
>>> from accounts_app.forms import CustomerSignupForm
>>> form = CustomerSignupForm({'email': '<EMAIL>'})
>>> print(form.is_valid())
>>> print(form.errors)

# Check field validation
>>> field = form.fields['email']
>>> field.clean('invalid-email')

# Test custom validators
>>> from accounts_app.utils.validators import normalize_phone
>>> normalize_phone('******-123-4567')
```

## Development Environment Issues

### Local Development Setup

#### Issue: Virtual Environment Problems
**Symptoms:**
- Package installation failures
- Import errors
- Version conflicts

**Solutions:**
```bash
# Recreate virtual environment
deactivate
rm -rf venv
python -m venv venv
source venv/bin/activate

# Install specific versions
pip install -r requirements.txt
pip list --outdated

# Check Python version
python --version
which python
```

#### Issue: Database Setup Problems
**Symptoms:**
- Migration errors
- Database creation failures
- Permission issues

**Solutions:**
```bash
# Reset database (development only)
rm db.sqlite3
python manage.py migrate

# Create test database
python manage.py migrate --run-syncdb

# Load fixtures
python manage.py loaddata initial_data.json
```

### Common Development Errors

#### Issue: Import Errors
**Symptoms:**
- "Module not found" errors
- Import path issues
- Circular import problems

**Solutions:**
```bash
# Check Python path
python manage.py shell
>>> import sys
>>> print(sys.path)

# Test imports
>>> from accounts_app.models import CustomUser
>>> from accounts_app.services import AuthenticationService

# Check for circular imports
python -c "import accounts_app.models"
```

#### Issue: Template Errors
**Symptoms:**
- Template not found errors
- Template syntax errors
- Context variable issues

**Solutions:**
```bash
# Check template configuration
python manage.py shell
>>> from django.conf import settings
>>> print(settings.TEMPLATES[0]['DIRS'])

# Test template rendering
>>> from django.template.loader import get_template
>>> template = get_template('accounts_app/customer/login.html')
>>> print(template.origin)

# Check template context
>>> from django.template import Context, Template
>>> template = Template("{{ user.email }}")
>>> context = Context({'user': user})
>>> print(template.render(context))
```

## Debug Commands and Tools

### Useful Management Commands
```bash
# Check system status
python manage.py check
python manage.py check --deploy

# Database operations
python manage.py showmigrations
python manage.py migrate --plan
python manage.py migrate --dry-run

# User management
python manage.py createsuperuser
python manage.py changepassword username

# Development tools
python manage.py shell
python manage.py runserver --settings=project_root.settings.development
python manage.py test --debug-mode
```

### Debugging in Python Shell
```python
# User debugging
from accounts_app.models import CustomUser, CustomerProfile, ServiceProviderProfile
user = CustomUser.objects.get(email='<EMAIL>')
print(user.is_active, user.role, user.email_verified)

# Authentication debugging
from accounts_app.services import AuthenticationService
success, user, message = AuthenticationService.authenticate_user('email', 'password')
print(success, message)

# Profile debugging
profile = user.customer_profile  # or service_provider_profile
print(profile.full_name, profile.phone_number)

# Email debugging
from accounts_app.models import EmailVerificationToken
tokens = EmailVerificationToken.objects.filter(user=user)
print(tokens.count())
```

### Log Analysis
```bash
# Application logs
tail -f /var/log/cozywish/django.log

# Database logs
tail -f /var/log/postgresql/postgresql.log

# Web server logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# System logs
journalctl -u cozywish.service -f
```

## Getting Help

### When to Contact Support
- Security incidents
- Data corruption
- System outages
- Performance degradation

### Information to Provide
- Error messages (full traceback)
- Steps to reproduce the issue
- Environment details (Python version, OS, etc.)
- Recent changes made to the system
- Log files and error outputs

### Self-Help Resources
- Django documentation
- Project README files
- Code comments and docstrings
- Test cases for expected behavior

---

*This troubleshooting guide is regularly updated based on common issues encountered. For additional help, consult the project documentation or contact the development team.* 