#!/usr/bin/env python
"""
Environment validation script for CozyWish database reset operations.
This script ensures the environment is properly configured before performing
database reset operations.
"""

import os
import sys
import subprocess
import importlib.util

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def check_django_installation():
    """Check if Django is installed and get version."""
    try:
        import django
        print(f"✅ Django {django.get_version()}")
        return True
    except ImportError:
        print("❌ Django not found. Please install requirements first.")
        return False

def check_virtual_environment():
    """Check if running in virtual environment."""
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Virtual environment detected")
        return True
    else:
        print("⚠️  Warning: Not running in virtual environment")
        return True  # Not fatal, just a warning

def check_required_files():
    """Check if required files exist."""
    required_files = [
        'manage.py',
        'project_root/settings/base.py',
        'requirements.txt'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing required files: {', '.join(missing_files)}")
        return False
    
    print("✅ All required files present")
    return True

def check_database_file():
    """Check if database file exists and show info."""
    if os.path.exists('db.sqlite3'):
        size = os.path.getsize('db.sqlite3')
        print(f"ℹ️  Current database file size: {size} bytes")
        return True
    else:
        print("ℹ️  No existing database file found")
        return True

def check_migrations_directories():
    """Check if migration directories exist."""
    migration_dirs = []
    for root, dirs, files in os.walk('.'):
        if 'migrations' in dirs:
            migration_dirs.append(os.path.join(root, 'migrations'))
    
    if migration_dirs:
        print(f"ℹ️  Found {len(migration_dirs)} migration directories")
        return True
    else:
        print("⚠️  No migration directories found")
        return True

def main():
    """Main validation function."""
    print("🔍 Validating environment for database reset...")
    print("=" * 50)
    
    checks = [
        check_python_version,
        check_django_installation,
        check_virtual_environment,
        check_required_files,
        check_database_file,
        check_migrations_directories
    ]
    
    all_passed = True
    for check in checks:
        if not check():
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("✅ Environment validation passed!")
        return 0
    else:
        print("❌ Environment validation failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 