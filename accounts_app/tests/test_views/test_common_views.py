"""
Basic tests for common views shared across user types.
"""

import pytest
from django.contrib.auth import get_user_model
from django.test import TestCase, Client
from django.urls import reverse

from accounts_app.constants import UserRoles, UserStatus
from accounts_app.models import UserProfile

User = get_user_model()


class CommonAuthViewTests(TestCase):
    """Test cases for common authentication views"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER,
            status=UserStatus.ACTIVE,
            email_verified=True
        )

    def test_business_landing_view(self):
        """Test business landing page view"""
        url = reverse('accounts_app:for_business')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)

    def test_unified_logout_view(self):
        """Test unified logout view"""
        # Login first
        self.client.login(email='<EMAIL>', password='testpass123')
        
        url = reverse('accounts_app:unified_logout')
        response = self.client.post(url)
        
        # Should redirect after logout
        self.assertEqual(response.status_code, 302)


class CommonProfileViewTests(TestCase):
    """Test cases for common profile views"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER,
            status=UserStatus.ACTIVE,
            email_verified=True
        )
        
        self.profile = UserProfile.objects.create(
            user=self.user,
            first_name='John',
            last_name='Doe',
            phone_number='+**********'
        )

    def test_profile_view_authenticated(self):
        """Test profile view for authenticated user"""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Test customer profile view
        url = reverse('accounts_app:customer_profile')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)

    def test_profile_view_unauthenticated(self):
        """Test profile view for unauthenticated user"""
        url = reverse('accounts_app:customer_profile')
        response = self.client.get(url)
        
        # Should redirect to login
        self.assertEqual(response.status_code, 302)

    def test_profile_edit_view_get(self):
        """Test profile edit view GET request"""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        url = reverse('accounts_app:customer_profile_edit')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)


class CommonEmailVerificationViewTests(TestCase):
    """Test cases for common email verification views"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER,
            email_verified=False
        )

    def test_email_verification_view(self):
        """Test email verification view"""
        url = reverse('accounts_app:email_verification', kwargs={'token': 'test_token'})
        response = self.client.get(url)
        
        # Should handle the request (may redirect or return 200)
        self.assertIn(response.status_code, [200, 302])

    def test_resend_verification_email_view(self):
        """Test resend verification email view"""
        url = reverse('accounts_app:resend_verification')
        response = self.client.get(url)
        
        # Should handle the request (may redirect or return 200)
        self.assertIn(response.status_code, [200, 302])

    def test_email_verification_success_view(self):
        """Test email verification success view"""
        url = reverse('accounts_app:email_verification_success')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)

    def test_email_verification_sent_view(self):
        """Test email verification sent view"""
        url = reverse('accounts_app:email_verification_sent')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)


@pytest.mark.django_db
class CommonViewsPytestTests:
    """Pytest-style tests for common views"""

    def test_view_context_processors(self):
        """Test view context processors"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER,
            email_verified=True
        )
        
        client = Client()
        client.login(email='<EMAIL>', password='testpass123')
        
        # Test profile view context
        url = reverse('accounts_app:customer_profile')
        response = client.get(url)
        
        assert response.status_code == 200
        assert 'user' in response.context

    def test_view_middleware_integration(self):
        """Test view middleware integration"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER,
            email_verified=True
        )
        
        client = Client()
        client.login(email='<EMAIL>', password='testpass123')
        
        # Test that middleware doesn't break views
        url = reverse('accounts_app:customer_profile')
        response = client.get(url)
        
        assert response.status_code == 200

    def test_permission_decorators(self):
        """Test permission decorators"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=UserRoles.CUSTOMER,
            email_verified=True
        )
        
        client = Client()
        
        # Test unauthenticated access
        url = reverse('accounts_app:customer_profile')
        response = client.get(url)
        assert response.status_code == 302  # Should redirect to login
        
        # Test authenticated access
        client.login(email='<EMAIL>', password='testpass123')
        response = client.get(url)
        assert response.status_code == 200 