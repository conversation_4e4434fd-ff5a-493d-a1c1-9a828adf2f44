{% extends 'base.html' %}

{% block title %}DaisyUI 5.0.46 Button Components - CozyWish{% endblock %}

{% block content %}
<!-- Hero Section with Gradient Background -->
<div class="hero min-h-[40vh] bg-gradient-to-br from-primary/10 to-secondary/10">
    <div class="hero-content text-center">
        <div class="max-w-md">
            <h1 class="text-5xl font-bold text-primary mb-4">DaisyUI 5.0.46</h1>
            <p class="text-xl text-base-content/80 mb-6">Button Components Collection</p>
            <p class="text-base text-base-content/60">10 Different Button Variations with CozyWish Brand Colors</p>
        </div>
    </div>
</div>

<div class="container mx-auto px-4 py-12 max-w-6xl">
    
    <!-- Button 1: Primary Colors -->
    <div class="card bg-base-100 shadow-lg mb-8">
        <div class="card-body">
            <h2 class="card-title text-2xl text-primary mb-6">1. Primary Color Buttons</h2>
            <div class="flex flex-wrap gap-4">
                <button class="btn">Default</button>
                <button class="btn btn-neutral">Neutral</button>
                <button class="btn btn-primary">Primary</button>
                <button class="btn btn-secondary">Secondary</button>
                <button class="btn btn-accent">Accent</button>
            </div>
        </div>
    </div>

    <!-- Button 2: Semantic Colors -->
    <div class="card bg-base-100 shadow-lg mb-8">
        <div class="card-body">
            <h2 class="card-title text-2xl text-primary mb-6">2. Semantic Color Buttons</h2>
            <div class="flex flex-wrap gap-4">
                <button class="btn btn-info">Info</button>
                <button class="btn btn-success">Success</button>
                <button class="btn btn-warning">Warning</button>
                <button class="btn btn-error">Error</button>
            </div>
        </div>
    </div>

    <!-- Button 3: Outline Variations -->
    <div class="card bg-base-100 shadow-lg mb-8">
        <div class="card-body">
            <h2 class="card-title text-2xl text-primary mb-6">3. Outline Button Styles</h2>
            <div class="flex flex-wrap gap-4">
                <button class="btn btn-outline">Default</button>
                <button class="btn btn-outline btn-primary">Primary</button>
                <button class="btn btn-outline btn-secondary">Secondary</button>
                <button class="btn btn-outline btn-accent">Accent</button>
                <button class="btn btn-outline btn-success">Success</button>
            </div>
        </div>
    </div>

    <!-- Button 4: Ghost Style -->
    <div class="card bg-base-100 shadow-lg mb-8">
        <div class="card-body">
            <h2 class="card-title text-2xl text-primary mb-6">4. Ghost & Link Buttons</h2>
            <div class="flex flex-wrap gap-4">
                <button class="btn btn-ghost">Ghost</button>
                <button class="btn btn-link">Link</button>
                <button class="btn btn-ghost text-primary">Ghost Primary</button>
                <button class="btn btn-ghost text-secondary">Ghost Secondary</button>
                <button class="btn btn-ghost text-accent">Ghost Accent</button>
            </div>
        </div>
    </div>

    <!-- Button 5: Size Variations -->
    <div class="card bg-base-100 shadow-lg mb-8">
        <div class="card-body">
            <h2 class="card-title text-2xl text-primary mb-6">5. Size Variations</h2>
            <div class="flex flex-wrap gap-4 items-center">
                <button class="btn btn-primary btn-xs">Extra Small</button>
                <button class="btn btn-primary btn-sm">Small</button>
                <button class="btn btn-primary btn-md">Medium</button>
                <button class="btn btn-primary btn-lg">Large</button>
            </div>
        </div>
    </div>

    <!-- Button 6: Square and Circle -->
    <div class="card bg-base-100 shadow-lg mb-8">
        <div class="card-body">
            <h2 class="card-title text-2xl text-primary mb-6">6. Square & Circle Buttons</h2>
            <div class="flex flex-wrap gap-4 items-center">
                <button class="btn btn-square btn-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="currentColor" class="size-[1.2em]">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z" />
                    </svg>
                </button>
                <button class="btn btn-circle btn-secondary">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="currentColor" class="size-[1.2em]">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                    </svg>
                </button>
                <button class="btn btn-square btn-outline btn-accent">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-[1.2em]">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
                <button class="btn btn-circle btn-ghost">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-[1.2em]">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Button 7: Wide and Block -->
    <div class="card bg-base-100 shadow-lg mb-8">
        <div class="card-body">
            <h2 class="card-title text-2xl text-primary mb-6">7. Wide & Block Buttons</h2>
            <div class="space-y-4">
                <button class="btn btn-primary btn-wide">Wide Button</button>
                <button class="btn btn-secondary btn-block">Block Button (Full Width)</button>
            </div>
        </div>
    </div>

    <!-- Button 8: Button States -->
    <div class="card bg-base-100 shadow-lg mb-8">
        <div class="card-body">
            <h2 class="card-title text-2xl text-primary mb-6">8. Button States</h2>
            <div class="flex flex-wrap gap-4 mb-4">
                <button class="btn btn-primary">Normal</button>
                <button class="btn btn-primary btn-active">Active</button>
                <button class="btn btn-primary" disabled>Disabled</button>
            </div>
            <div class="flex flex-wrap gap-4">
                <button class="btn btn-square btn-primary">
                    <span class="loading loading-spinner loading-sm"></span>
                </button>
                <button class="btn btn-primary">
                    <span class="loading loading-spinner loading-sm"></span>
                    Loading...
                </button>
            </div>
        </div>
    </div>

    <!-- Button 9: Icon Buttons -->
    <div class="card bg-base-100 shadow-lg mb-8">
        <div class="card-body">
            <h2 class="card-title text-2xl text-primary mb-6">9. Buttons with Icons</h2>
            <div class="flex flex-wrap gap-4">
                <button class="btn btn-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="currentColor" class="size-[1.2em]">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z" />
                    </svg>
                    Like
                </button>
                <button class="btn btn-secondary">
                    Download
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="currentColor" class="size-[1.2em]">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
                    </svg>
                </button>
                <button class="btn btn-outline btn-accent">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-[1.2em]">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 3.935 2.186 2.25 2.25 0 0 0-3.935-2.186Zm0-12.814a2.25 2.25 0 1 0 3.935-2.186 2.25 2.25 0 0 0-3.935 2.186Z" />
                    </svg>
                    Share
                </button>
            </div>
        </div>
    </div>

    <!-- Button 10: Group Buttons -->
    <div class="card bg-base-100 shadow-lg mb-8">
        <div class="card-body">
            <h2 class="card-title text-2xl text-primary mb-6">10. Button Groups</h2>
            <div class="join">
                <button class="btn join-item btn-primary">Button 1</button>
                <button class="btn join-item btn-primary">Button 2</button>
                <button class="btn join-item btn-primary">Button 3</button>
            </div>
            <div class="divider"></div>
            <div class="join">
                <button class="btn join-item btn-outline">Left</button>
                <button class="btn join-item btn-outline btn-active">Center</button>
                <button class="btn join-item btn-outline">Right</button>
            </div>
        </div>
    </div>

    <!-- Interactive Demo Section -->
    <div class="bg-gradient-to-r from-primary/5 to-secondary/5 rounded-2xl p-8 mb-8">
        <h2 class="text-3xl font-bold text-primary mb-8 text-center">Interactive Demo</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h3 class="card-title text-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456z" />
                        </svg>
                        CozyWish Premium
                    </h3>
                    <p class="text-base-content/70">Experience our premium services with beautiful DaisyUI components.</p>
                    <div class="card-actions justify-end mt-4">
                        <button class="btn btn-outline btn-primary">Learn More</button>
                        <button class="btn btn-primary">Get Started</button>
                    </div>
                </div>
            </div>
            
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h3 class="card-title text-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z" />
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 6h.008v.008H6V6z" />
                        </svg>
                        Special Offers
                    </h3>
                    <p class="text-base-content/70">Discover our latest promotions and exclusive discounts.</p>
                    <div class="card-actions justify-end mt-4">
                        <button class="btn btn-secondary">View Offers</button>
                        <button class="btn btn-accent">Save Now</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Brand Colors Reference -->
    <div class="card bg-base-100 shadow-lg mb-8">
        <div class="card-body">
            <h2 class="card-title text-2xl text-primary mb-6">CozyWish Brand Colors</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="w-20 h-20 bg-primary rounded-xl mx-auto mb-3 shadow-lg border-2 border-primary/20"></div>
                    <p class="font-semibold text-primary">Primary</p>
                    <p class="text-sm opacity-70">#43251B</p>
                </div>
                <div class="text-center">
                    <div class="w-20 h-20 bg-secondary rounded-xl mx-auto mb-3 shadow-lg border-2 border-secondary/20"></div>
                    <p class="font-semibold text-secondary">Secondary</p>
                    <p class="text-sm opacity-70">#FAE1D7</p>
                </div>
                <div class="text-center">
                    <div class="w-20 h-20 bg-accent rounded-xl mx-auto mb-3 shadow-lg border-2 border-accent/20"></div>
                    <p class="font-semibold text-accent">Accent</p>
                    <p class="text-sm opacity-70">#FAE1D7</p>
                </div>
                <div class="text-center">
                    <div class="w-20 h-20 bg-neutral rounded-xl mx-auto mb-3 shadow-lg border-2 border-neutral/20"></div>
                    <p class="font-semibold text-neutral-content">Neutral</p>
                    <p class="text-sm opacity-70">#262626</p>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Footer with Gradient -->
<footer class="bg-gradient-to-r from-primary/10 to-secondary/10 p-8 mt-12">
    <div class="container mx-auto text-center">
        <p class="text-primary font-semibold">CozyWish Design System</p>
        <p class="text-base-content/70 mt-2">Built with DaisyUI 5.0.46 & Tailwind CSS</p>
        <div class="mt-4">
            <button class="btn btn-primary btn-sm">Documentation</button>
            <button class="btn btn-outline btn-primary btn-sm ml-2">GitHub</button>
        </div>
    </div>
</footer>
{% endblock %}
