{% comment %}
CozyWish Professional Navbar Component - Design System Enhanced
Usage: {% include 'includes/navbar_cw.html' with variant='default' %}
Variants: default, minimal, hero, admin, provider, customer
{% endcomment %}

{% load static %}

{% comment %}
Navbar Configuration
{% endcomment %}
{% with variant=variant|default:'default' %}
{% with show_brand=show_brand|default:True %}
{% with show_search=show_search|default:False %}
{% with show_cart=show_cart|default:True %}
{% with show_notifications=show_notifications|default:True %}
{% with show_user_menu=show_user_menu|default:True %}

<!-- Professional Navigation Bar -->
<nav class="navbar navbar-expand-lg navbar-{{ variant }}" id="cozywish-navbar">
    <div class="container">
        <!-- Brand -->
        {% if show_brand %}
        <a class="navbar-brand fw-bold" href="{% url 'home_app:home' %}">
            <i class="fas fa-spa me-2"></i>CozyWish
        </a>
        {% endif %}

        <!-- Search Bar (Optional) -->
        {% if show_search %}
        <div class="navbar-search d-none d-lg-flex mx-auto">
            <form class="d-flex" role="search">
                <div class="input-group">
                    <input class="form-control" type="search" placeholder="Search services..." aria-label="Search">
                    <button class="btn btn-outline-brand" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
        {% endif %}

        <!-- Mobile Toggle Button -->
        <button class="navbar-toggler" 
                type="button" 
                data-bs-toggle="collapse" 
                data-bs-target="#navbarNav" 
                aria-controls="navbarNav" 
                aria-expanded="false" 
                aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navigation Content -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <!-- Main Navigation Links -->
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'home' %}active{% endif %}" 
                       href="{% url 'home_app:home' %}">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
                </li>
                
                {% if variant != 'minimal' %}
                <li class="nav-item">
                    <a class="nav-link {% if 'services' in request.path %}active{% endif %}" href="#">
                        <i class="fas fa-spa me-1"></i>Services
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'about' in request.path %}active{% endif %}" href="#">
                        <i class="fas fa-info-circle me-1"></i>About
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'contact' in request.path %}active{% endif %}" href="#">
                        <i class="fas fa-envelope me-1"></i>Contact
                    </a>
                </li>
                {% endif %}

                <!-- Role-specific navigation items -->
                {% if user.is_authenticated %}
                    {% if user.is_service_provider %}
                        <li class="nav-item">
                            <a class="nav-link {% if 'provider' in request.path %}active{% endif %}" 
                               href="{% url 'dashboard_app:provider_dashboard' %}">
                                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                            </a>
                        </li>
                    {% elif user.is_customer %}
                        <li class="nav-item">
                            <a class="nav-link {% if 'customer' in request.path %}active{% endif %}" 
                               href="{% url 'dashboard_app:customer_dashboard' %}">
                                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                            </a>
                        </li>
                    {% endif %}
                {% endif %}
            </ul>

            <!-- Right Side Navigation -->
            <div class="navbar-nav ms-auto align-items-center">
                <!-- Shopping Cart (Customer Only) -->
                {% if show_cart and user.is_authenticated and user.is_customer %}
                    <a href="{% url 'booking_cart_app:cart_view' %}"
                       class="btn btn-outline-brand me-2 position-relative"
                       title="View Cart">
                        <i class="fas fa-shopping-cart"></i>
                        {% if cart_count > 0 %}
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                {{ cart_count }}
                                <span class="visually-hidden">items in cart</span>
                            </span>
                        {% endif %}
                    </a>
                {% endif %}

                <!-- Notifications -->
                {% if show_notifications and user.is_authenticated %}
                    {% include 'notifications_app/notification_dropdown.html' %}
                {% endif %}

                <!-- Provider Dashboard or Business Link -->
                {% if user.is_service_provider %}
                    <a href="{% url 'dashboard_app:provider_dashboard' %}" 
                       class="btn btn-outline-brand me-2" 
                       title="Provider Dashboard">
                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                    </a>
                {% elif not user.is_authenticated or not user.is_customer %}
                    <a href="{% url 'accounts_app:for_business' %}" 
                       class="btn btn-outline-secondary me-2" 
                       title="Business Registration">
                        <i class="fas fa-store me-1"></i>For Business
                    </a>
                {% endif %}

                <!-- User Menu -->
                {% if show_user_menu %}
                <div class="dropdown">
                    <button class="btn btn-primary dropdown-toggle" 
                            type="button" 
                            data-bs-toggle="dropdown" 
                            aria-expanded="false"
                            title="Main Menu">
                        {% if user.is_authenticated %}
                            <i class="fas fa-user me-2"></i>
                            {{ user.first_name|default:"Account" }}
                        {% else %}
                            <i class="fas fa-user me-2"></i>Menu
                        {% endif %}
                    </button>
                    
                    <!-- Dropdown Menu Content -->
                    <ul class="dropdown-menu dropdown-menu-end shadow">
                        {% if user.is_authenticated %}
                            <!-- User Profile Section -->
                            <li>
                                <h6 class="dropdown-header">
                                    {% if user.is_service_provider %}
                                        <i class="fas fa-store me-2"></i>Business Account
                                    {% else %}
                                        <i class="fas fa-user me-2"></i>Customer Account
                                    {% endif %}
                                </h6>
                            </li>
                            
                            <li>
                                {% if user.is_service_provider %}
                                    <a class="dropdown-item" href="{% url 'accounts_app:service_provider_profile' %}">
                                        <i class="fas fa-user me-2"></i>My Profile
                                    </a>
                                {% else %}
                                    <a class="dropdown-item" href="{% url 'accounts_app:customer_profile' %}">
                                        <i class="fas fa-user me-2"></i>My Profile
                                    </a>
                                {% endif %}
                            </li>

                            <!-- Customer-Specific Menu Items -->
                            {% if user.is_customer %}
                                <li>
                                    <a class="dropdown-item" href="{% url 'dashboard_app:customer_dashboard' %}">
                                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'dashboard_app:customer_booking_status' %}">
                                        <i class="fas fa-calendar-check me-2"></i>My Bookings
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'booking_cart_app:cart_view' %}">
                                        <i class="fas fa-shopping-cart me-2"></i>My Cart
                                        {% if cart_count > 0 %}
                                            <span class="badge bg-danger ms-auto">{{ cart_count }}</span>
                                        {% endif %}
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'notifications_app:notification_list' %}">
                                        <i class="fas fa-bell me-2"></i>Notifications
                                        {% if unread_notifications_count > 0 %}
                                            <span class="badge bg-danger ms-auto">{{ unread_notifications_count }}</span>
                                        {% endif %}
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'payments_app:payment_history' %}">
                                        <i class="fas fa-credit-card me-2"></i>Payment History
                                    </a>
                                </li>
                            {% endif %}

                            <!-- Service Provider-Specific Menu Items -->
                            {% if user.is_service_provider %}
                                <li>
                                    <a class="dropdown-item" href="{% url 'booking_cart_app:provider_booking_list' %}">
                                        <i class="fas fa-calendar-check me-2"></i>Bookings
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'notifications_app:notification_list' %}">
                                        <i class="fas fa-bell me-2"></i>Notifications
                                        {% if unread_notifications_count > 0 %}
                                            <span class="badge bg-danger ms-auto">{{ unread_notifications_count }}</span>
                                        {% endif %}
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'payments_app:provider_payment_history' %}">
                                        <i class="fas fa-credit-card me-2"></i>Payment History
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#">
                                        <i class="fas fa-building me-2"></i>My Venue
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#">
                                        <i class="fas fa-spa me-2"></i>My Services
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#">
                                        <i class="fas fa-star me-2"></i>Reviews
                                    </a>
                                </li>
                            {% endif %}

                            <!-- Admin Menu Items -->
                            {% if user.is_staff %}
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <h6 class="dropdown-header">
                                        <i class="fas fa-cog me-2"></i>Administration
                                    </h6>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'admin:index' %}">
                                        <i class="fas fa-cog me-2"></i>Django Admin
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'admin_app:pending_providers' %}">
                                        <i class="fas fa-check-circle me-2"></i>Provider Approvals
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'admin_app:user_list' %}">
                                        <i class="fas fa-users-cog me-2"></i>User Management
                                    </a>
                                </li>
                            {% endif %}

                            <li><hr class="dropdown-divider"></li>

                            <!-- Logout -->
                            <li>
                                <a class="dropdown-item text-danger" href="{% url 'accounts_app:unified_logout' %}">
                                    <i class="fas fa-sign-out-alt me-2"></i>Log out
                                </a>
                            </li>
                        {% else %}
                            <!-- Guest User Menu Items -->
                            <li>
                                <a class="dropdown-item" href="{% url 'accounts_app:customer_login' %}">
                                    <i class="fas fa-sign-in-alt me-2"></i>Log in
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{% url 'account_signup' %}">
                                    <i class="fas fa-user-plus me-2"></i>Sign Up
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{% url 'account_login' %}">
                                    <i class="fas fa-sign-in-alt me-2"></i>Business Login
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</nav>

{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}
