/************************************************************
 * 1. GLOBAL / BASE STYLES & TYPOGRAPHY HIERARCHY
 ************************************************************/

/* Typography Scale Variables */
:root {
  /* Font Families */
  --font-primary: 'Roboto', sans-serif;
  --font-heading: 'Yeseva One', serif;

  /* Font Sizes - Mobile First Approach */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 4rem;      /* 64px */

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Spacing Scale */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 1rem;      /* 16px */
  --spacing-lg: 1.5rem;    /* 24px */
  --spacing-xl: 2rem;      /* 32px */
  --spacing-2xl: 3rem;     /* 48px */
  --spacing-3xl: 4rem;     /* 64px */

  /* Colors */
  --color-primary: black;
  --color-primary-light: black;
  --color-primary-dark: black;
  --color-accent: white;
  --color-accent-light: white;
  --color-accent-dark: white;
  --color-text: black;
  --color-text-muted: black;
  --color-text-light: black;
  --color-border: black;
}

body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  margin: 0;
  min-height: 100vh;
  padding-top: 50px;
  background-color: #F8F9FA !important;
  background: #F8F9FA !important;
}

/* Typography Hierarchy */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 700;
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-md);
  color: var(--color-primary);
}

h1 {
  font-size: var(--font-size-5xl);
  margin-bottom: var(--spacing-xl);
}

h2 {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-lg);
}

h3 {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-lg);
}

h4 {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-md);
}

h5 {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-md);
}

h6 {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-sm);
}

/* Text Utilities */
.text-large {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
}

.text-small {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

.text-xs {
  font-size: var(--font-size-xs);
  line-height: var(--line-height-normal);
}

.text-muted {
  color: var(--color-text-muted) !important;
}

.text-light {
  color: var(--color-text-light) !important;
}

/* Section Spacing */
.section {
  padding: var(--spacing-3xl) 0;
}

.section-sm {
  padding: var(--spacing-2xl) 0;
}

.section-lg {
  padding: calc(var(--spacing-3xl) * 1.5) 0;
}



/************************************************************
 * 2. RADIAL BACKGROUND GRADIENT & ANIMATION
 ************************************************************/
.radial-gradient {
  width: 100%;
  min-height: 100vh;
  background: radial-gradient(ellipse at center,
              #fae1d7 40%,
              rgba(248, 249, 250, 0.5) 70%,
              #F8F9FA 100%);
  background-size: cover;
  animation: radialMove 8s ease-in-out infinite;
}

/* Subtle movement animation */
@keyframes radialMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}



/************************************************************
 * 3. PROFESSIONAL NAVBAR STYLES
 ************************************************************/

/*
  Main navbar container with enhanced professional styling
*/
.navbar {
  background: white !important;
  padding: 1rem 2rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1030;
  margin-bottom: 0;
}

/*
  Enhanced brand styling with better typography
*/
.navbar-brand {
  font-family: var(--font-heading);
  font-weight: 700;
  font-size: 1.875rem;
  color: black;
  text-decoration: none;
  letter-spacing: -0.025em;
  transition: all 0.2s ease;
}

.navbar-brand:hover {
  color: black;
  transform: translateY(-1px);
}

/*
  Professional navigation buttons container
*/
.nav-buttons {
  gap: 0.75rem;
}

/*
  Enhanced button styling for professional appearance
*/
.nav-buttons .btn {
  border-radius: 0.75rem;
  padding: 0.75rem 1.25rem;
  background: white;
  font-size: 0.875rem;
  font-weight: 600;
  border: 1.5px solid rgba(0, 0, 0, 0.12);
  color: black;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.025em;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.nav-buttons .btn:hover {
  background: rgba(0, 0, 0, 0.02);
  border-color: rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  color: black;
}

.nav-buttons .btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.nav-buttons .btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

/*
  Professional notification icon styling
*/
.nav-link {
  color: black !important;
  font-weight: 500;
  padding: 0.75rem 1rem !important;
  border-radius: 0.75rem;
  transition: all 0.2s ease;
  position: relative;
  border: 1.5px solid transparent;
}

.nav-link:hover {
  background: rgba(0, 0, 0, 0.04);
  color: black !important;
  border-color: rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.nav-link i.fas.fa-bell {
  font-size: 1.125rem;
  transition: all 0.2s ease;
}

.nav-link:hover i.fas.fa-bell {
  transform: scale(1.1);
}

/*
  Enhanced notification badge styling
*/
.nav-link .badge {
  font-size: 0.625rem;
  font-weight: 700;
  padding: 0.25rem 0.4rem;
  border: 2px solid white;
  box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/*
  Fixed navbar enhancements
*/
.fixed-top {
  background-color: white;
  padding: 0.75rem 2rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/*
  Scrolled navbar state
*/
.navbar.scrolled {
  background-color: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/*
  Professional dropdown menu styling
*/
.dropdown-menu {
  padding: 0.75rem 0;
  border-radius: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  min-width: 260px;
  background: white;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/*
  Enhanced dropdown items
*/
.dropdown-item {
  padding: 0.875rem 1.5rem;
  color: black;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  border-radius: 0.5rem;
  margin: 0.125rem 0.5rem;
  position: relative;
}

.dropdown-item:hover {
  background: rgba(0, 0, 0, 0.04);
  color: black;
  transform: translateX(4px);
}

.dropdown-item i {
  width: 1.25rem;
  text-align: center;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.dropdown-item:hover i {
  opacity: 1;
}

/*
  Professional badge styling in dropdown
*/
.dropdown-item .badge {
  font-size: 0.625rem;
  font-weight: 700;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
}

/*
  Dropdown divider enhancement
*/
.dropdown-divider {
  margin: 0.75rem 0.5rem;
  border-color: rgba(0, 0, 0, 0.08);
}

/*
  Professional cart button styling
*/
.nav-buttons .btn.btn-light {
  background: white;
  border: 1.5px solid rgba(0, 0, 0, 0.12);
  color: black;
  position: relative;
  overflow: visible;
}

.nav-buttons .btn.btn-light:hover {
  background: rgba(0, 0, 0, 0.02);
  border-color: rgba(0, 0, 0, 0.2);
  color: black;
}

.nav-buttons .btn.btn-light i {
  font-size: 1rem;
  transition: transform 0.2s ease;
}

.nav-buttons .btn.btn-light:hover i {
  transform: scale(1.1);
}

/*
  Enhanced cart badge styling
*/
.nav-buttons .btn .badge {
  font-size: 0.625rem;
  font-weight: 700;
  padding: 0.25rem 0.4rem;
  border: 2px solid white;
  box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3);
  min-width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/*
  Professional navbar toggler for mobile
*/
.navbar-toggler {
  border: 1.5px solid rgba(0, 0, 0, 0.12);
  border-radius: 0.75rem;
  padding: 0.5rem 0.75rem;
  transition: all 0.2s ease;
}

.navbar-toggler:hover {
  border-color: rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0.02);
}

.navbar-toggler:focus {
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

/*
  Responsive navbar improvements
*/
@media (max-width: 991.98px) {
  .navbar {
    padding: 0.75rem 1.5rem;
  }

  .navbar-brand {
    font-size: 1.5rem;
  }

  .nav-buttons {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.75rem;
  }

  .nav-buttons .btn {
    padding: 0.625rem 1rem;
    font-size: 0.8125rem;
  }

  .dropdown-menu {
    min-width: 240px;
    margin-top: 0.5rem;
  }
}

@media (max-width: 767.98px) {
  .navbar {
    padding: 0.625rem 1rem;
  }

  .navbar-brand {
    font-size: 1.375rem;
  }

  .nav-buttons {
    width: 100%;
    justify-content: center;
    margin-top: 1rem;
  }

  .nav-buttons .btn {
    flex: 1;
    max-width: 120px;
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }

  .dropdown-menu {
    min-width: 220px;
    left: 50% !important;
    transform: translateX(-50%) !important;
  }
}

/*
  Professional active state for navigation items
*/
.nav-buttons .btn.active {
  background: rgba(0, 0, 0, 0.06);
  border-color: rgba(0, 0, 0, 0.2);
  color: black;
  font-weight: 700;
}

.dropdown-item.active {
  background: rgba(0, 0, 0, 0.06);
  color: black;
  font-weight: 600;
}

/*
  Professional focus states for accessibility
*/
.nav-buttons .btn:focus-visible,
.nav-link:focus-visible,
.dropdown-item:focus-visible {
  outline: 2px solid rgba(0, 0, 0, 0.8);
  outline-offset: 2px;
}

/*
  Smooth transitions for all interactive elements
*/
.navbar *,
.nav-buttons *,
.dropdown-menu * {
  transition: all 0.2s ease;
}

/*
  Headers (like "Top categories") inside dropdown
*/
.dropdown-header {
  padding: 0.75rem 1.25rem;
  font-size: 0.875rem;
  color: black;
}

/*
  Divider lines inside dropdown
*/
.dropdown-divider {
  margin: 0.5rem 0;
}


/************************************************************
 * 4. HERO SECTION
 ************************************************************/

.hero-title {
  font-family: 'Yeseva One', serif; /* Apply the new font */
  font-size: 64px; /* Larger, more professional size */
  font-weight: 700; /* Keep it bold */
  line-height: 1.2; /* Maintain spacing */
  text-transform: capitalize; /* Ensure a nice capitalized effect */
  margin-bottom: 48px;
  color: #2F160F; /* Dark brown color for heading */
}

.hero-subtitle {
  font-size: 20px;
  line-height: 1.6;
  color: #2F160F;
  margin-bottom: 48px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  font-weight: 400;
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.section-title {
  font-family: var(--font-heading);
  font-size: var(--font-size-4xl);
  font-weight: 700;
  line-height: var(--line-height-tight);
  color: var(--color-primary);
  margin-bottom: var(--spacing-md);
}

.section-subtitle {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  color: var(--color-text-muted);
  max-width: 500px;
  margin: 0 auto;
}




/************************************************************
 * 5. SEARCH CONTAINER (IN HERO)
 ************************************************************/
/*
  Container holding the search fields (categories & location)
  plus the search button.
*/
.search-container {
  background: white;
  border-radius: 100px;                 /* Pill shape around the entire search bar */
  padding: 8px;                         /* Internal padding for the entire container */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  width: 100%;
  max-width: 800px;                     /* Limits the maximum width on larger screens */
  margin: 0 auto;                       /* Centers the container */
}

.search-container:hover {
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
}

.search-container:focus-within {
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
}

/*
  The wrapper for each input inside the search container
*/
.search-group {
  position: relative; /* Allows positioning of the icon in the input */
  width: 100%;
}

/*
  The text input styles for categories & location
*/
.search-input {
  border: none;                    /* Removes default border */
  padding: 16px 16px 16px 48px;    /* Spacing around text and extra space for icon */
  background: transparent;         /* Transparent so the container's background shows */
  width: 100%;                     /* Full width inside the container */
  font-size: 1rem;                 /* Base font size for inputs */
  color: #333;                     /* Dark text color */
}

/*
  Removes the focus outline and shadow for a cleaner look
*/
.search-input:focus {
  outline: none;
  box-shadow: none;
}

/*
  Placeholder text color
*/
.search-input::placeholder {
  color: #666;
}



/*
  The left icon inside each search input (e.g., search or location icon)
*/
.input-icon-left {
  position: absolute;         /* Positioned relative to parent container */
  left: 16px;                 /* Spacing from left side */
  top: 50%;                   /* Vertically center it within input */
  transform: translateY(-50%);
  color: #666;
  font-size: 1.1rem;
}

/*
  Vertical separator line between Category and Location inputs
*/
.search-divider {
  width: 1px;
  height: 32px;
  background-color: #e0e0e0;
  margin: 0 8px;
}

/*
  "Search" button on the right side
*/
.search-btn {
  background: #2F160F;        /* Dark background color */
  color: white;               /* White text */
  border: none;
  border-radius: 100px;       /* Pill shaped button */
  padding: 16px 32px;
  font-weight: 500;
  font-size: 1rem;
  transition: background-color 0.2s;
  white-space: nowrap;        /* Prevent text from wrapping */
  cursor: pointer;
}

.search-btn:hover {
  background: #1a0d09;        /* Slightly darker color on hover */
  color: white;               /* Ensure text stays white */
}

.search-btn:active {
  background: #1a0d09;
  color: white;
}

.search-btn:focus {
  outline: none;
  background: #2F160F;
  color: white;
  box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.3);
}


/************************************************************
 * 6. BOOKING COUNT (HERO SECTION)
 ************************************************************/
/*
  Text showing the total bookings made today
*/
.booking-count {
  font-size: 25px;
  margin-top: 2rem;
  color: #2F160F;
}

/*
  Emphasize the number in bold
*/
.booking-count strong {
  font-weight: 600;
}


/************************************************************
 * 7. CARD SECTIONS (TOP PICKS, TRENDING, EXCLUSIVE DEALS, ETC.)
 ************************************************************/
/*
  General styles for each service card
  used in multiple sections (Top Picks, Trending, etc.)
*/
.service-card {
  width: 317px;                       /* Fixed width for consistency */
  border: none;
  border-radius: 10px;                /* Rounds card corners */
  overflow: hidden;                   /* Ensures child elements don't overflow */
  transition: transform 0.2s;         /* Slight scale effect on hover */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Soft shadow for card depth */
  margin: 0 auto;                     /* Center the card horizontally */
  background: white;
}

.service-card:hover {
  transform: translateY(-5px); /* Move the card up slightly on hover */
}

/*
  Images within each card
*/
.card-img-top {
  width: 317px;
  height: 177px;
  object-fit: cover; /* Ensures the image is cropped proportionally */
}

/*
  Inner card body spacing
*/
.card-body {
  padding: 1rem;
}

/*
  Card title (e.g., "Glow Beauty Salon")
*/
.card-title {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--color-primary);
  line-height: var(--line-height-snug);
}

/*
  Rating section inside the card (stars + number of reviews)
*/
.rating {
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/*
  Star rating number
*/
.rating-score {
  font-weight: 600;
  color: var(--color-primary);
  font-size: var(--font-size-base);
}

/*
  The total number of reviews
*/
.review-count {
  color: var(--color-text-muted);
  font-size: var(--font-size-sm);
}

/*
  Location text (e.g., "Al Jazirah, Al Qatif")
*/
.location {
  margin-bottom: var(--spacing-sm);
  color: var(--color-text-muted);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

/*
  Business type label (e.g. "Beauty Salon", "Barbershop")
*/
.business-type {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--color-accent-light);
  border-radius: 20px;
  font-size: var(--font-size-xs);
  color: var(--color-primary);
  font-weight: 500;
  border: 1px solid var(--color-accent);
}

/************************************************************
 * 8. GENERAL NEXT-BUTTON (IF USED FOR SLIDERS)
 ************************************************************/
/*
  A round button typically used to navigate horizontally in a card slider.
  Not always visible but structured for potential use.
*/
.next-btn {
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: white;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.next-btn:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-50%) scale(1.05);
}

/************************************************************
 * 9. GENERIC CONTAINER (OVERRIDE)
 ************************************************************/
/*
  Container override to allow up to 1328px wide content
  and a consistent 24px side padding.
*/
.container {
  max-width: 1328px;
  margin: 0 auto;
  padding: 0 24px;
}

/************************************************************
 * 10. RESPONSIVE MEDIA QUERIES (GENERAL)
 ************************************************************/
/*
  Below are some general breakpoints adjusting font sizes,
  layout, etc. The code above includes smaller breakpoints too.
*/

/*
  Smaller than 1328px wide
*/
@media (max-width: 1328px) {
  .service-card {
    width: 100%;
    max-width: 317px;
  }

  .card-img-top {
    width: 100%;
    height: auto;
    aspect-ratio: 317/177; /* Maintains the same aspect ratio */
  }

  .banner-slider-section {
    width: 100%;
    height: auto;
  }
}

/*
  Tablets and below (less than ~768px)
*/
@media (max-width: 768px) {
  .hero-title {
    font-size: 48px;
  }

  .hero-subtitle {
    font-size: 18px;
  }

  .search-container {
    padding: 4px;
  }

  .search-divider {
    width: 100%;
    height: 1px;
    margin: 8px 0;
  }

  .search-btn {
    width: 100%;
    margin-top: 8px;
    padding: 16px 24px;
    font-size: 1rem;
    border-radius: 40px;
  }

  .next-btn {
    right: 0;
  }

  .nav-buttons .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  .section {
    padding: var(--spacing-2xl) 0;
  }

  .section-lg {
    padding: var(--spacing-3xl) 0;
  }
}

/*
  Extra small devices (less than ~576px)
*/
@media (max-width: 576px) {
  .navbar {
    padding: 1rem;
  }

  .hero-title {
    font-size: 36px;
  }

  .hero-subtitle {
    font-size: 16px;
  }

  .search-input {
    padding: 16px 16px 16px 44px;
    font-size: 1rem;
  }

  .input-icon-left {
    left: 16px;
    font-size: 1rem;
  }

  .booking-count {
    font-size: 20px;
  }

  .card-title {
    font-size: var(--font-size-base);
  }

  .section {
    padding: var(--spacing-xl) 0;
  }

  .section-header {
    margin-bottom: var(--spacing-2xl);
  }
}

/* Skeleton Loader Styles */
.skeleton-card {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
    min-height: 150px;
    background-color: white;
    border: 1px solid black;
}

.skeleton-shimmer {
    height: 100%;
    width: 100%;
    animation: shimmer 1.5s infinite;
    background: linear-gradient(to right, white 0%, #f0f0f0 50%, white 100%);
    background-size: 200% 100%;
}

/* Sticky filter form */
.search-filters.sticky-top {
    top: 70px;
    z-index: 1020;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Discount card styling used across grids */
.discount-card {
    transition: transform 0.2s ease-in-out;
    border: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.discount-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.discount-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
}

.discount-badge.service {
    background-color: white;
    color: black;
    border: 2px solid black;
}

.discount-badge.venue {
    background-color: white;
    color: black;
    border: 2px solid black;
}

.discount-badge.platform {
    background-color: white;
    color: black;
    border: 2px solid black;
}

/* Dashboard card spacing and shadows */
.dashboard-card {
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Sticky table headers */
.table-sticky thead th {
    position: sticky;
    top: 0;
    background-color: white;
    z-index: 1;
}

/* Clickable venue cards */
.card-venue {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card-venue:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

/* Ensure anchor tags around venue cards don't affect styling */
a .card-venue {
    color: inherit;
    text-decoration: none;
}

a:hover .card-venue {
    color: inherit;
    text-decoration: none;
}
