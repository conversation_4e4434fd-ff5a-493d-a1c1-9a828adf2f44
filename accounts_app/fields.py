from django import forms
from .utils.validators import normalize_phone

class TelInput(forms.TextInput):
    """TextInput widget with input type set to 'tel'"""
    input_type = 'tel'

class PhoneNumberField(forms.CharField):
    """CharField that normalizes phone numbers using normalize_phone."""
    widget = TelInput
    
    def to_python(self, value):
        value = super().to_python(value)
        return normalize_phone(value)
