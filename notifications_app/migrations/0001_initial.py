# Generated by Django 5.2.4 on 2025-07-09 09:04

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AdminAnnouncement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title",
                    models.Char<PERSON>ield(help_text="Announcement title", max_length=255),
                ),
                (
                    "slug",
                    models.SlugField(
                        blank=True,
                        help_text="URL-friendly slug auto-generated from the title",
                        max_length=255,
                        unique=True,
                    ),
                ),
                (
                    "announcement_text",
                    models.TextField(help_text="Announcement message content"),
                ),
                (
                    "target_audience",
                    models.CharField(
                        choices=[
                            ("all", "All Users"),
                            ("customers", "Customers Only"),
                            ("providers", "Service Providers Only"),
                            ("admins", "Admins Only"),
                        ],
                        default="all",
                        help_text="Who should receive this announcement",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.Char<PERSON>ield(
                        choices=[
                            ("pending", "Pending"),
                            ("sent", "Sent"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        help_text="Current status of the announcement",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "sent_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the announcement was sent",
                        null=True,
                    ),
                ),
                (
                    "total_recipients",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Total number of users who received this announcement",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        help_text="Admin user who created this announcement",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_admin_announcements",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Admin Announcement",
                "verbose_name_plural": "Admin Announcements",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["target_audience", "status"],
                        name="notificatio_target__4fe376_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="notificatio_created_06ebea_idx"
                    ),
                    models.Index(
                        fields=["status"], name="notificatio_status_5b5c52_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="EmailBounceRecord",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "email_address",
                    models.EmailField(
                        help_text="Email address that bounced", max_length=254
                    ),
                ),
                (
                    "bounce_type",
                    models.CharField(
                        choices=[
                            ("hard", "Hard Bounce"),
                            ("soft", "Soft Bounce"),
                            ("complaint", "Complaint"),
                            ("unsubscribe", "Unsubscribe"),
                        ],
                        help_text="Type of bounce",
                        max_length=20,
                    ),
                ),
                ("bounce_reason", models.TextField(help_text="Reason for the bounce")),
                (
                    "bounce_count",
                    models.PositiveIntegerField(
                        default=1, help_text="Number of times this email has bounced"
                    ),
                ),
                ("first_bounce_at", models.DateTimeField(auto_now_add=True)),
                ("last_bounce_at", models.DateTimeField(auto_now=True)),
                (
                    "is_suppressed",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this email should be suppressed from future sends",
                    ),
                ),
                ("suppressed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        help_text="User associated with the email (if applicable)",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="email_bounces",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Email Bounce Record",
                "verbose_name_plural": "Email Bounce Records",
                "ordering": ["-last_bounce_at"],
                "indexes": [
                    models.Index(
                        fields=["email_address", "is_suppressed"],
                        name="notificatio_email_a_4e7bc4_idx",
                    ),
                    models.Index(
                        fields=["bounce_type", "last_bounce_at"],
                        name="notificatio_bounce__5ffd22_idx",
                    ),
                ],
                "unique_together": {("email_address", "bounce_type")},
            },
        ),
        migrations.CreateModel(
            name="EmailDeliveryStatus",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "email_id",
                    models.CharField(
                        help_text="Unique identifier for this email",
                        max_length=255,
                        unique=True,
                    ),
                ),
                (
                    "recipient_email",
                    models.EmailField(
                        help_text="Email address of the recipient", max_length=254
                    ),
                ),
                (
                    "email_type",
                    models.CharField(
                        help_text="Type of email (welcome, booking_confirmation, etc.)",
                        max_length=50,
                    ),
                ),
                (
                    "subject",
                    models.CharField(help_text="Email subject line", max_length=255),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("sent", "Sent"),
                            ("delivered", "Delivered"),
                            ("opened", "Opened"),
                            ("clicked", "Clicked"),
                            ("bounced", "Bounced"),
                            ("failed", "Failed"),
                        ],
                        default="sent",
                        help_text="Current delivery status",
                        max_length=20,
                    ),
                ),
                ("sent_at", models.DateTimeField(auto_now_add=True)),
                ("delivered_at", models.DateTimeField(blank=True, null=True)),
                ("opened_at", models.DateTimeField(blank=True, null=True)),
                ("clicked_at", models.DateTimeField(blank=True, null=True)),
                ("bounced_at", models.DateTimeField(blank=True, null=True)),
                ("failed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "error_message",
                    models.TextField(
                        blank=True, help_text="Error message if delivery failed"
                    ),
                ),
                (
                    "bounce_type",
                    models.CharField(
                        blank=True,
                        help_text="Type of bounce (hard, soft, etc.)",
                        max_length=50,
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True, default=dict, help_text="Additional email metadata"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who received the email (if applicable)",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="email_deliveries",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Email Delivery Status",
                "verbose_name_plural": "Email Delivery Statuses",
                "ordering": ["-sent_at"],
                "indexes": [
                    models.Index(
                        fields=["recipient_email", "status"],
                        name="notificatio_recipie_165c3d_idx",
                    ),
                    models.Index(
                        fields=["email_type", "sent_at"],
                        name="notificatio_email_t_a71820_idx",
                    ),
                    models.Index(
                        fields=["user", "status"], name="notificatio_user_id_a76fd5_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("booking", "Booking"),
                            ("payment", "Payment"),
                            ("review", "Review"),
                            ("announcement", "Announcement"),
                            ("system", "System"),
                        ],
                        help_text="Type of notification",
                        max_length=20,
                    ),
                ),
                (
                    "title",
                    models.CharField(help_text="Notification title", max_length=255),
                ),
                ("message", models.TextField(help_text="Notification message content")),
                (
                    "read_status",
                    models.CharField(
                        choices=[("unread", "Unread"), ("read", "Read")],
                        default="unread",
                        help_text="Whether the notification has been read",
                        max_length=10,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "read_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the notification was read",
                        null=True,
                    ),
                ),
                (
                    "related_object_id",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="ID of related object (booking, payment, etc.)",
                        null=True,
                    ),
                ),
                (
                    "related_object_type",
                    models.CharField(
                        blank=True, help_text="Type of related object", max_length=50
                    ),
                ),
                (
                    "action_url",
                    models.URLField(
                        blank=True, help_text="URL for notification action (optional)"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="User who will receive this notification",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Notification",
                "verbose_name_plural": "Notifications",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "read_status"],
                        name="notificatio_user_id_40c963_idx",
                    ),
                    models.Index(
                        fields=["user", "created_at"],
                        name="notificatio_user_id_1174e9_idx",
                    ),
                    models.Index(
                        fields=["notification_type"],
                        name="notificatio_notific_f7ef6c_idx",
                    ),
                ],
                "constraints": [
                    models.UniqueConstraint(
                        fields=("user", "related_object_id", "related_object_type"),
                        name="unique_notification_object",
                    )
                ],
            },
        ),
        migrations.CreateModel(
            name="NotificationPreference",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("booking", "Booking"),
                            ("payment", "Payment"),
                            ("review", "Review"),
                            ("announcement", "Announcement"),
                            ("system", "System"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "channel",
                    models.CharField(
                        choices=[("email", "Email"), ("dashboard", "Dashboard")],
                        max_length=20,
                    ),
                ),
                ("is_enabled", models.BooleanField(default=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notification_preferences",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Notification Preference",
                "verbose_name_plural": "Notification Preferences",
                "unique_together": {("user", "notification_type", "channel")},
            },
        ),
    ]
