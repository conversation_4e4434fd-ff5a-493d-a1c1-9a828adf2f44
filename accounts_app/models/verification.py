# --- Standard Library Imports ---
import secrets
from datetime import <PERSON><PERSON><PERSON>
from typing import Optional, Tuple

# --- Django Imports ---
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from .auth import CustomUser


class EmailVerificationToken(models.Model):
    """
    Manages email verification tokens with enhanced security and expiration handling.
    
    This model provides secure token generation and validation for email verification
    with proper expiration times and usage tracking.
    """
    
    user = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='email_verification_tokens',
        verbose_name=_('user account')
    )
    
    token = models.CharField(
        _('verification token'),
        max_length=64,
        unique=True,
        help_text=_('Unique token for email verification')
    )
    
    email_address = models.EmailField(
        _('email address'),
        help_text=_('Email address being verified')
    )
    
    is_used = models.BooleanField(
        _('is used'),
        default=False,
        help_text=_('Whether this token has been used')
    )
    
    expires_at = models.DateTimeField(
        _('expires at'),
        help_text=_('When this token expires')
    )
    
    used_at = models.DateTimeField(
        _('used at'),
        null=True,
        blank=True,
        help_text=_('When this token was used')
    )
    
    ip_address = models.GenericIPAddressField(
        _('IP address'),
        null=True,
        blank=True,
        help_text=_('IP address when token was created')
    )
    
    user_agent = models.TextField(
        _('user agent'),
        blank=True,
        help_text=_('Browser user agent when token was created')
    )
    
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Email Verification Token')
        verbose_name_plural = _('Email Verification Tokens')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['token'], name='email_token_idx'),
            models.Index(fields=['user'], name='email_token_user_idx'),
            models.Index(fields=['expires_at'], name='email_token_expires_idx'),
            models.Index(fields=['is_used'], name='email_token_used_idx'),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['token'],
                name='unique_verification_token'
            )
        ]

    def __str__(self):
        return f"{self.user.email} - Token ({self.token[:8]}...)"

    @property
    def is_valid(self) -> bool:
        """Check if token is valid and not expired"""
        return not self.is_used and not self.is_expired

    @property
    def is_expired(self) -> bool:
        """Check if token has expired"""
        return timezone.now() > self.expires_at

    def mark_as_used(self):
        """Mark token as used"""
        self.is_used = True
        self.used_at = timezone.now()
        self.save(update_fields=['is_used', 'used_at'])

    @classmethod
    def generate_token(cls, user: CustomUser, email_address: str = None, 
                      request=None, expiry_hours: int = 24) -> 'EmailVerificationToken':
        """
        Generate a new verification token for a user.
        
        Args:
            user: User instance
            email_address: Email to verify (defaults to user's email)
            request: HTTP request object for IP and user agent
            expiry_hours: Token expiry in hours (default: 24)
            
        Returns:
            EmailVerificationToken instance
        """
        if email_address is None:
            email_address = user.email
            
        # Generate secure token
        token = secrets.token_urlsafe(32)
        
        # Set expiry time
        expires_at = timezone.now() + timedelta(hours=expiry_hours)
        
        # Extract request information
        ip_address = None
        user_agent = ''
        if request:
            ip_address = request.META.get('REMOTE_ADDR')
            user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # Create token
        verification_token = cls.objects.create(
            user=user,
            token=token,
            email_address=email_address,
            expires_at=expires_at,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        return verification_token

    @classmethod
    def verify_token(cls, token: str) -> Tuple[bool, Optional['EmailVerificationToken'], str]:
        """
        Verify a token and return result.
        
        Args:
            token: Token string to verify
            
        Returns:
            Tuple of (is_valid, token_instance, error_message)
        """
        try:
            token_instance = cls.objects.get(token=token)
            
            if token_instance.is_used:
                return False, token_instance, _('Token has already been used')
            
            if token_instance.is_expired:
                return False, token_instance, _('Token has expired')
            
            return True, token_instance, ''
            
        except cls.DoesNotExist:
            return False, None, _('Invalid token')

    @classmethod
    def cleanup_expired_tokens(cls, days: int = 7):
        """
        Remove expired tokens older than specified days.
        
        Args:
            days: Number of days to keep expired tokens
        """
        cutoff = timezone.now() - timedelta(days=days)
        cls.objects.filter(
            expires_at__lt=cutoff,
            is_used=True
        ).delete()

    @classmethod
    def revoke_user_tokens(cls, user: CustomUser):
        """
        Revoke all active tokens for a user.
        
        Args:
            user: User instance
        """
        cls.objects.filter(
            user=user,
            is_used=False
        ).update(is_used=True, used_at=timezone.now())


class ProfileVerification(models.Model):
    """
    Manages profile verification badges and status for users.
    
    This model tracks different types of verification (email, phone, ID, business)
    and provides a badge system to show verified status to other users.
    """
    
    # Verification types
    VERIFICATION_TYPES = [
        ('email', _('Email Verification')),
        ('phone', _('Phone Verification')),
        ('identity', _('Identity Verification')),
        ('business', _('Business Verification')),
        ('address', _('Address Verification')),
        ('payment', _('Payment Method Verification')),
    ]
    
    # Verification statuses
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('verified', _('Verified')),
        ('rejected', _('Rejected')),
        ('expired', _('Expired')),
    ]
    
    user = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='verifications',
        verbose_name=_('user account')
    )
    
    verification_type = models.CharField(
        _('verification type'),
        max_length=20,
        choices=VERIFICATION_TYPES,
        help_text=_('Type of verification')
    )
    
    status = models.CharField(
        _('status'),
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        help_text=_('Current verification status')
    )
    
    # Verification details
    verification_data = models.JSONField(
        _('verification data'),
        default=dict,
        blank=True,
        help_text=_('Additional verification details and metadata')
    )
    
    # File attachments for verification
    verification_document = models.FileField(
        _('verification document'),
        upload_to='verification_documents/',
        blank=True,
        null=True,
        help_text=_('Supporting document for verification')
    )
    
    # Verification process tracking
    submitted_at = models.DateTimeField(
        _('submitted at'),
        auto_now_add=True,
        help_text=_('When verification was submitted')
    )
    
    verified_at = models.DateTimeField(
        _('verified at'),
        null=True,
        blank=True,
        help_text=_('When verification was approved')
    )
    
    expires_at = models.DateTimeField(
        _('expires at'),
        null=True,
        blank=True,
        help_text=_('When verification expires (if applicable)')
    )
    
    # Staff who processed the verification
    verified_by = models.ForeignKey(
        CustomUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='verified_users',
        verbose_name=_('verified by'),
        help_text=_('Staff member who approved verification')
    )
    
    # Rejection details
    rejection_reason = models.TextField(
        _('rejection reason'),
        blank=True,
        help_text=_('Reason for rejection if verification was denied')
    )
    
    # Notes for staff
    admin_notes = models.TextField(
        _('admin notes'),
        blank=True,
        help_text=_('Internal notes for staff')
    )
    
    # Timestamps
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Profile Verification')
        verbose_name_plural = _('Profile Verifications')
        ordering = ['-created_at']
        unique_together = ['user', 'verification_type']
        indexes = [
            models.Index(fields=['user'], name='verification_user_idx'),
            models.Index(fields=['verification_type'], name='verification_type_idx'),
            models.Index(fields=['status'], name='verification_status_idx'),
            models.Index(fields=['verified_at'], name='verification_verified_idx'),
            models.Index(fields=['expires_at'], name='verification_expires_idx'),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.get_verification_type_display()} ({self.get_status_display()})"

    @property
    def is_verified(self) -> bool:
        """Check if verification is currently valid"""
        if self.status != 'verified':
            return False
        
        # Check if expired
        if self.expires_at and timezone.now() > self.expires_at:
            return False
        
        return True

    @property
    def is_expired(self) -> bool:
        """Check if verification has expired"""
        if not self.expires_at:
            return False
        return timezone.now() > self.expires_at

    @property
    def days_until_expiry(self) -> Optional[int]:
        """Get number of days until verification expires"""
        if not self.expires_at:
            return None
        
        delta = self.expires_at - timezone.now()
        return max(0, delta.days)

    def verify(self, verified_by_user=None, notes=''):
        """Mark verification as verified"""
        self.status = 'verified'
        self.verified_at = timezone.now()
        self.verified_by = verified_by_user
        if notes:
            self.admin_notes = notes
        self.save()

    def reject(self, reason='', rejected_by_user=None, notes=''):
        """Mark verification as rejected"""
        self.status = 'rejected'
        self.rejection_reason = reason
        self.verified_by = rejected_by_user
        if notes:
            self.admin_notes = notes
        self.save()

    def set_expiry(self, days=365):
        """Set expiry date for verification"""
        if self.status == 'verified':
            self.expires_at = timezone.now() + timedelta(days=days)
            self.save()

    @classmethod
    def get_user_verifications(cls, user, include_expired=False):
        """Get all verifications for a user"""
        queryset = cls.objects.filter(user=user)
        
        if not include_expired:
            now = timezone.now()
            queryset = queryset.filter(
                models.Q(expires_at__isnull=True) | 
                models.Q(expires_at__gt=now)
            )
        
        return queryset

    @classmethod
    def get_verified_users(cls, verification_type=None):
        """Get all users with verified profiles"""
        queryset = cls.objects.filter(status='verified')
        
        if verification_type:
            queryset = queryset.filter(verification_type=verification_type)
        
        # Filter out expired verifications
        now = timezone.now()
        queryset = queryset.filter(
            models.Q(expires_at__isnull=True) | 
            models.Q(expires_at__gt=now)
        )
        
        return queryset.values_list('user', flat=True).distinct()

    @classmethod
    def cleanup_expired_verifications(cls):
        """Update expired verifications to expired status"""
        now = timezone.now()
        cls.objects.filter(
            status='verified',
            expires_at__lt=now
        ).update(status='expired')

    def get_badge_info(self) -> dict:
        """Get badge information for display"""
        badge_colors = {
            'email': 'blue',
            'phone': 'green',
            'identity': 'purple',
            'business': 'orange',
            'address': 'teal',
            'payment': 'red'
        }
        
        return {
            'type': self.verification_type,
            'display_name': self.get_verification_type_display(),
            'color': badge_colors.get(self.verification_type, 'gray'),
            'is_verified': self.is_verified,
            'verified_at': self.verified_at,
            'expires_at': self.expires_at,
            'days_until_expiry': self.days_until_expiry
        } 