{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}My Profile - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
{% endblock %}

{% block extra_js %}
<script>
/**
 * Customer Profile Page JavaScript
 * Handles profile picture upload, account deactivation, and animations
 */
document.addEventListener('DOMContentLoaded', function() {
    // Profile picture upload functionality
    const profilePictureEdit = document.getElementById('profile-picture-edit');
    const profilePictureInput = document.getElementById('profile-picture-input');
    const profilePictureForm = document.getElementById('profile-picture-form');

    if (profilePictureEdit && profilePictureInput && profilePictureForm) {
        profilePictureEdit.addEventListener('click', function() {
            profilePictureInput.click();
        });

        profilePictureInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                // Validate file type
                const file = this.files[0];
                const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];

                if (!validTypes.includes(file.type)) {
                    alert('Please select a valid image file (JPEG, PNG, or JPG).');
                    return;
                }

                // Validate file size (max 5MB)
                const maxSize = 5 * 1024 * 1024; // 5MB
                if (file.size > maxSize) {
                    alert('File size must be less than 5MB.');
                    return;
                }

                // Show loading state
                const profilePicture = document.querySelector('.profile-picture');
                const container = document.querySelector('.profile-picture-container');
                
                if (profilePicture && container) {
                    container.classList.add('profile-loading');
                }

                // Submit the form
                profilePictureForm.submit();
            }
        });
    }

    // Account deactivation functionality
    const deactivateBtn = document.getElementById('deactivate-account-btn');
    const deactivateForm = document.getElementById('deactivate-form');

    if (deactivateBtn && deactivateForm) {
        deactivateBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to deactivate your account? This action cannot be undone.')) {
                deactivateForm.submit();
            }
        });
    }

    // Add smooth animations for profile info rows
    const profileInfoRows = document.querySelectorAll('.profile-info-row');
    profileInfoRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transition = 'all 0.2s ease';
        });
    });
});
</script>
{% endblock %}

{% block content %}
<section class="profile-section">
    <div class="profile-container">
        <!-- Profile Header -->
        <div class="profile-hero">
            <!-- Top Action Buttons -->
            <div class="top-actions">
                <a href="{% url 'accounts_app:customer_profile_edit' %}" class="btn-cw-small">
                    <i class="fas fa-edit" aria-hidden="true"></i>
                    Edit Profile
                </a>
                <a href="{% url 'accounts_app:customer_change_password' %}" class="btn-cw-small-secondary">
                    <i class="fas fa-key" aria-hidden="true"></i>
                    Change Password
                </a>
            </div>

            <div class="content">
                <div class="row align-items-center">
                    <div class="col-md-3 text-center mb-3 mb-md-0">
                        <div class="profile-picture-container">
                            {% if profile.profile_picture %}
                                <img src="{{ profile.profile_picture.url }}" alt="Profile Picture" class="profile-picture">
                            {% else %}
                                <img src="https://via.placeholder.com/140x140/fae1d7/2F160F?text=No+Image" alt="Profile Picture" class="profile-picture">
                            {% endif %}
                            <button type="button" id="profile-picture-edit" class="profile-picture-edit-btn" aria-label="Edit profile picture">
                                <i class="fas fa-camera"></i>
                            </button>
                        </div>
                        <!-- Hidden form for profile picture upload -->
                        <form id="profile-picture-form" method="post" enctype="multipart/form-data" action="{% url 'accounts_app:customer_profile_edit' %}" class="d-none">
                            {% csrf_token %}
                            <input type="file" id="profile-picture-input" name="profile_picture" accept="image/*">
                        </form>
                    </div>
                    <div class="col-md-9">
                        <h1 class="profile-name">{{ profile.get_full_name|default:"Welcome!" }}</h1>
                        <p class="profile-email">{{ user.email }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Information Section -->
        <div class="profile-info-section">
            <h2 class="section-title">Profile Information</h2>
            <div class="row">
                <!-- Personal Details Card -->
                <div class="col-lg-6 mb-4">
                    <div class="card-cw">
                        <div class="card-header-cw">
                            <h3 class="card-title-cw">
                                <i class="fas fa-user" aria-hidden="true"></i>
                                Personal Details
                            </h3>
                        </div>
                        <div class="card-body-cw">
                            <div class="profile-info-row">
                                <div class="profile-info-label">First Name:</div>
                                <div class="profile-info-value {% if not profile.first_name %}empty{% endif %}">
                                    {{ profile.first_name|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">Last Name:</div>
                                <div class="profile-info-value {% if not profile.last_name %}empty{% endif %}">
                                    {{ profile.last_name|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">Phone Number:</div>
                                <div class="profile-info-value {% if not profile.phone_number %}empty{% endif %}">
                                    {{ profile.phone_number|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">Gender:</div>
                                <div class="profile-info-value {% if not profile.gender %}empty{% endif %}">
                                    {{ profile.get_gender_display|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">Birth Date:</div>
                                <div class="profile-info-value {% if not profile.birth_month or not profile.birth_year %}empty{% endif %}">
                                    {% if profile.birth_month and profile.birth_year %}
                                        {{ profile.get_birth_month_display }} {{ profile.birth_year }}
                                    {% else %}
                                        Not provided
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information Card -->
                <div class="col-lg-6 mb-4">
                    <div class="card-cw">
                        <div class="card-header-cw">
                            <h3 class="card-title-cw">
                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                Contact Information
                            </h3>
                        </div>
                        <div class="card-body-cw">
                            <div class="profile-info-row">
                                <div class="profile-info-label">Address:</div>
                                <div class="profile-info-value {% if not profile.address %}empty{% endif %}">
                                    {{ profile.address|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">City:</div>
                                <div class="profile-info-value {% if not profile.city %}empty{% endif %}">
                                    {{ profile.city|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">ZIP Code:</div>
                                <div class="profile-info-value {% if not profile.zip_code %}empty{% endif %}">
                                    {{ profile.zip_code|default:"Not provided" }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Information Section -->
        <div class="profile-info-section">
            <h2 class="section-title">Account Information</h2>
            <div class="row">
                <div class="col-12">
                    <div class="card-cw">
                        <div class="card-header-cw">
                            <h3 class="card-title-cw">
                                <i class="fas fa-info-circle" aria-hidden="true"></i>
                                Account Details
                            </h3>
                        </div>
                        <div class="card-body-cw">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Email Address:</div>
                                        <div class="profile-info-value">{{ user.email }}</div>
                                    </div>
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Account Type:</div>
                                        <div class="profile-info-value">
                                            <span class="badge-cw-primary">Customer</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Member Since:</div>
                                        <div class="profile-info-value">{{ user.date_joined|date:"F d, Y" }}</div>
                                    </div>
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Last Login:</div>
                                        <div class="profile-info-value">
                                            {{ user.last_login|date:"F d, Y g:i A"|default:"Never" }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Actions Section -->
        <div class="profile-actions-section">
            <div class="content">
                <h2 class="actions-title">Account Settings</h2>
                <div class="profile-actions">
                    <button type="button" id="deactivate-account-btn" class="btn-cw-danger">
                        <i class="fas fa-user-times" aria-hidden="true"></i>
                        Deactivate Account
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Hidden deactivate form -->
<form id="deactivate-form" method="post" action="{% url 'accounts_app:customer_deactivate' %}" class="d-none">
    {% csrf_token %}
</form>
{% endblock %}
