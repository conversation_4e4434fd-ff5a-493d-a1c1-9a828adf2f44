# CozyWish Design System - Complete Implementation Guide
*Step-by-step tutorial for absolute beginners*

## Your Brand Colors
- **Primary Brown**: `#43251B`
- **Cream Accent**: `#FAE1D7` 
- **Light Cream**: `#FFF9F4`
- **Background**: `#F8F9FA`

---

## PHASE 1: CLEANING & SETUP (Week 1)

### 🧹 PROMPT 1: Clean Up Existing CSS Files

**Goal**: Remove duplicate and conflicting CSS files

**Steps**:
1. **Backup your project first!**
   ```bash
   git add .
   git commit -m "Backup before design system cleanup"
   ```

2. **Identify files to remove**:
   - Keep: `templates/base.html`
   - Remove: `design-system.css` (142KB - too big)
   - Remove: `design-system-complete.css`
   - Remove: `design-system-mvp.css`
   - Keep but will modify: `static/css/base.css`
   - Keep but will modify: `static/css/style.css`

3. **Delete duplicate CSS files**:
   ```bash
   rm design_system/design-system.css
   rm design_system/design-system-complete.css
   rm design_system/design-system-mvp.css
   rm static/css/design-system-complete.css
   ```

4. **Remove inline styles from templates**:
   - Open each template file
   - Remove `<style>` blocks
   - Remove `style=""` attributes
   - Keep only class names

**What you should have after this step**:
- Cleaner file structure
- No duplicate CSS files
- Templates without inline styles

---

### 🏗️ PROMPT 2: Create New File Structure

**Goal**: Set up organized folders for your design system

**Create this folder structure**:
```
static/
├── design-system/
│   ├── tokens/
│   │   └── colors.css
│   ├── base/
│   │   ├── bootstrap-override.css
│   │   └── global.css
│   ├── components/
│   │   ├── buttons.css
│   │   ├── cards.css
│   │   ├── forms.css
│   │   └── navigation.css
│   ├── patterns/
│   │   ├── hero-sections.css
│   │   └── dashboard.css
│   └── main.css
```

**Commands to create folders**:
```bash
mkdir -p static/design-system/tokens
mkdir -p static/design-system/base
mkdir -p static/design-system/components
mkdir -p static/design-system/patterns
```

**What you should have after this step**:
- Organized folder structure
- Empty CSS files ready to fill

---

### 🎨 PROMPT 3: Create Design Tokens (Colors)

**Goal**: Define your brand colors once and use everywhere

**Create `static/design-system/tokens/colors.css`**:
```css
/**
 * CozyWish Design System - Color Tokens
 * Your brand colors defined once, used everywhere
 */

:root {
  /* ===========================================
     YOUR BRAND COLORS (Never change these)
     =========================================== */
  --cw-brand-primary: #43251B;    /* Your main brown */
  --cw-brand-accent: #FAE1D7;     /* Your cream accent */
  --cw-brand-light: #FFF9F4;      /* Your light cream */
  --cw-brand-background: #F8F9FA;  /* Your background */

  /* ===========================================
     BOOTSTRAP 5 OVERRIDES
     =========================================== */
  --bs-primary: var(--cw-brand-primary);
  --bs-primary-rgb: 67, 37, 27;
  --bs-secondary: var(--cw-brand-accent);
  --bs-secondary-rgb: 250, 225, 215;
  --bs-light: var(--cw-brand-light);
  --bs-light-rgb: 255, 249, 244;
  --bs-body-bg: var(--cw-brand-background);
  --bs-body-bg-rgb: 248, 249, 250;

  /* ===========================================
     NEUTRAL COLORS (For text, borders, etc.)
     =========================================== */
  --cw-neutral-50: #fafafa;
  --cw-neutral-100: #f5f5f5;
  --cw-neutral-200: #e5e5e5;
  --cw-neutral-300: #d4d4d4;
  --cw-neutral-400: #a3a3a3;
  --cw-neutral-500: #737373;
  --cw-neutral-600: #525252;
  --cw-neutral-700: #404040;
  --cw-neutral-800: #262626;
  --cw-neutral-900: #171717;

  /* ===========================================
     SEMANTIC COLORS (For alerts, success, etc.)
     =========================================== */
  --cw-success: #059669;
  --cw-warning: #d97706;
  --cw-error: #dc2626;
  --cw-info: #0284c7;
}

/* ===========================================
   UTILITY CLASSES FOR YOUR BRAND COLORS
   =========================================== */
.text-brand-primary { color: var(--cw-brand-primary) !important; }
.text-brand-accent { color: var(--cw-brand-accent) !important; }
.bg-brand-primary { background-color: var(--cw-brand-primary) !important; }
.bg-brand-accent { background-color: var(--cw-brand-accent) !important; }
.bg-brand-light { background-color: var(--cw-brand-light) !important; }
.bg-brand-background { background-color: var(--cw-brand-background) !important; }
```

**What you should have after this step**:
- All your brand colors defined in one place
- Bootstrap colors overridden with your brand
- Utility classes for quick color application

---

### 📝 PROMPT 4: Create Global Base Styles

**Goal**: Set up basic typography and layout using Bootstrap + your colors

**Create `static/design-system/base/global.css`**:
```css
/**
 * CozyWish Design System - Global Base Styles
 * Basic typography and layout with your brand colors
 */

/* ===========================================
   GLOBAL BACKGROUND
   =========================================== */
html, body {
  background-color: var(--cw-brand-background) !important;
  min-height: 100vh;
}

/* ===========================================
   TYPOGRAPHY (Building on Bootstrap)
   =========================================== */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: var(--cw-neutral-800);
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: var(--cw-brand-primary);
  font-weight: 600;
}

/* ===========================================
   LINK STYLES
   =========================================== */
a {
  color: var(--cw-brand-primary);
  text-decoration: none;
}

a:hover {
  color: var(--cw-neutral-700);
  text-decoration: underline;
}

/* ===========================================
   CONTAINER IMPROVEMENTS
   =========================================== */
.container,
.container-fluid {
  background-color: transparent;
}

/* ===========================================
   CUSTOM SPACING UTILITIES
   =========================================== */
.py-section { padding-top: 4rem; padding-bottom: 4rem; }
.py-section-sm { padding-top: 2rem; padding-bottom: 2rem; }
.py-section-lg { padding-top: 6rem; padding-bottom: 6rem; }
```

**What you should have after this step**:
- Consistent background color across your entire site
- Typography using your brand colors
- Custom spacing utilities

---

### 📦 PROMPT 5: Create Main CSS File

**Goal**: Combine everything into one main file

**Create `static/design-system/main.css`**:
```css
/**
 * CozyWish Design System - Main Entry Point
 * Import this file in your base.html template
 */

/* Import Bootstrap 5 first */
@import url('https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css');

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');

/* Import your design system */
@import './tokens/colors.css';
@import './base/global.css';
```

**Update your `templates/base.html`**:
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}CozyWish{% endblock %}</title>
    
    <!-- CozyWish Design System (includes Bootstrap 5) -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'design-system/main.css' %}">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    {% block extra_css %}{% endblock %}
</head>
<body class="{% block body_class %}{% endblock %}">
    {% block content %}{% endblock %}
    
    <!-- Bootstrap 5 JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
```

**What you should have after this step**:
- One main CSS file that includes everything
- Clean base.html template
- Bootstrap 5 with your brand colors applied

---

## PHASE 2: COMPONENT DEVELOPMENT (Week 2-3)

### 🔘 PROMPT 6: Create Button Components

**Goal**: Build consistent buttons using Bootstrap + your brand

**Create `static/design-system/components/buttons.css`**:
```css
/**
 * CozyWish Design System - Button Components
 * Enhanced Bootstrap buttons with your brand
 */

/* ===========================================
   BUTTON ENHANCEMENTS
   =========================================== */

/* Primary buttons use your brand color automatically via Bootstrap override */
.btn-primary {
  border-radius: 0.5rem;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(67, 37, 27, 0.3);
}

/* Secondary buttons use your cream color */
.btn-secondary {
  border-radius: 0.5rem;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  color: var(--cw-brand-primary);
  border-color: var(--cw-brand-primary);
}

.btn-secondary:hover {
  background-color: var(--cw-brand-primary);
  border-color: var(--cw-brand-primary);
  color: white;
}

/* ===========================================
   CUSTOM BUTTON VARIANTS
   =========================================== */

/* Outline button with your brand color */
.btn-outline-brand {
  color: var(--cw-brand-primary);
  border-color: var(--cw-brand-primary);
  border-radius: 0.5rem;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
}

.btn-outline-brand:hover {
  background-color: var(--cw-brand-primary);
  border-color: var(--cw-brand-primary);
  color: white;
}

/* Icon buttons */
.btn-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

/* Button sizes */
.btn-xs {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 0.25rem;
}

.btn-xl {
  padding: 1rem 2rem;
  font-size: 1.125rem;
  border-radius: 0.75rem;
}
```

**Add to `static/design-system/main.css`**:
```css
@import './components/buttons.css';
```

**Test your buttons** - Create a test page:
```html
<!-- Test buttons in any template -->
<div class="container py-5">
    <h2>Button Tests</h2>
    
    <!-- Bootstrap buttons with your brand colors -->
    <button class="btn btn-primary me-2">Primary Button</button>
    <button class="btn btn-secondary me-2">Secondary Button</button>
    <button class="btn btn-outline-brand me-2">Outline Brand</button>
    
    <!-- Different sizes -->
    <button class="btn btn-primary btn-xs me-2">Extra Small</button>
    <button class="btn btn-primary me-2">Default</button>
    <button class="btn btn-primary btn-xl me-2">Extra Large</button>
    
    <!-- Icon button -->
    <button class="btn btn-primary btn-icon">
        <i class="fas fa-heart"></i>
    </button>
</div>
```

**What you should have after this step**:
- Beautiful buttons using your brand colors
- Multiple button variants and sizes
- Hover effects and animations

---

### 📋 PROMPT 7: Create Card Components

**Goal**: Build consistent cards for your content

**Create `static/design-system/components/cards.css`**:
```css
/**
 * CozyWish Design System - Card Components
 * Enhanced Bootstrap cards with your brand
 */

/* ===========================================
   CARD ENHANCEMENTS
   =========================================== */

.card {
  border: none;
  border-radius: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  background-color: var(--cw-brand-light);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
  background-color: transparent;
  border-bottom: 1px solid var(--cw-neutral-200);
  font-weight: 600;
  color: var(--cw-brand-primary);
}

.card-footer {
  background-color: transparent;
  border-top: 1px solid var(--cw-neutral-200);
}

/* ===========================================
   CUSTOM CARD VARIANTS
   =========================================== */

/* Brand accent card */
.card-brand {
  background: linear-gradient(135deg, var(--cw-brand-light) 0%, var(--cw-brand-accent) 100%);
  border: 1px solid var(--cw-brand-accent);
}

/* Profile card */
.card-profile {
  text-align: center;
  padding: 2rem 1rem;
}

.card-profile img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid var(--cw-brand-accent);
  margin-bottom: 1rem;
}

/* Service card for your booking system */
.card-service {
  position: relative;
  overflow: hidden;
}

.card-service .card-img-top {
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.card-service:hover .card-img-top {
  transform: scale(1.05);
}

.card-service .badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background-color: var(--cw-brand-primary);
}

/* Stats card for dashboard */
.card-stats {
  background: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-neutral-700) 100%);
  color: white;
  border: none;
}

.card-stats .card-body {
  padding: 2rem;
}

.card-stats h3 {
  color: white;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.card-stats p {
  color: var(--cw-brand-accent);
  margin-bottom: 0;
}
```

**Add to `static/design-system/main.css`**:
```css
@import './components/cards.css';
```

**Test your cards**:
```html
<div class="container py-5">
    <div class="row">
        <!-- Basic card -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Basic Card</h5>
                    <p class="card-text">This is a basic card with your brand styling.</p>
                    <a href="#" class="btn btn-primary">Learn More</a>
                </div>
            </div>
        </div>
        
        <!-- Brand card -->
        <div class="col-md-4 mb-4">
            <div class="card card-brand">
                <div class="card-body">
                    <h5 class="card-title">Brand Card</h5>
                    <p class="card-text">This card uses your brand gradient.</p>
                    <a href="#" class="btn btn-outline-brand">Explore</a>
                </div>
            </div>
        </div>
        
        <!-- Stats card -->
        <div class="col-md-4 mb-4">
            <div class="card card-stats">
                <div class="card-body">
                    <h3>150+</h3>
                    <p>Happy Customers</p>
                </div>
            </div>
        </div>
    </div>
</div>
```

**What you should have after this step**:
- Beautiful cards with your brand colors
- Multiple card variants for different use cases
- Hover effects and gradients

---

### 📝 PROMPT 8: Create Form Components

**Goal**: Build consistent forms using Bootstrap + your brand

**Create `static/design-system/components/forms.css`**:
```css
/**
 * CozyWish Design System - Form Components
 * Enhanced Bootstrap forms with your brand
 */

/* ===========================================
   FORM ENHANCEMENTS
   =========================================== */

.form-control {
  border: 2px solid var(--cw-neutral-200);
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.form-control:focus {
  border-color: var(--cw-brand-primary);
  box-shadow: 0 0 0 0.2rem rgba(67, 37, 27, 0.25);
}

.form-label {
  color: var(--cw-brand-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

/* ===========================================
   CUSTOM FORM COMPONENTS
   =========================================== */

/* Search input with icon */
.input-group-search {
  position: relative;
}

.input-group-search .form-control {
  padding-left: 2.5rem;
}

.input-group-search .input-group-text {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: var(--cw-neutral-400);
  z-index: 10;
}

/* File upload styling */
.form-control[type="file"] {
  padding: 0.5rem;
}

.form-control[type="file"]::-webkit-file-upload-button {
  background-color: var(--cw-brand-primary);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  margin-right: 1rem;
  cursor: pointer;
}

/* Checkbox and radio styling */
.form-check-input:checked {
  background-color: var(--cw-brand-primary);
  border-color: var(--cw-brand-primary);
}

.form-check-input:focus {
  box-shadow: 0 0 0 0.2rem rgba(67, 37, 27, 0.25);
}

.form-check-label {
  color: var(--cw-neutral-700);
}

/* Select styling */
.form-select {
  border: 2px solid var(--cw-neutral-200);
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
}

.form-select:focus {
  border-color: var(--cw-brand-primary);
  box-shadow: 0 0 0 0.2rem rgba(67, 37, 27, 0.25);
}

/* ===========================================
   FORM LAYOUTS
   =========================================== */

/* Booking form specific styling */
.form-booking {
  background-color: var(--cw-brand-light);
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.form-booking .form-label {
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Contact form styling */
.form-contact {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  border: 1px solid var(--cw-neutral-200);
}

/* Form validation styling */
.was-validated .form-control:valid {
  border-color: var(--cw-success);
}

.was-validated .form-control:invalid {
  border-color: var(--cw-error);
}

.valid-feedback {
  color: var(--cw-success);
}

.invalid-feedback {
  color: var(--cw-error);
}
```

**Add to `static/design-system/main.css`**:
```css
@import './components/forms.css';
```

**Test your forms**:
```html
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="form-booking">
                <h3 class="mb-4">Book a Service</h3>
                <form>
                    <div class="mb-3">
                        <label for="service" class="form-label">Service Type</label>
                        <select class="form-select" id="service">
                            <option selected>Choose a service...</option>
                            <option value="1">Massage</option>
                            <option value="2">Facial</option>
                            <option value="3">Spa Package</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="date" class="form-label">Preferred Date</label>
                        <input type="date" class="form-control" id="date">
                    </div>
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Your Name</label>
                        <input type="text" class="form-control" id="name" placeholder="Enter your name">
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" placeholder="Enter your email">
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="terms">
                        <label class="form-check-label" for="terms">
                            I agree to the terms and conditions
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">Book Now</button>
                </form>
            </div>
        </div>
    </div>
</div>
```

**What you should have after this step**:
- Beautiful forms with your brand colors
- Enhanced input styling with focus states
- Form validation styling
- Special booking and contact form layouts

---

### 🧭 PROMPT 9: Create Navigation Components

**Goal**: Build consistent navigation using Bootstrap navbar + your brand

**Create `static/design-system/components/navigation.css`**:
```css
/**
 * CozyWish Design System - Navigation Components
 * Enhanced Bootstrap navigation with your brand
 */

/* ===========================================
   NAVBAR ENHANCEMENTS
   =========================================== */

.navbar {
  background-color: white !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1rem 0;
}

.navbar-brand {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 1.5rem;
  color: var(--cw-brand-primary) !important;
}

.navbar-nav .nav-link {
  color: var(--cw-neutral-700) !important;
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.navbar-nav .nav-link:hover {
  color: var(--cw-brand-primary) !important;
  background-color: var(--cw-brand-light);
}

.navbar-nav .nav-link.active {
  color: var(--cw-brand-primary) !important;
  background-color: var(--cw-brand-accent);
}

/* Mobile navbar styling */
.navbar-toggler {
  border: none;
  padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
  box-shadow: none;
}

.navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2867, 37, 27, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* ===========================================
   BREADCRUMB STYLING
   =========================================== */

.breadcrumb {
  background-color: transparent;
  padding: 0;
  margin-bottom: 2rem;
}

.breadcrumb-item a {
  color: var(--cw-brand-primary);
  text-decoration: none;
}

.breadcrumb-item.active {
  color: var(--cw-neutral-600);
}

.breadcrumb-item + .breadcrumb-item::before {
  content: "›";
  color: var(--cw-neutral-400);
}

/* ===========================================
   SIDEBAR NAVIGATION (for dashboard)
   =========================================== */

.sidebar {
  background-color: white;
  min-height: 100vh;
  border-right: 1px solid var(--cw-neutral-200);
  padding: 2rem 0;
}

.sidebar .nav-link {
  color: var(--cw-neutral-700);
  padding: 0.75rem 1.5rem;
  border-radius: 0;
  border-left: 3px solid transparent;
  transition: all 0.2s ease;
}

.sidebar .nav-link:hover {
  background-color: var(--cw-brand-light);
  border-left-color: var(--cw-brand-accent);
}

.sidebar .nav-link.active {
  background-color: var(--cw-brand-accent);
  color: var(--cw-brand-primary);
  border-left-color: var(--cw-brand-primary);
}

.sidebar .nav-link i {
  width: 20px;
  margin-right: 0.75rem;
}

/* ===========================================
   FOOTER STYLING
   =========================================== */

.footer {
  background-color: var(--cw-brand-primary);
  color: var(--cw-brand-accent);
  padding: 3rem 0 1rem;
  margin-top: auto;
}

.footer h5 {
  color: white;
  margin-bottom: 1rem;
}

.footer a {
  color: var(--cw-brand-accent);
  text-decoration: none;
}

.footer a:hover {
  color: white;
}

.footer .social-links a {
  display: inline-block;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  text-align: center;
  line-height: 40px;
  margin-right: 0.5rem;
  transition: all 0.2s ease;
}

.footer .social-links a:hover {
  background-color: var(--cw-brand-accent);
  color: var(--cw-brand-primary);
}

/* ===========================================
   TABS STYLING
   =========================================== */

.nav-tabs {
  border-bottom: 2px solid var(--cw-neutral-200);
}

.nav-tabs .nav-link {
  color: var(--cw-neutral-600);
  border: none;
  border-bottom: 2px solid transparent;
  border-radius: 0;
  padding: 1rem 1.5rem;
}

.nav-tabs .nav-link:hover {
  border-bottom-color: var(--cw-brand-accent);
  background-color: transparent;
}

.nav-tabs .nav-link.active {
  color: var(--cw-brand-primary);
  border-bottom-color: var(--cw-brand-primary);
  background-color: transparent;
}
```

**Add to `static/design-system/main.css`**:
```css
@import './components/navigation.css';
```

**Update your `templates/base.html`**:
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}CozyWish{% endblock %}</title>
    
    <!-- CozyWish Design System -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'design-system/main.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    {% block extra_css %}{% endblock %}
</head>
<body class="{% block body_class %}{% endblock %}">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="/">CozyWish</a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/services">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/contact">Contact</a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-primary ms-2" href="/book">Book Now</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4">
                    <h5>CozyWish</h5>
                    <p>Your premier destination for wellness and beauty services.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <h5>Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="/">Home</a></li>
                        <li><a href="/services">Services</a></li>
                        <li><a href="/about">About Us</a></li>
                        <li><a href="/contact">Contact</a></li>
                    </ul>
                </div>
                <div class="col-md-4 mb-4">
                    <h5>Contact Info</h5>
                    <p><i class="fas fa-phone me-2"></i> (*************</p>
                    <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    <p><i class="fas fa-map-marker-alt me-2"></i> 123 Wellness St, City</p>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; 2024 CozyWish. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
```

**What you should have after this step**:
- Beautiful navigation with your brand colors
- Responsive mobile menu
- Branded footer
- Sidebar navigation for dashboard
- Tab navigation styling

---

## PHASE 3: BUSINESS-SPECIFIC COMPONENTS (Week 3-4)

### 🏨 PROMPT 10: Create Service & Booking Components

**Goal**: Build components specific to your spa/wellness business

**Create `static/design-system/patterns/booking.css`**:
```css
/**
 * CozyWish Design System - Booking & Service Components
 * Business-specific components for spa/wellness booking
 */

/* ===========================================
   SERVICE CARD COMPONENT
   =========================================== */

.service-card {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.service-card-image {
  height: 200px;
  background-size: cover;
  background-position: center;
  position: relative;
}

.service-card-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--cw-brand-primary);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 600;
}

.service-card-body {
  padding: 1.5rem;
}

.service-card-title {
  color: var(--cw-brand-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.service-card-duration {
  color: var(--cw-neutral-500);
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.service-card-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--cw-brand-primary);
  margin-bottom: 1rem;
}

.service-card-description {
  color: var(--cw-neutral-600);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

/* ===========================================
   BOOKING FORM COMPONENT
   =========================================== */

.booking-form-container {
  background: linear-gradient(135deg, var(--cw-brand-light) 0%, white 100%);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.booking-step {
  display: none;
}

.booking-step.active {
  display: block;
}

.booking-progress {
  margin-bottom: 2rem;
}

.booking-progress-bar {
  height: 4px;
  background: var(--cw-neutral-200);
  border-radius: 2px;
  overflow: hidden;
}

.booking-progress-fill {
  height: 100%;
  background: var(--cw-brand-primary);
  transition: width 0.3s ease;
}

.booking-step-title {
  color: var(--cw-brand-primary);
  margin-bottom: 1.5rem;
}

/* ===========================================
   PROVIDER PROFILE COMPONENT
   =========================================== */

.provider-card {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.provider-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 4px solid var(--cw-brand-accent);
  margin: 0 auto 1rem;
  object-fit: cover;
}

.provider-name {
  color: var(--cw-brand-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.provider-specialty {
  color: var(--cw-neutral-500);
  margin-bottom: 1rem;
}

.provider-rating {
  margin-bottom: 1rem;
}

.provider-rating .star {
  color: #ffc107;
  font-size: 1.1rem;
}

.provider-bio {
  color: var(--cw-neutral-600);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

/* ===========================================
   AVAILABILITY CALENDAR
   =========================================== */

.availability-calendar {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.calendar-nav-btn {
  background: none;
  border: none;
  color: var(--cw-brand-primary);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.calendar-nav-btn:hover {
  background: var(--cw-brand-light);
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.calendar-day:hover {
  background: var(--cw-brand-light);
}

.calendar-day.available {
  background: var(--cw-success);
  color: white;
}

.calendar-day.selected {
  background: var(--cw-brand-primary);
  color: white;
}

.calendar-day.unavailable {
  background: var(--cw-neutral-200);
  color: var(--cw-neutral-400);
  cursor: not-allowed;
}

/* ===========================================
   TIME SLOT SELECTION
   =========================================== */

.time-slots {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
  margin-top: 1rem;
}

.time-slot {
  padding: 0.75rem;
  border: 2px solid var(--cw-neutral-200);
  border-radius: 0.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.time-slot:hover {
  border-color: var(--cw-brand-accent);
  background: var(--cw-brand-light);
}

.time-slot.selected {
  border-color: var(--cw-brand-primary);
  background: var(--cw-brand-primary);
  color: white;
}

.time-slot.unavailable {
  background: var(--cw-neutral-100);
  color: var(--cw-neutral-400);
  cursor: not-allowed;
}

/* ===========================================
   BOOKING SUMMARY
   =========================================== */

.booking-summary {
  background: var(--cw-brand-light);
  border-radius: 1rem;
  padding: 1.5rem;
  border: 1px solid var(--cw-brand-accent);
}

.booking-summary-title {
  color: var(--cw-brand-primary);
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--cw-brand-accent);
}

.booking-summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(67, 37, 27, 0.1);
}

.booking-summary-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.booking-summary-label {
  color: var(--cw-neutral-600);
}

.booking-summary-value {
  color: var(--cw-brand-primary);
  font-weight: 500;
}

.booking-total {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--cw-brand-primary);
}
```

**Add to `static/design-system/main.css`**:
```css
@import './patterns/booking.css';
```

**Test your booking components**:
```html
<div class="container py-5">
    <!-- Service Cards -->
    <div class="row mb-5">
        <div class="col-md-4 mb-4">
            <div class="service-card">
                <div class="service-card-image" style="background-image: url('https://via.placeholder.com/300x200');">
                    <div class="service-card-badge">Popular</div>
                </div>
                <div class="service-card-body">
                    <h5 class="service-card-title">Relaxing Massage</h5>
                    <p class="service-card-duration"><i class="fas fa-clock me-2"></i>60 minutes</p>
                    <div class="service-card-price">$89</div>
                    <p class="service-card-description">A soothing full-body massage to help you relax and unwind.</p>
                    <button class="btn btn-primary w-100">Book Now</button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-4">
            <div class="service-card">
                <div class="service-card-image" style="background-image: url('https://via.placeholder.com/300x200');">
                    <div class="service-card-badge">New</div>
                </div>
                <div class="service-card-body">
                    <h5 class="service-card-title">Facial Treatment</h5>
                    <p class="service-card-duration"><i class="fas fa-clock me-2"></i>45 minutes</p>
                    <div class="service-card-price">$75</div>
                    <p class="service-card-description">Rejuvenating facial treatment for glowing, healthy skin.</p>
                    <button class="btn btn-primary w-100">Book Now</button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-4">
            <div class="provider-card">
                <img src="https://via.placeholder.com/100x100" alt="Provider" class="provider-avatar">
                <h5 class="provider-name">Sarah Johnson</h5>
                <p class="provider-specialty">Licensed Massage Therapist</p>
                <div class="provider-rating">
                    <span class="star">★</span>
                    <span class="star">★</span>
                    <span class="star">★</span>
                    <span class="star">★</span>
                    <span class="star">★</span>
                    <span class="ms-2">5.0 (127 reviews)</span>
                </div>
                <p class="provider-bio">Specializing in deep tissue and Swedish massage with 8 years of experience.</p>
                <button class="btn btn-outline-brand">View Profile</button>
            </div>
        </div>
    </div>
    
    <!-- Booking Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="booking-form-container">
                <h3 class="booking-step-title">Book Your Appointment</h3>
                
                <div class="booking-progress">
                    <div class="booking-progress-bar">
                        <div class="booking-progress-fill" style="width: 33%;"></div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-8">
                        <!-- Service Selection -->
                        <div class="mb-4">
                            <label class="form-label">Select Service</label>
                            <select class="form-select">
                                <option>Relaxing Massage - $89</option>
                                <option>Facial Treatment - $75</option>
                                <option>Spa Package - $150</option>
                            </select>
                        </div>
                        
                        <!-- Date Selection -->
                        <div class="mb-4">
                            <label class="form-label">Select Date</label>
                            <input type="date" class="form-control">
                        </div>
                        
                        <!-- Time Slots -->
                        <div class="mb-4">
                            <label class="form-label">Available Times</label>
                            <div class="time-slots">
                                <div class="time-slot">9:00 AM</div>
                                <div class="time-slot">10:30 AM</div>
                                <div class="time-slot selected">2:00 PM</div>
                                <div class="time-slot">3:30 PM</div>
                                <div class="time-slot unavailable">5:00 PM</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="booking-summary">
                            <h5 class="booking-summary-title">Booking Summary</h5>
                            
                            <div class="booking-summary-item">
                                <span class="booking-summary-label">Service:</span>
                                <span class="booking-summary-value">Relaxing Massage</span>
                            </div>
                            
                            <div class="booking-summary-item">
                                <span class="booking-summary-label">Date:</span>
                                <span class="booking-summary-value">Dec 15, 2024</span>
                            </div>
                            
                            <div class="booking-summary-item">
                                <span class="booking-summary-label">Time:</span>
                                <span class="booking-summary-value">2:00 PM</span>
                            </div>
                            
                            <div class="booking-summary-item">
                                <span class="booking-summary-label">Duration:</span>
                                <span class="booking-summary-value">60 minutes</span>
                            </div>
                            
                            <div class="booking-summary-item">
                                <span class="booking-summary-label booking-total">Total:</span>
                                <span class="booking-summary-value booking-total">$89</span>
                            </div>
                        </div>
                        
                        <button class="btn btn-primary w-100 mt-3">Continue to Payment</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
```

**What you should have after this step**:
- Service cards for displaying spa services
- Provider profile cards
- Booking form with time slot selection
- Booking summary component
- Availability calendar styling

---

## PHASE 4: FINAL INTEGRATION & TESTING (Week 4)

### 🔧 PROMPT 11: Create Dashboard Components

**Goal**: Build dashboard components for business owners and customers

**Create `static/design-system/patterns/dashboard.css`**:
```css
/**
 * CozyWish Design System - Dashboard Components
 * Dashboard layouts and components for admin and customer views
 */

/* ===========================================
   DASHBOARD LAYOUT
   =========================================== */

.dashboard-container {
  min-height: 100vh;
  background: var(--cw-brand-background);
}

.dashboard-sidebar {
  background: white;
  min-height: 100vh;
  border-right: 1px solid var(--cw-neutral-200);
  padding: 0;
}

.dashboard-content {
  padding: 2rem;
  min-height: 100vh;
}

/* ===========================================
   DASHBOARD HEADER
   =========================================== */

.dashboard-header {
  background: white;
  padding: 1rem 2rem;
  border-bottom: 1px solid var(--cw-neutral-200);
  margin-bottom: 2rem;
  border-radius: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.dashboard-title {
  color: var(--cw-brand-primary);
  margin-bottom: 0.5rem;
}

.dashboard-subtitle {
  color: var(--cw-neutral-500);
  margin-bottom: 0;
}

/* ===========================================
   STATS CARDS
   =========================================== */

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stats-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--cw-brand-primary);
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.stats-card-icon {
  width: 50px;
  height: 50px;
  background: var(--cw-brand-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  color: var(--cw-brand-primary);
  font-size: 1.2rem;
}

.stats-card-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--cw-brand-primary);
  margin-bottom: 0.5rem;
}

.stats-card-label {
  color: var(--cw-neutral-600);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.stats-card-change {
  font-size: 0.9rem;
  font-weight: 500;
}

.stats-card-change.positive {
  color: var(--cw-success);
}

.stats-card-change.negative {
  color: var(--cw-error);
}

/* ===========================================
   APPOINTMENT CARDS
   =========================================== */

.appointment-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border-left: 4px solid var(--cw-brand-primary);
  transition: all 0.2s ease;
}

.appointment-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.appointment-title {
  color: var(--cw-brand-primary);
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.appointment-client {
  color: var(--cw-neutral-600);
  font-size: 0.9rem;
}

.appointment-status {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 600;
}

.appointment-status.confirmed {
  background: rgba(5, 150, 105, 0.1);
  color: var(--cw-success);
}

.appointment-status.pending {
  background: rgba(217, 119, 6, 0.1);
  color: var(--cw-warning);
}

.appointment-status.cancelled {
  background: rgba(220, 38, 38, 0.1);
  color: var(--cw-error);
}

.appointment-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.appointment-detail {
  display: flex;
  align-items: center;
  color: var(--cw-neutral-600);
  font-size: 0.9rem;
}

.appointment-detail i {
  margin-right: 0.5rem;
  color: var(--cw-brand-primary);
}

.appointment-actions {
  display: flex;
  gap: 0.5rem;
}

/* ===========================================
   RECENT ACTIVITY
   =========================================== */

.activity-feed {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 1rem 0;
  border-bottom: 1px solid var(--cw-neutral-100);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  background: var(--cw-brand-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: var(--cw-brand-primary);
  font-size: 0.9rem;
}

.activity-content {
  flex: 1;
}

.activity-text {
  color: var(--cw-neutral-700);
  margin-bottom: 0.25rem;
}

.activity-time {
  color: var(--cw-neutral-500);
  font-size: 0.8rem;
}

/* ===========================================
   QUICK ACTIONS
   =========================================== */

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.quick-action-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.quick-action-card:hover {
  transform: translateY(-2px);
  border-color: var(--cw-brand-accent);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.quick-action-icon {
  width: 60px;
  height: 60px;
  background: var(--cw-brand-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  color: var(--cw-brand-primary);
  font-size: 1.5rem;
}

.quick-action-title {
  color: var(--cw-brand-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.quick-action-description {
  color: var(--cw-neutral-600);
  font-size: 0.9rem;
}

/* ===========================================
   RESPONSIVE DASHBOARD
   =========================================== */

@media (max-width: 768px) {
  .dashboard-content {
    padding: 1rem;
  }
  
  .dashboard-header {
    padding: 1rem;
    margin-bottom: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .appointment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .appointment-details {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
  }
}
```

**Add to `static/design-system/main.css`**:
```css
@import './patterns/dashboard.css';
```

**Test your dashboard components**:
```html
<div class="dashboard-container">
    <div class="row g-0">
        <!-- Sidebar -->
        <div class="col-md-3 dashboard-sidebar">
            <div class="p-3">
                <h5 class="text-brand-primary">Dashboard</h5>
            </div>
            <nav class="nav flex-column">
                <a class="nav-link active" href="#"><i class="fas fa-home"></i> Overview</a>
                <a class="nav-link" href="#"><i class="fas fa-calendar"></i> Appointments</a>
                <a class="nav-link" href="#"><i class="fas fa-users"></i> Customers</a>
                <a class="nav-link" href="#"><i class="fas fa-chart-bar"></i> Analytics</a>
                <a class="nav-link" href="#"><i class="fas fa-cog"></i> Settings</a>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 dashboard-content">
            <!-- Dashboard Header -->
            <div class="dashboard-header">
                <h2 class="dashboard-title">Welcome back, Sarah!</h2>
                <p class="dashboard-subtitle">Here's what's happening with your business today.</p>
            </div>
            
            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stats-card">
                    <div class="stats-card-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="stats-card-value">24</div>
                    <div class="stats-card-label">Today's Appointments</div>
                    <div class="stats-card-change positive">
                        <i class="fas fa-arrow-up"></i> +12% from yesterday
                    </div>
                </div>
                
                <div class="stats-card">
                    <div class="stats-card-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stats-card-value">$2,450</div>
                    <div class="stats-card-label">Today's Revenue</div>
                    <div class="stats-card-change positive">
                        <i class="fas fa-arrow-up"></i> +8% from yesterday
                    </div>
                </div>
                
                <div class="stats-card">
                    <div class="stats-card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-card-value">156</div>
                    <div class="stats-card-label">Total Customers</div>
                    <div class="stats-card-change positive">
                        <i class="fas fa-arrow-up"></i> +3 new this week
                    </div>
                </div>
                
                <div class="stats-card">
                    <div class="stats-card-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stats-card-value">4.9</div>
                    <div class="stats-card-label">Average Rating</div>
                    <div class="stats-card-change positive">
                        <i class="fas fa-arrow-up"></i> +0.1 this month
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="quick-actions">
                <div class="quick-action-card">
                    <div class="quick-action-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <h6 class="quick-action-title">New Appointment</h6>
                    <p class="quick-action-description">Schedule a new appointment for a customer</p>
                </div>
                
                <div class="quick-action-card">
                    <div class="quick-action-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h6 class="quick-action-title">Add Customer</h6>
                    <p class="quick-action-description">Register a new customer profile</p>
                </div>
                
                <div class="quick-action-card">
                    <div class="quick-action-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h6 class="quick-action-title">View Reports</h6>
                    <p class="quick-action-description">Check business analytics and reports</p>
                </div>
            </div>
            
            <!-- Recent Appointments -->
            <div class="row">
                <div class="col-lg-8">
                    <h4 class="mb-3">Today's Appointments</h4>
                    
                    <div class="appointment-card">
                        <div class="appointment-header">
                            <div>
                                <h6 class="appointment-title">Relaxing Massage</h6>
                                <p class="appointment-client">John Smith</p>
                            </div>
                            <span class="appointment-status confirmed">Confirmed</span>
                        </div>
                        <div class="appointment-details">
                            <div class="appointment-detail">
                                <i class="fas fa-clock"></i> 2:00 PM - 3:00 PM
                            </div>
                            <div class="appointment-detail">
                                <i class="fas fa-user"></i> Sarah Johnson
                            </div>
                            <div class="appointment-detail">
                                <i class="fas fa-dollar-sign"></i> $89
                            </div>
                        </div>
                        <div class="appointment-actions">
                            <button class="btn btn-sm btn-outline-brand">View Details</button>
                            <button class="btn btn-sm btn-primary">Check In</button>
                        </div>
                    </div>
                    
                    <div class="appointment-card">
                        <div class="appointment-header">
                            <div>
                                <h6 class="appointment-title">Facial Treatment</h6>
                                <p class="appointment-client">Emily Davis</p>
                            </div>
                            <span class="appointment-status pending">Pending</span>
                        </div>
                        <div class="appointment-details">
                            <div class="appointment-detail">
                                <i class="fas fa-clock"></i> 3:30 PM - 4:15 PM
                            </div>
                            <div class="appointment-detail">
                                <i class="fas fa-user"></i> Maria Garcia
                            </div>
                            <div class="appointment-detail">
                                <i class="fas fa-dollar-sign"></i> $75
                            </div>
                        </div>
                        <div class="appointment-actions">
                            <button class="btn btn-sm btn-outline-brand">View Details</button>
                            <button class="btn btn-sm btn-secondary">Confirm</button>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="activity-feed">
                        <h5 class="mb-3">Recent Activity</h5>
                        
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-calendar-plus"></i>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">New appointment booked by John Smith</p>
                                <p class="activity-time">2 minutes ago</p>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">New 5-star review from Emily Davis</p>
                                <p class="activity-time">1 hour ago</p>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">New customer registered: Mike Johnson</p>
                                <p class="activity-time">3 hours ago</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
```

**What you should have after this step**:
- Complete dashboard layout with sidebar
- Statistics cards with your brand colors
- Appointment management cards
- Activity feed component
- Quick action cards
- Responsive design for mobile

---

### ✅ PROMPT 12: Final Testing & Documentation

**Goal**: Test everything and create usage documentation

**Create a test page to verify all components**:

**Create `test_components.html`**:
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CozyWish Design System Test</title>
    
    <!-- Your Design System -->
    <link rel="stylesheet" href="static/design-system/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Test all components here -->
    <div class="container py-5">
        <h1 class="text-center mb-5">CozyWish Design System Test</h1>
        
        <!-- Test Buttons -->
        <section class="mb-5">
            <h2>Buttons</h2>
            <button class="btn btn-primary me-2">Primary</button>
            <button class="btn btn-secondary me-2">Secondary</button>
            <button class="btn btn-outline-brand me-2">Outline Brand</button>
            <button class="btn btn-primary btn-icon"><i class="fas fa-heart"></i></button>
        </section>
        
        <!-- Test Cards -->
        <section class="mb-5">
            <h2>Cards</h2>
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Basic Card</h5>
                            <p class="card-text">This is a test card.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card card-brand">
                        <div class="card-body">
                            <h5 class="card-title">Brand Card</h5>
                            <p class="card-text">This uses your brand colors.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Test Forms -->
        <section class="mb-5">
            <h2>Forms</h2>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-booking">
                        <div class="mb-3">
                            <label class="form-label">Test Input</label>
                            <input type="text" class="form-control" placeholder="Enter text">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Test Select</label>
                            <select class="form-select">
                                <option>Option 1</option>
                                <option>Option 2</option>
                            </select>
                        </div>
                        <button class="btn btn-primary">Submit</button>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Test Colors -->
        <section class="mb-5">
            <h2>Brand Colors</h2>
            <div class="row">
                <div class="col-3">
                    <div class="p-3 bg-brand-primary text-white text-center rounded">Primary<br>#43251B</div>
                </div>
                <div class="col-3">
                    <div class="p-3 bg-brand-accent text-center rounded">Accent<br>#FAE1D7</div>
                </div>
                <div class="col-3">
                    <div class="p-3 bg-brand-light text-center rounded">Light<br>#FFF9F4</div>
                </div>
                <div class="col-3">
                    <div class="p-3 bg-brand-background text-center rounded border">Background<br>#F8F9FA</div>
                </div>
            </div>
        </section>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
```

**Create usage documentation**:

**Create `COMPONENT_USAGE.md`**:
```markdown
# CozyWish Design System - Component Usage Guide

## Quick Start

1. Include the main CSS file:
```html
<link rel="stylesheet" href="static/design-system/main.css">
```

2. Include Font Awesome for icons:
```html
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
```

3. Include Bootstrap JavaScript:
```html
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
```

## Your Brand Colors

Use these CSS classes for your brand colors:

- `.text-brand-primary` - Main brown text (#43251B)
- `.text-brand-accent` - Cream text (#FAE1D7)
- `.bg-brand-primary` - Main brown background
- `.bg-brand-accent` - Cream background
- `.bg-brand-light` - Light cream background (#FFF9F4)
- `.bg-brand-background` - Main page background (#F8F9FA)

## Bootstrap Components with Your Brand

All Bootstrap components automatically use your brand colors:

```html
<!-- These buttons use your brand colors -->
<button class="btn btn-primary">Your Brand Button</button>
<button class="btn btn-secondary">Your Cream Button</button>

<!-- Cards use your background colors -->
<div class="card">
  <div class="card-body">
    Content automatically styled with your brand
  </div>
</div>
```

## Custom Components

### Service Cards
```html
<div class="service-card">
  <div class="service-card-image" style="background-image: url('image.jpg');">
    <div class="service-card-badge">Popular</div>
  </div>
  <div class="service-card-body">
    <h5 class="service-card-title">Service Name</h5>
    <p class="service-card-duration"><i class="fas fa-clock me-2"></i>60 minutes</p>
    <div class="service-card-price">$89</div>
    <p class="service-card-description">Description here.</p>
    <button class="btn btn-primary w-100">Book Now</button>
  </div>
</div>
```

### Booking Forms
```html
<div class="booking-form-container">
  <h3>Book Your Appointment</h3>
  <!-- Your form fields here -->
</div>
```

### Dashboard Stats
```html
<div class="stats-card">
  <div class="stats-card-icon">
    <i class="fas fa-calendar"></i>
  </div>
  <div class="stats-card-value">24</div>
  <div class="stats-card-label">Appointments</div>
  <div class="stats-card-change positive">
    <i class="fas fa-arrow-up"></i> +12%
  </div>
</div>
```

## Do's and Don'ts

### ✅ Do:
- Use your brand color classes
- Use Bootstrap components when possible
- Follow the component structure
- Test on mobile devices

### ❌ Don't:
- Override component styles with inline CSS
- Use hardcoded colors
- Mix different design patterns
- Forget to include Font Awesome for icons
```

**Final checklist**:
- [ ] All components display correctly
- [ ] Brand colors are consistent throughout
- [ ] Mobile responsiveness works
- [ ] Bootstrap integration is seamless
- [ ] All custom components follow the design system
- [ ] Documentation is complete

**What you should have after this step**:
- Fully functional design system
- All components tested and working
- Complete documentation
- Consistent brand colors throughout
- Mobile-responsive design
- Bootstrap 5 integration

---

## 🎉 CONGRATULATIONS!

You now have a complete, professional design system for CozyWish that includes:

✅ **Your exact brand colors** (#43251B, #FAE1D7, #FFF9F4, #F8F9FA)
✅ **Bootstrap 5 integration** with your brand colors
✅ **50+ components** for your spa/wellness business
✅ **Mobile-responsive design**
✅ **Consistent styling** across all pages
✅ **Easy maintenance** - change colors in one place
✅ **Professional documentation**

### Next Steps:
1. Replace your old CSS files with the new design system
2. Update your templates to use the new components
3. Test everything thoroughly
4. Train your team on the new system
5. Enjoy your beautiful, consistent website!

### Support:
- Keep this guide for reference
- Test new components before adding them
- Maintain consistency with your brand colors
- Document any custom additions

**Your design system is now ready for production!** 🚀 