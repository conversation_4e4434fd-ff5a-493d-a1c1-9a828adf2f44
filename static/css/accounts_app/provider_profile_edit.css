/* CozyWish Design System - Service Provider Profile Edit */
:root {
    /* Brand Colors */
    --cw-brand-primary: #2F160F;
    --cw-brand-light: #4a2a1f;
    --cw-brand-accent: #fae1d7;
    --cw-accent-light: #fef7f0;
    --cw-accent-dark: #f1d4c4;

    /* Neutral Colors */
    --cw-neutral-600: #525252;
    --cw-neutral-700: #404040;
    --cw-neutral-800: #262626;

    /* Typography */
    --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

    /* Shadows */
    --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Gradients */
    --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
    --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
    --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
}

/* Global Styles */
body {
    font-family: var(--cw-font-primary);
    line-height: 1.6;
    color: var(--cw-neutral-800);
    background: white;
    min-height: 100vh;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--cw-font-heading);
    font-weight: 600;
    color: var(--cw-brand-primary);
    line-height: 1.3;
}

/* Edit Profile Section */
.edit-profile-section {
    padding: 5rem 0;
    background: white;
    min-height: 100vh;
}

.edit-profile-container {
    max-width: 800px;
    width: 100%;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Header */
.profile-edit-header {
    background: var(--cw-gradient-card-subtle);
    padding: 3rem 2rem 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    border-radius: 1rem 1rem 0 0;
}

.profile-edit-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="edit-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="%23f1d4c4" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23edit-pattern)"/></svg>') repeat;
    opacity: 0.6;
    z-index: 1;
}

.profile-edit-header .content {
    position: relative;
    z-index: 2;
}

.edit-icon {
    width: 80px;
    height: 80px;
    background: var(--cw-gradient-brand-button);
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--cw-shadow-md);
}

.edit-title {
    font-family: var(--cw-font-display);
    font-size: 2.75rem;
    font-weight: 700;
    color: var(--cw-brand-primary);
    margin-bottom: 1rem;
    line-height: 1.2;
}

.edit-subtitle {
    font-size: 1.25rem;
    color: var(--cw-neutral-600);
    margin-bottom: 0;
    line-height: 1.6;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* Form Cards */
.form-card {
    border: 1px solid rgba(250, 225, 215, 0.3);
    border-radius: 1rem;
    box-shadow: var(--cw-shadow-md);
    background: white;
    overflow: hidden;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.form-card:hover {
    box-shadow: var(--cw-shadow-lg);
    border-color: var(--cw-brand-accent);
}

.form-card-header {
    background: var(--cw-gradient-card-subtle);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--cw-brand-accent);
}

.form-card-title {
    font-family: var(--cw-font-heading);
    font-size: 1.375rem;
    font-weight: 600;
    color: var(--cw-brand-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.form-card-title i {
    color: var(--cw-brand-primary);
    font-size: 1.25rem;
}

.form-card-body {
    padding: 2rem;
}

/* Form Styling */
.form-label {
    font-family: var(--cw-font-heading);
    font-weight: 600;
    color: var(--cw-brand-primary);
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.form-control,
.form-select {
    border: 2px solid var(--cw-brand-accent);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    font-family: var(--cw-font-primary);
    transition: all 0.2s ease;
    background: white;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--cw-brand-primary);
    box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    outline: none;
}

.form-control::placeholder {
    color: var(--cw-neutral-600);
    opacity: 0.7;
}

.form-text {
    color: var(--cw-neutral-600);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.invalid-feedback {
    color: #dc2626;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 0.375rem;
    display: block !important; /* Ensure visible even without JS */
}

.invalid-feedback::before {
    content: "\f071"; /* FontAwesome exclamation-triangle */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    margin-right: 0.5rem;
    color: #dc2626;
}

/* Ensure form controls with errors are highlighted */
.form-control.is-invalid,
.form-select.is-invalid {
    border-color: #dc2626;
    box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.1);
}

/* Non-field errors styling */
.alert-danger {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    color: white;
    border: none;
    border-radius: 0.75rem;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    font-family: var(--cw-font-primary);
    font-weight: 500;
}

.alert-danger::before {
    content: "\f071"; /* FontAwesome exclamation-triangle */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    margin-right: 0.75rem;
}

/* Logo Preview */
.logo-preview-container {
    text-align: center;
    margin-bottom: 1rem;
}

.logo-preview {
    max-width: 200px;
    max-height: 200px;
    border-radius: 1rem;
    border: 3px solid var(--cw-brand-accent);
    box-shadow: var(--cw-shadow-md);
    transition: all 0.3s ease;
}

.logo-preview:hover {
    border-color: var(--cw-brand-primary);
    transform: scale(1.02);
}

/* Checkbox Styling */
.form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--cw-brand-accent);
    border-radius: 0.25rem;
    background-color: white;
    transition: all 0.2s ease;
}

.form-check-input:checked {
    background-color: var(--cw-brand-primary);
    border-color: var(--cw-brand-primary);
}

.form-check-input:focus {
    border-color: var(--cw-brand-primary);
    box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
}

.form-check-label {
    font-family: var(--cw-font-heading);
    font-weight: 500;
    color: var(--cw-brand-primary);
    margin-left: 0.5rem;
}

/* Button Styling */
.btn-cw-primary {
    background: var(--cw-gradient-brand-button);
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    padding: 1rem 2rem;
    color: white;
    transition: all 0.2s ease;
    box-shadow: var(--cw-shadow-sm);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    font-family: var(--cw-font-heading);
    letter-spacing: 0.025em;
    gap: 0.5rem;
}

.btn-cw-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
    color: white;
    text-decoration: none;
}

.btn-cw-secondary {
    border: 2px solid var(--cw-brand-primary);
    color: var(--cw-brand-primary);
    border-radius: 0.5rem;
    font-weight: 600;
    padding: 1rem 2rem;
    background: white;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    font-family: var(--cw-font-heading);
    letter-spacing: 0.025em;
    gap: 0.5rem;
}

.btn-cw-secondary:hover {
    background: var(--cw-brand-primary);
    color: white;
    transform: translateY(-2px);
    text-decoration: none;
    box-shadow: var(--cw-shadow-md);
}

/* Action Buttons */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    padding: 2rem 0;
    border-top: 2px solid var(--cw-brand-accent);
    margin-top: 2rem;
}

/* Alert Styling */
.alert {
    border: none;
    border-radius: 0.75rem;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    font-family: var(--cw-font-primary);
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    color: white;
}

.alert-info {
    background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .edit-profile-section {
        padding: 3rem 0;
    }

    .edit-profile-container {
        padding: 0 1.5rem;
    }

    .profile-edit-header {
        padding: 2rem 2rem 1.5rem;
    }

    .edit-title {
        font-size: 2.25rem;
    }

    .edit-subtitle {
        font-size: 1.125rem;
    }

    .edit-profile-body {
        padding: 2rem;
    }

    .form-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn-cw-primary,
    .btn-cw-secondary {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .edit-profile-container {
        padding: 0 1rem;
    }

    .profile-edit-header {
        padding: 1.5rem 1.5rem 1rem;
    }

    .edit-profile-body {
        padding: 1.5rem;
    }

    .form-card-body {
        padding: 1.5rem;
    }

    .edit-title {
        font-size: 1.875rem;
    }

    .edit-icon {
        width: 64px;
        height: 64px;
        font-size: 1.5rem;
    }

    /* Mobile toast notifications */
    .toast-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .toast-notification {
        margin-bottom: 0.5rem;
    }

    .toast-header {
        padding: 0.75rem 1rem 0.25rem;
    }

    .toast-body {
        padding: 0.25rem 1rem 0.75rem;
        font-size: 0.875rem;
    }
}

/* Toast Notification Styling */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
}

.toast-notification {
    background: white;
    border-radius: 0.75rem;
    box-shadow: var(--cw-shadow-lg);
    border: 1px solid var(--cw-brand-accent);
    margin-bottom: 0.75rem;
    overflow: hidden;
    transform: translateX(100%);
    transition: all 0.3s ease;
    opacity: 0;
}

.toast-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.toast-notification.error {
    border-left: 4px solid #dc2626;
}

.toast-notification.success {
    border-left: 4px solid #059669;
}

.toast-notification.warning {
    border-left: 4px solid #d97706;
}

.toast-header {
    padding: 1rem 1.5rem 0.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--cw-brand-accent);
}

.toast-title {
    font-weight: 600;
    color: var(--cw-brand-primary);
    font-family: var(--cw-font-heading);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.toast-close {
    background: none;
    border: none;
    color: var(--cw-neutral-600);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.toast-close:hover {
    background: var(--cw-brand-accent);
    color: var(--cw-brand-primary);
}

.toast-body {
    padding: 0.5rem 1.5rem 1rem;
    color: var(--cw-neutral-700);
    line-height: 1.5;
}

/* Inline Error Styling */
.field-error {
    color: #dc2626;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.field-error.show {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
} 