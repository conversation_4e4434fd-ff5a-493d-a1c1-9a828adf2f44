"""
Privacy settings and verification forms for accounts_app.

This module contains forms for managing user privacy settings,
data export functionality, and verification badge management.
"""

from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# Crispy Forms imports
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field, Div, HTML, Submit, Row, Column
from crispy_forms.bootstrap import FormActions

# Local imports
from ..models import ProfilePrivacySettings, ProfileVerification, CustomUser
from .common import AccessibleFormMixin


class PrivacySettingsForm(AccessibleFormMixin, forms.ModelForm):
    """
    Form for managing user privacy settings.
    """
    
    class Meta:
        model = ProfilePrivacySettings
        fields = [
            'profile_visibility',
            'show_email',
            'show_phone',
            'show_address',
            'show_birth_date',
            'show_gender',
            'show_profile_picture',
            'show_last_seen',
            'show_booking_history',
            'show_reviews',
            'show_favorites',
            'discoverable_in_search',
            'allow_friend_requests',
            'allow_messages',
            'allow_analytics',
            'allow_personalized_ads',
            'data_processing_consent',
            'marketing_consent',
            'third_party_sharing',
        ]
        widgets = {
            'profile_visibility': forms.Select(
                attrs={
                    'class': 'form-select',
                    'aria-describedby': 'profile-visibility-help'
                }
            ),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Configure form helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'privacy-settings-form'
        
        # Group fields by category
        self.helper.layout = Layout(
            HTML('<h4>Profile Visibility</h4>'),
            Div(
                Field('profile_visibility'),
                HTML('<small id="profile-visibility-help" class="form-text text-muted">Control who can see your profile</small>'),
                css_class='mb-4'
            ),
            
            HTML('<h4>Profile Information</h4>'),
            Div(
                Row(
                    Column('show_email', css_class='form-group col-md-6 mb-3'),
                    Column('show_phone', css_class='form-group col-md-6 mb-3'),
                    css_class='form-row'
                ),
                Row(
                    Column('show_address', css_class='form-group col-md-6 mb-3'),
                    Column('show_birth_date', css_class='form-group col-md-6 mb-3'),
                    css_class='form-row'
                ),
                Row(
                    Column('show_gender', css_class='form-group col-md-6 mb-3'),
                    Column('show_profile_picture', css_class='form-group col-md-6 mb-3'),
                    css_class='form-row'
                ),
                Field('show_last_seen', css_class='form-group mb-3'),
                css_class='mb-4'
            ),
            
            HTML('<h4>Activity & Interactions</h4>'),
            Div(
                Row(
                    Column('show_booking_history', css_class='form-group col-md-6 mb-3'),
                    Column('show_reviews', css_class='form-group col-md-6 mb-3'),
                    css_class='form-row'
                ),
                Field('show_favorites', css_class='form-group mb-3'),
                css_class='mb-4'
            ),
            
            HTML('<h4>Search & Discovery</h4>'),
            Div(
                Row(
                    Column('discoverable_in_search', css_class='form-group col-md-6 mb-3'),
                    Column('allow_friend_requests', css_class='form-group col-md-6 mb-3'),
                    css_class='form-row'
                ),
                Field('allow_messages', css_class='form-group mb-3'),
                css_class='mb-4'
            ),
            
            HTML('<h4>Data & Analytics</h4>'),
            Div(
                Row(
                    Column('allow_analytics', css_class='form-group col-md-6 mb-3'),
                    Column('allow_personalized_ads', css_class='form-group col-md-6 mb-3'),
                    css_class='form-row'
                ),
                css_class='mb-4'
            ),
            
            HTML('<h4>GDPR Compliance</h4>'),
            Div(
                Row(
                    Column('data_processing_consent', css_class='form-group col-md-6 mb-3'),
                    Column('marketing_consent', css_class='form-group col-md-6 mb-3'),
                    css_class='form-row'
                ),
                Field('third_party_sharing', css_class='form-group mb-3'),
                css_class='mb-4'
            ),
            
            FormActions(
                Submit('save', _('Save Privacy Settings'), css_class='btn btn-primary'),
                css_class='d-grid gap-2'
            )
        )
        
        # Add Bootstrap classes to checkboxes
        for field_name in self.fields:
            field = self.fields[field_name]
            if isinstance(field.widget, forms.CheckboxInput):
                field.widget.attrs.update({
                    'class': 'form-check-input',
                    'role': 'switch'
                })
    
    def clean(self):
        cleaned_data = super().clean()
        
        # Validate GDPR compliance
        if not cleaned_data.get('data_processing_consent'):
            raise ValidationError(
                _('Data processing consent is required to use this service.'),
                code='required_consent'
            )
        
        return cleaned_data


class DataExportForm(AccessibleFormMixin, forms.Form):
    """
    Form for requesting data export.
    """
    
    EXPORT_FORMAT_CHOICES = [
        ('json', _('JSON - Machine readable format')),
        ('csv', _('CSV - Spreadsheet format')),
        ('xml', _('XML - Structured format')),
    ]
    
    export_format = forms.ChoiceField(
        choices=EXPORT_FORMAT_CHOICES,
        initial='json',
        widget=forms.RadioSelect(
            attrs={
                'class': 'form-check-input'
            }
        ),
        label=_('Export Format'),
        help_text=_('Choose the format for your data export')
    )
    
    include_deleted_data = forms.BooleanField(
        required=False,
        initial=False,
        label=_('Include deleted data'),
        help_text=_('Include data that has been marked for deletion but not yet purged'),
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
                'role': 'switch'
            }
        )
    )
    
    confirm_export = forms.BooleanField(
        required=True,
        label=_('I confirm that I want to export my data'),
        help_text=_('This will generate a file containing all your personal data'),
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
                'required': True
            }
        )
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'data-export-form'
        
        self.helper.layout = Layout(
            HTML('<div class="alert alert-info">'),
            HTML('<h5>Data Export Information</h5>'),
            HTML('<p>Your data export will include:</p>'),
            HTML('<ul>'),
            HTML('<li>Account information</li>'),
            HTML('<li>Profile data</li>'),
            HTML('<li>Privacy settings</li>'),
            HTML('<li>Activity history</li>'),
            HTML('<li>Verification data</li>'),
            HTML('</ul>'),
            HTML('</div>'),
            
            Field('export_format', css_class='mb-3'),
            Field('include_deleted_data', css_class='mb-3'),
            Field('confirm_export', css_class='mb-3'),
            
            FormActions(
                Submit('export', _('Export My Data'), css_class='btn btn-primary'),
                css_class='d-grid gap-2'
            )
        )


class VerificationRequestForm(AccessibleFormMixin, forms.ModelForm):
    """
    Form for requesting profile verification.
    """
    
    class Meta:
        model = ProfileVerification
        fields = ['verification_type', 'verification_document']
        widgets = {
            'verification_type': forms.Select(
                attrs={
                    'class': 'form-select',
                    'aria-describedby': 'verification-type-help'
                }
            ),
            'verification_document': forms.FileInput(
                attrs={
                    'class': 'form-control',
                    'accept': '.pdf,.jpg,.jpeg,.png,.gif',
                    'aria-describedby': 'verification-document-help'
                }
            )
        }
    
    confirm_accuracy = forms.BooleanField(
        required=True,
        label=_('I confirm that all information provided is accurate'),
        help_text=_('False information may result in account suspension'),
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
                'required': True
            }
        )
    )
    
    def __init__(self, user=None, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)
        
        # Filter verification types based on user type and existing verifications
        if user:
            self._filter_verification_types(user)
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'verification-request-form'
        self.helper.form_enctype = 'multipart/form-data'
        
        self.helper.layout = Layout(
            HTML('<div class="alert alert-warning">'),
            HTML('<h5>Verification Requirements</h5>'),
            HTML('<p>Please ensure that:</p>'),
            HTML('<ul>'),
            HTML('<li>All information is accurate and up to date</li>'),
            HTML('<li>Documents are clear and legible</li>'),
            HTML('<li>Files are in PDF, JPG, PNG, or GIF format</li>'),
            HTML('<li>File size is under 5MB</li>'),
            HTML('</ul>'),
            HTML('</div>'),
            
            Field('verification_type'),
            HTML('<small id="verification-type-help" class="form-text text-muted">Select the type of verification you want to request</small>'),
            
            Field('verification_document', css_class='mb-3'),
            HTML('<small id="verification-document-help" class="form-text text-muted">Upload supporting documents (PDF, JPG, PNG, GIF - max 5MB)</small>'),
            
            Field('confirm_accuracy', css_class='mb-3'),
            
            FormActions(
                Submit('submit', _('Submit Verification Request'), css_class='btn btn-primary'),
                css_class='d-grid gap-2'
            )
        )
    
    def _filter_verification_types(self, user):
        """Filter verification types based on user and existing verifications."""
        available_types = []
        
        # Get existing verifications
        existing_verifications = ProfileVerification.objects.filter(
            user=user,
            status__in=['verified', 'pending']
        ).values_list('verification_type', flat=True)
        
        # Email verification - always available if not already verified
        if 'email' not in existing_verifications:
            available_types.append(('email', _('Email Verification')))
        
        # Phone verification - available if user has phone number
        if 'phone' not in existing_verifications:
            has_phone = False
            if hasattr(user, 'customer_profile') and user.customer_profile.phone_number:
                has_phone = True
            elif hasattr(user, 'service_provider_profile') and user.service_provider_profile.phone:
                has_phone = True
            
            if has_phone:
                available_types.append(('phone', _('Phone Verification')))
        
        # Identity verification - always available
        if 'identity' not in existing_verifications:
            available_types.append(('identity', _('Identity Verification')))
        
        # Business verification - for service providers only
        if user.is_service_provider and 'business' not in existing_verifications:
            available_types.append(('business', _('Business Verification')))
        
        # Address verification - always available
        if 'address' not in existing_verifications:
            available_types.append(('address', _('Address Verification')))
        
        # Payment verification - always available
        if 'payment' not in existing_verifications:
            available_types.append(('payment', _('Payment Method Verification')))
        
        # Update field choices
        self.fields['verification_type'].choices = available_types
        
        if not available_types:
            self.fields['verification_type'].widget.attrs['disabled'] = True
            self.fields['verification_type'].help_text = _('No verification types available.')
    
    def clean_verification_document(self):
        """Validate uploaded document."""
        document = self.cleaned_data.get('verification_document')
        
        if document:
            # Check file size (5MB limit)
            if document.size > 5 * 1024 * 1024:
                raise ValidationError(
                    _('File size must be under 5MB.'),
                    code='file_too_large'
                )
            
            # Check file type
            allowed_types = ['application/pdf', 'image/jpeg', 'image/png', 'image/gif']
            if document.content_type not in allowed_types:
                raise ValidationError(
                    _('File must be PDF, JPG, PNG, or GIF format.'),
                    code='invalid_file_type'
                )
        
        return document


class DataDeletionRequestForm(AccessibleFormMixin, forms.Form):
    """
    Form for requesting data deletion (GDPR right to be forgotten).
    """
    
    confirm_email = forms.EmailField(
        label=_('Confirm your email address'),
        help_text=_('Enter your email address to confirm data deletion request'),
        widget=forms.EmailInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter your email address'),
                'autocomplete': 'email'
            }
        )
    )
    
    deletion_reason = forms.CharField(
        label=_('Reason for deletion (optional)'),
        help_text=_('Please let us know why you want to delete your data'),
        required=False,
        widget=forms.Textarea(
            attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': _('Optional: Tell us why you want to delete your data')
            }
        )
    )
    
    understand_consequences = forms.BooleanField(
        required=True,
        label=_('I understand that this action cannot be undone'),
        help_text=_('Once deleted, your data cannot be recovered'),
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
                'required': True
            }
        )
    )
    
    confirm_deletion = forms.BooleanField(
        required=True,
        label=_('I confirm that I want to delete all my data'),
        help_text=_('This will permanently remove all your personal data'),
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
                'required': True
            }
        )
    )
    
    def __init__(self, user=None, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'data-deletion-form'
        
        self.helper.layout = Layout(
            HTML('<div class="alert alert-danger">'),
            HTML('<h5>⚠️ Warning: Data Deletion Request</h5>'),
            HTML('<p>This action will permanently delete all your personal data including:</p>'),
            HTML('<ul>'),
            HTML('<li>Account information</li>'),
            HTML('<li>Profile data</li>'),
            HTML('<li>Activity history</li>'),
            HTML('<li>Verification data</li>'),
            HTML('<li>Privacy settings</li>'),
            HTML('</ul>'),
            HTML('<p><strong>This action cannot be undone!</strong></p>'),
            HTML('</div>'),
            
            Field('confirm_email', css_class='mb-3'),
            Field('deletion_reason', css_class='mb-3'),
            Field('understand_consequences', css_class='mb-3'),
            Field('confirm_deletion', css_class='mb-3'),
            
            FormActions(
                Submit('delete', _('Delete My Data'), css_class='btn btn-danger'),
                css_class='d-grid gap-2'
            )
        )
    
    def clean_confirm_email(self):
        """Validate that email matches the user's email."""
        email = self.cleaned_data.get('confirm_email')
        
        if email and self.user and email != self.user.email:
            raise ValidationError(
                _('Email address does not match your account email.'),
                code='email_mismatch'
            )
        
        return email


class PrivacyQuickToggleForm(AccessibleFormMixin, forms.Form):
    """
    Form for quick privacy setting toggles via AJAX.
    """
    
    field_name = forms.CharField(
        max_length=50,
        widget=forms.HiddenInput()
    )
    
    current_value = forms.BooleanField(
        required=False,
        widget=forms.HiddenInput()
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'privacy-quick-toggle-form'
        
        self.helper.layout = Layout(
            Field('field_name'),
            Field('current_value'),
        )
    
    def clean_field_name(self):
        """Validate field name."""
        field_name = self.cleaned_data.get('field_name')
        
        # List of valid privacy setting fields
        valid_fields = [
            'show_email', 'show_phone', 'show_address', 'show_birth_date',
            'show_gender', 'show_profile_picture', 'show_last_seen',
            'show_booking_history', 'show_reviews', 'show_favorites',
            'discoverable_in_search', 'allow_friend_requests', 'allow_messages',
            'allow_analytics', 'allow_personalized_ads', 'marketing_consent',
            'third_party_sharing'
        ]
        
        if field_name not in valid_fields:
            raise ValidationError(
                _('Invalid privacy setting field.'),
                code='invalid_field'
            )
        
        return field_name 