"""Database models for :mod:`notifications_app`."""

# --- Standard Library Imports ---
import os
from datetime import timedelta

# --- Third-Party Imports ---
from django.contrib.auth import get_user_model
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _


# Get the user model
User = get_user_model()


class Notification(models.Model):
    """Model to store notifications for users."""

    # Notification type choices
    BOOKING = 'booking'
    PAYMENT = 'payment'
    REVIEW = 'review'
    ANNOUNCEMENT = 'announcement'
    SYSTEM = 'system'

    TYPE_CHOICES = [
        (BOOKING, 'Booking'),
        (PAYMENT, 'Payment'),
        (REVIEW, 'Review'),
        (ANNOUNCEMENT, 'Announcement'),
        (SYSTEM, 'System'),
    ]

    # Read status choices
    UNREAD = 'unread'
    READ = 'read'

    STATUS_CHOICES = [
        (UNREAD, 'Unread'),
        (READ, 'Read'),
    ]

    # Core fields
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='notifications',
        help_text=_('User who will receive this notification')
    )
    notification_type = models.CharField(
        max_length=20,
        choices=TYPE_CHOICES,
        help_text=_('Type of notification')
    )
    title = models.CharField(
        max_length=255,
        help_text=_('Notification title')
    )
    message = models.TextField(
        help_text=_('Notification message content')
    )
    read_status = models.CharField(
        max_length=10,
        choices=STATUS_CHOICES,
        default=UNREAD,
        help_text=_('Whether the notification has been read')
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the notification was read')
    )

    # Optional fields for linking to related objects
    related_object_id = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text=_('ID of related object (booking, payment, etc.)')
    )
    related_object_type = models.CharField(
        max_length=50,
        blank=True,
        help_text=_('Type of related object')
    )
    action_url = models.URLField(
        blank=True,
        help_text=_('URL for notification action (optional)')
    )

    class Meta:
        verbose_name = _('Notification')
        verbose_name_plural = _('Notifications')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'read_status']),
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['notification_type']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['user', 'related_object_id', 'related_object_type'],
                name='unique_notification_object'
            ),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.title}"

    def mark_as_read(self):
        """Mark this notification as read."""
        if self.read_status == self.UNREAD:
            self.read_status = self.READ
            self.read_at = timezone.now()
            self.save(update_fields=['read_status', 'read_at'])

    def mark_as_unread(self):
        """Mark this notification as unread."""
        if self.read_status == self.READ:
            self.read_status = self.UNREAD
            self.read_at = None
            self.save(update_fields=['read_status', 'read_at'])

    @property
    def is_read(self):
        """Check if notification is read."""
        return self.read_status == self.READ

    @property
    def is_recent(self):
        """Check if notification is from the last 24 hours."""
        return self.created_at >= timezone.now() - timedelta(days=1)

    @classmethod
    def get_unread_count_for_user(cls, user):
        """Get count of unread notifications for a user."""
        return cls.objects.filter(user=user, read_status=cls.UNREAD).count()

    @classmethod
    def mark_all_as_read_for_user(cls, user):
        """Mark all notifications as read for a user."""
        return cls.objects.filter(
            user=user,
            read_status=cls.UNREAD
        ).update(
            read_status=cls.READ,
            read_at=timezone.now()
        )

    @classmethod
    def mark_all_as_unread_for_user(cls, user):
        """Mark all notifications as unread for a user."""
        return cls.objects.filter(
            user=user,
            read_status=cls.READ
        ).update(
            read_status=cls.UNREAD,
            read_at=None
        )


class AdminAnnouncement(models.Model):
    """Model to store admin announcements that can be sent to specific user groups."""

    # Target audience choices
    ALL_USERS = 'all'
    CUSTOMERS = 'customers'
    PROVIDERS = 'providers'
    ADMINS = 'admins'

    TARGET_AUDIENCE_CHOICES = [
        (ALL_USERS, 'All Users'),
        (CUSTOMERS, 'Customers Only'),
        (PROVIDERS, 'Service Providers Only'),
        (ADMINS, 'Admins Only'),
    ]

    # Status choices
    PENDING = 'pending'
    SENT = 'sent'
    CANCELLED = 'cancelled'

    STATUS_CHOICES = [
        (PENDING, 'Pending'),
        (SENT, 'Sent'),
        (CANCELLED, 'Cancelled'),
    ]

    # Core fields
    title = models.CharField(
        max_length=255,
        help_text=_('Announcement title')
    )
    slug = models.SlugField(
        max_length=255,
        unique=True,
        blank=True,
        help_text=_('URL-friendly slug auto-generated from the title')
    )
    announcement_text = models.TextField(
        help_text=_('Announcement message content')
    )
    target_audience = models.CharField(
        max_length=20,
        choices=TARGET_AUDIENCE_CHOICES,
        default=ALL_USERS,
        help_text=_('Who should receive this announcement')
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=PENDING,
        help_text=_('Current status of the announcement')
    )

    # Metadata
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='created_admin_announcements',
        help_text=_('Admin user who created this announcement')
    )
    created_at = models.DateTimeField(auto_now_add=True)
    sent_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the announcement was sent')
    )

    # Statistics
    total_recipients = models.PositiveIntegerField(
        default=0,
        help_text=_('Total number of users who received this announcement')
    )

    class Meta:
        verbose_name = _('Admin Announcement')
        verbose_name_plural = _('Admin Announcements')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['target_audience', 'status']),
            models.Index(fields=['created_at']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"{self.title} - {self.get_target_audience_display()}"

    def get_absolute_url(self):
        """Return URL for this announcement."""
        return reverse('notifications_app:announcement_detail', kwargs={'slug': self.slug})

    def save(self, *args, **kwargs):
        if not self.slug:
            base_slug = slugify(self.title)
            slug = base_slug
            counter = 1
            while AdminAnnouncement.objects.filter(slug=slug).exclude(pk=self.pk).exists():
                slug = f"{base_slug}-{counter}"
                counter += 1
            self.slug = slug
        super().save(*args, **kwargs)

    def send_announcement(self):
        """Send this announcement synchronously (Celery dependencies removed)."""
        if self.status != self.PENDING:
            return False

        # Always use synchronous sending since Celery has been removed
        return self.send_announcement_sync()

    def send_announcement_sync(self):
        """Send this announcement synchronously (for testing)."""
        if self.status != self.PENDING:
            return False

        target_users = self.get_target_users()
        notifications = [
            Notification(
                user=user,
                notification_type=Notification.ANNOUNCEMENT,
                title=self.title,
                message=self.announcement_text,
                related_object_id=self.id,
                related_object_type='AdminAnnouncement'
            )
            for user in target_users
        ]
        Notification.objects.bulk_create(notifications)

        self.status = self.SENT
        self.sent_at = timezone.now()
        self.total_recipients = len(notifications)
        self.save(update_fields=['status', 'sent_at', 'total_recipients'])
        return True

    def get_target_users(self):
        """Get the list of users who should receive this announcement."""
        if self.target_audience == self.ALL_USERS:
            return User.objects.filter(is_active=True)
        elif self.target_audience == self.CUSTOMERS:
            return User.objects.filter(is_active=True, role='customer')
        elif self.target_audience == self.PROVIDERS:
            return User.objects.filter(is_active=True, role='service_provider')
        elif self.target_audience == self.ADMINS:
            return User.objects.filter(is_active=True, role='admin')
        else:
            return User.objects.none()

    def cancel_announcement(self):
        """Cancel this announcement if it hasn't been sent yet."""
        if self.status == self.PENDING:
            self.status = self.CANCELLED
            self.save(update_fields=['status'])
            return True
        return False

    @property
    def can_be_sent(self):
        """Check if this announcement can be sent."""
        return self.status == self.PENDING

    @property
    def is_sent(self):
        """Check if this announcement has been sent."""
        return self.status == self.SENT

    @classmethod
    def get_pending_count(cls):
        """Get count of pending announcements."""
        return cls.objects.filter(status=cls.PENDING).count()


class NotificationPreference(models.Model):
    """User-specific notification channel preferences."""

    EMAIL = 'email'
    DASHBOARD = 'dashboard'

    CHANNEL_CHOICES = [
        (EMAIL, 'Email'),
        (DASHBOARD, 'Dashboard'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='notification_preferences'
    )
    notification_type = models.CharField(
        max_length=20,
        choices=Notification.TYPE_CHOICES
    )
    channel = models.CharField(
        max_length=20,
        choices=CHANNEL_CHOICES
    )
    is_enabled = models.BooleanField(default=True)

    class Meta:
        unique_together = ('user', 'notification_type', 'channel')
        verbose_name = _('Notification Preference')
        verbose_name_plural = _('Notification Preferences')

    def __str__(self):
        return f"{self.user.email} - {self.notification_type} - {self.channel}"


class EmailDeliveryStatus(models.Model):
    """Track email delivery status for all sent emails."""
    
    SENT = 'sent'
    DELIVERED = 'delivered'
    OPENED = 'opened'
    CLICKED = 'clicked'
    BOUNCED = 'bounced'
    FAILED = 'failed'
    
    STATUS_CHOICES = [
        (SENT, 'Sent'),
        (DELIVERED, 'Delivered'),
        (OPENED, 'Opened'),
        (CLICKED, 'Clicked'),
        (BOUNCED, 'Bounced'),
        (FAILED, 'Failed'),
    ]
    
    # Email identification
    email_id = models.CharField(
        max_length=255,
        unique=True,
        help_text=_('Unique identifier for this email')
    )
    
    # Recipient information
    recipient_email = models.EmailField(
        help_text=_('Email address of the recipient')
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='email_deliveries',
        help_text=_('User who received the email (if applicable)')
    )
    
    # Email details
    email_type = models.CharField(
        max_length=50,
        help_text=_('Type of email (welcome, booking_confirmation, etc.)')
    )
    subject = models.CharField(
        max_length=255,
        help_text=_('Email subject line')
    )
    
    # Delivery tracking
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=SENT,
        help_text=_('Current delivery status')
    )
    
    # Timestamps
    sent_at = models.DateTimeField(auto_now_add=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    opened_at = models.DateTimeField(null=True, blank=True)
    clicked_at = models.DateTimeField(null=True, blank=True)
    bounced_at = models.DateTimeField(null=True, blank=True)
    failed_at = models.DateTimeField(null=True, blank=True)
    
    # Error information
    error_message = models.TextField(
        blank=True,
        help_text=_('Error message if delivery failed')
    )
    bounce_type = models.CharField(
        max_length=50,
        blank=True,
        help_text=_('Type of bounce (hard, soft, etc.)')
    )
    
    # Metadata
    metadata = models.JSONField(
        default=dict,
        blank=True,
        help_text=_('Additional email metadata')
    )
    
    class Meta:
        verbose_name = _('Email Delivery Status')
        verbose_name_plural = _('Email Delivery Statuses')
        ordering = ['-sent_at']
        indexes = [
            models.Index(fields=['recipient_email', 'status']),
            models.Index(fields=['email_type', 'sent_at']),
            models.Index(fields=['user', 'status']),
        ]
    
    def __str__(self):
        return f"{self.email_type} to {self.recipient_email} - {self.status}"
    
    def mark_delivered(self):
        """Mark email as delivered."""
        if self.status == self.SENT:
            self.status = self.DELIVERED
            self.delivered_at = timezone.now()
            self.save(update_fields=['status', 'delivered_at'])
    
    def mark_opened(self):
        """Mark email as opened."""
        if self.status in [self.SENT, self.DELIVERED]:
            self.status = self.OPENED
            self.opened_at = timezone.now()
            self.save(update_fields=['status', 'opened_at'])
    
    def mark_clicked(self):
        """Mark email as clicked."""
        self.status = self.CLICKED
        self.clicked_at = timezone.now()
        self.save(update_fields=['status', 'clicked_at'])
    
    def mark_bounced(self, bounce_type='', error_message=''):
        """Mark email as bounced."""
        self.status = self.BOUNCED
        self.bounced_at = timezone.now()
        self.bounce_type = bounce_type
        self.error_message = error_message
        self.save(update_fields=['status', 'bounced_at', 'bounce_type', 'error_message'])
    
    def mark_failed(self, error_message=''):
        """Mark email as failed."""
        self.status = self.FAILED
        self.failed_at = timezone.now()
        self.error_message = error_message
        self.save(update_fields=['status', 'failed_at', 'error_message'])


class EmailBounceRecord(models.Model):
    """Record of email bounces for managing email reputation."""
    
    HARD_BOUNCE = 'hard'
    SOFT_BOUNCE = 'soft'
    COMPLAINT = 'complaint'
    UNSUBSCRIBE = 'unsubscribe'
    
    BOUNCE_TYPE_CHOICES = [
        (HARD_BOUNCE, 'Hard Bounce'),
        (SOFT_BOUNCE, 'Soft Bounce'),
        (COMPLAINT, 'Complaint'),
        (UNSUBSCRIBE, 'Unsubscribe'),
    ]
    
    email_address = models.EmailField(
        help_text=_('Email address that bounced')
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='email_bounces',
        help_text=_('User associated with the email (if applicable)')
    )
    
    bounce_type = models.CharField(
        max_length=20,
        choices=BOUNCE_TYPE_CHOICES,
        help_text=_('Type of bounce')
    )
    bounce_reason = models.TextField(
        help_text=_('Reason for the bounce')
    )
    
    # Tracking
    bounce_count = models.PositiveIntegerField(
        default=1,
        help_text=_('Number of times this email has bounced')
    )
    first_bounce_at = models.DateTimeField(auto_now_add=True)
    last_bounce_at = models.DateTimeField(auto_now=True)
    
    # Status
    is_suppressed = models.BooleanField(
        default=False,
        help_text=_('Whether this email should be suppressed from future sends')
    )
    suppressed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        verbose_name = _('Email Bounce Record')
        verbose_name_plural = _('Email Bounce Records')
        unique_together = ('email_address', 'bounce_type')
        ordering = ['-last_bounce_at']
        indexes = [
            models.Index(fields=['email_address', 'is_suppressed']),
            models.Index(fields=['bounce_type', 'last_bounce_at']),
        ]
    
    def __str__(self):
        return f"{self.email_address} - {self.bounce_type} ({self.bounce_count}x)"
    
    def increment_bounce_count(self):
        """Increment the bounce count and update last bounce time."""
        self.bounce_count += 1
        self.last_bounce_at = timezone.now()
        
        # Auto-suppress after certain conditions
        if (self.bounce_type == self.HARD_BOUNCE or 
            (self.bounce_type == self.SOFT_BOUNCE and self.bounce_count >= 5)):
            self.suppress()
        
        self.save()
    
    def suppress(self):
        """Suppress this email address from future sends."""
        self.is_suppressed = True
        self.suppressed_at = timezone.now()
        self.save(update_fields=['is_suppressed', 'suppressed_at'])
    
    def unsuppress(self):
        """Allow this email address to receive emails again."""
        self.is_suppressed = False
        self.suppressed_at = None
        self.save(update_fields=['is_suppressed', 'suppressed_at'])
    
    @classmethod
    def is_email_suppressed(cls, email_address):
        """Check if an email address is suppressed."""
        return cls.objects.filter(
            email_address=email_address,
            is_suppressed=True
        ).exists()
    
    @classmethod
    def get_bounce_count(cls, email_address):
        """Get total bounce count for an email address."""
        return cls.objects.filter(
            email_address=email_address
        ).aggregate(
            total_bounces=models.Sum('bounce_count')
        )['total_bounces'] or 0

