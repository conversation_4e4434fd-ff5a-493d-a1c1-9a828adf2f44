/* Profile Completion Progress Bar Styles */
/* CozyWish - Black & White Design Theme */

.profile-completion-widget {
    background: white;
    border: 2px solid black;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    font-family: var(--font-primary);
    position: relative;
    transition: all 0.3s ease;
}

.profile-completion-widget:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.completion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.completion-title {
    font-family: var(--font-heading);
    font-size: 1.2rem;
    font-weight: 600;
    color: black;
    margin: 0;
    display: flex;
    align-items: center;
}

.completion-percentage {
    font-size: 1.5rem;
    font-weight: 700;
    color: black;
    font-family: var(--font-heading);
    background: #f8f9fa;
    padding: 0.25rem 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;
}

.progress-container {
    position: relative;
    margin-bottom: 1.5rem;
}

.progress-bar-wrapper {
    width: 100%;
    height: 12px;
    background: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 6px;
    overflow: hidden;
    position: relative;
    cursor: pointer;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50 0%, #8BC34A 100%);
    transition: width 0.8s ease-in-out;
    position: relative;
    min-width: 8px;
    border-radius: 6px;
}

.progress-bar-fill {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
    position: relative;
}

.progress-step {
    font-size: 0.75rem;
    color: #666;
    background: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    position: relative;
    border: 1px solid #e9ecef;
}

.progress-step[data-step="25"] { left: 25%; transform: translateX(-25%); }
.progress-step[data-step="50"] { left: 50%; transform: translateX(-50%); }
.progress-step[data-step="75"] { left: 75%; transform: translateX(-75%); }
.progress-step[data-step="100"] { left: 100%; transform: translateX(-100%); }

.completion-suggestions {
    margin-top: 1rem;
}

.suggestions-title {
    font-size: 1rem;
    font-weight: 600;
    color: black;
    margin-bottom: 0.75rem;
    font-family: var(--font-heading);
}

.suggestions-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.suggestion-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.suggestion-item:hover {
    background: #e9ecef;
    border-color: #dee2e6;
    transform: translateX(2px);
}

.suggestion-item.priority-critical {
    border-left: 4px solid #dc3545;
    background: #fff5f5;
}

.suggestion-item.priority-critical:hover {
    background: #ffebee;
}

.suggestion-item.priority-high {
    border-left: 4px solid #fd7e14;
    background: #fff8f1;
}

.suggestion-item.priority-high:hover {
    background: #fff3e0;
}

.suggestion-item.priority-medium {
    border-left: 4px solid #ffc107;
    background: #fffbf0;
}

.suggestion-item.priority-medium:hover {
    background: #fffde7;
}

.suggestion-icon {
    color: #6c757d;
    margin-right: 0.75rem;
    font-size: 0.875rem;
}

.suggestion-text {
    flex: 1;
    font-size: 0.875rem;
    color: #495057;
    line-height: 1.4;
}

.suggestion-action {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: all 0.3s ease;
}

.suggestion-action:hover {
    background: #e9ecef;
    color: #495057;
    transform: translateX(2px);
}

.completion-success {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 0.5rem;
    color: #155724;
    animation: successPulse 2s infinite;
}

@keyframes successPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

.success-icon {
    font-size: 1.25rem;
    margin-right: 0.5rem;
    animation: successIconSpin 1s ease-in-out;
}

@keyframes successIconSpin {
    0% { transform: rotate(0deg) scale(0.8); }
    100% { transform: rotate(360deg) scale(1); }
}

.success-text {
    font-weight: 600;
    font-size: 1rem;
}

/* Notification badge for urgent items */
.completion-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    border: 2px solid white;
    animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Compact version for smaller spaces */
.profile-completion-widget.compact {
    padding: 1rem;
    margin-bottom: 1rem;
}

.profile-completion-widget.compact .completion-header {
    margin-bottom: 0.5rem;
}

.profile-completion-widget.compact .completion-title {
    font-size: 1rem;
}

.profile-completion-widget.compact .completion-percentage {
    font-size: 1.25rem;
}

.profile-completion-widget.compact .progress-container {
    margin-bottom: 1rem;
}

.profile-completion-widget.compact .suggestions-list {
    gap: 0.25rem;
}

.profile-completion-widget.compact .suggestion-item {
    padding: 0.5rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .completion-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .completion-percentage {
        font-size: 1.25rem;
    }
    
    .progress-steps {
        font-size: 0.7rem;
    }
    
    .suggestion-item {
        padding: 0.5rem;
    }
    
    .suggestion-text {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .profile-completion-widget {
        padding: 1rem;
    }
    
    .completion-title {
        font-size: 1rem;
    }
    
    .completion-percentage {
        font-size: 1.1rem;
    }
    
    .progress-steps {
        display: none; /* Hide steps on very small screens */
    }
    
    .suggestion-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .suggestion-action {
        align-self: flex-end;
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    .profile-completion-widget {
        background: #1a1a1a;
        border-color: #333;
        color: white;
    }
    
    .completion-title,
    .completion-percentage {
        color: white;
    }
    
    .progress-bar-wrapper {
        background: #333;
        border-color: #555;
    }
    
    .suggestion-item {
        background: #2a2a2a;
        border-color: #444;
    }
    
    .suggestion-item:hover {
        background: #333;
        border-color: #555;
    }
    
    .suggestion-text {
        color: #ccc;
    }
}

/* Print styles */
@media print {
    .profile-completion-widget {
        border: 1px solid #000;
        box-shadow: none;
    }
    
    .suggestion-action {
        display: none;
    }
    
    .progress-bar-fill {
        animation: none;
    }
} 