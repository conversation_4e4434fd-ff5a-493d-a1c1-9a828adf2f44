{% extends 'base.html' %}

{% block title %}Navbar Variants Demo - CozyWish{% endblock %}

{% block content %}
<div class="container py-5">
    <h1 class="mb-4">Navbar Variants Demo</h1>
    <p class="lead mb-5">Explore different navbar variants and configurations for different use cases.</p>

    <!-- Default Navbar Demo -->
    <section class="mb-5">
        <h2 class="mb-3">1. Default Navbar</h2>
        <p class="text-muted mb-3">Standard navbar with full navigation and features.</p>
        <div class="card">
            <div class="card-header">
                <code>{% include 'includes/navbar_cw.html' with variant='default' %}</code>
            </div>
            <div class="card-body p-0">
                {% include 'includes/navbar_cw.html' with variant='default' %}
                <div class="p-4">
                    <p>This is the default navbar with all features enabled.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Minimal Navbar Demo -->
    <section class="mb-5">
        <h2 class="mb-3">2. Minimal Navbar</h2>
        <p class="text-muted mb-3">Clean, simplified design for focused content pages.</p>
        <div class="card">
            <div class="card-header">
                <code>{% include 'includes/navbar_cw.html' with variant='minimal' %}</code>
            </div>
            <div class="card-body p-0">
                {% include 'includes/navbar_cw.html' with variant='minimal' %}
                <div class="p-4">
                    <p>Minimal navbar with reduced padding and simplified design.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Hero Navbar Demo -->
    <section class="mb-5">
        <h2 class="mb-3">3. Hero Navbar</h2>
        <p class="text-muted mb-3">Transparent navbar for landing pages with scroll effect.</p>
        <div class="card position-relative" style="background: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-accent) 100%); min-height: 300px;">
            <div class="card-header bg-transparent text-white">
                <code class="text-white">{% include 'includes/navbar_cw.html' with variant='hero' %}</code>
            </div>
            <div class="card-body p-0">
                {% include 'includes/navbar_cw.html' with variant='hero' %}
                <div class="p-4 text-white">
                    <h3>Hero Section</h3>
                    <p>This navbar is transparent and positioned absolutely. It becomes solid on scroll.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Admin Navbar Demo -->
    <section class="mb-5">
        <h2 class="mb-3">4. Admin Navbar</h2>
        <p class="text-muted mb-3">Dark theme for administrative interfaces.</p>
        <div class="card">
            <div class="card-header">
                <code>{% include 'includes/navbar_cw.html' with variant='admin' %}</code>
            </div>
            <div class="card-body p-0">
                {% include 'includes/navbar_cw.html' with variant='admin' %}
                <div class="p-4">
                    <p>Admin navbar with dark theme and administrative styling.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Provider Navbar Demo -->
    <section class="mb-5">
        <h2 class="mb-3">5. Provider Navbar</h2>
        <p class="text-muted mb-3">Service provider specific styling and navigation.</p>
        <div class="card">
            <div class="card-header">
                <code>{% include 'includes/navbar_cw.html' with variant='provider' %}</code>
            </div>
            <div class="card-body p-0">
                {% include 'includes/navbar_cw.html' with variant='provider' %}
                <div class="p-4">
                    <p>Provider navbar with light theme and provider-focused features.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Customer Navbar Demo -->
    <section class="mb-5">
        <h2 class="mb-3">6. Customer Navbar</h2>
        <p class="text-muted mb-3">Customer-focused styling with shopping cart integration.</p>
        <div class="card">
            <div class="card-header">
                <code>{% include 'includes/navbar_cw.html' with variant='customer' %}</code>
            </div>
            <div class="card-body p-0">
                {% include 'includes/navbar_cw.html' with variant='customer' %}
                <div class="p-4">
                    <p>Customer navbar with shopping cart and customer-specific features.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Configuration Examples -->
    <section class="mb-5">
        <h2 class="mb-3">Configuration Examples</h2>
        
        <div class="row">
            <div class="col-md-6 mb-4">
                <h4>With Search</h4>
                <div class="card">
                    <div class="card-header">
                        <code>show_search=True</code>
                    </div>
                    <div class="card-body p-0">
                        {% include 'includes/navbar_cw.html' with variant='default' show_search=True %}
                        <div class="p-3">
                            <p class="mb-0">Navbar with search functionality enabled.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-4">
                <h4>Without Brand</h4>
                <div class="card">
                    <div class="card-header">
                        <code>show_brand=False</code>
                    </div>
                    <div class="card-body p-0">
                        {% include 'includes/navbar_cw.html' with variant='default' show_brand=False %}
                        <div class="p-3">
                            <p class="mb-0">Navbar without brand logo.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-4">
                <h4>Minimal Configuration</h4>
                <div class="card">
                    <div class="card-header">
                        <code>variant='minimal' show_search=False show_cart=False</code>
                    </div>
                    <div class="card-body p-0">
                        {% include 'includes/navbar_cw.html' with variant='minimal' show_search=False show_cart=False %}
                        <div class="p-3">
                            <p class="mb-0">Minimal navbar with reduced features.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-4">
                <h4>Hero with Search</h4>
                <div class="card position-relative" style="background: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-accent) 100%); min-height: 200px;">
                    <div class="card-header bg-transparent text-white">
                        <code class="text-white">variant='hero' show_search=True</code>
                    </div>
                    <div class="card-body p-0">
                        {% include 'includes/navbar_cw.html' with variant='hero' show_search=True %}
                        <div class="p-3 text-white">
                            <p class="mb-0">Hero navbar with search functionality.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Usage Guide -->
    <section class="mb-5">
        <h2 class="mb-3">Usage Guide</h2>
        <div class="row">
            <div class="col-md-6">
                <h4>Basic Usage</h4>
                <pre><code>{% include 'includes/navbar_cw.html' %}

{% include 'includes/navbar_cw.html' with variant='hero' %}

{% include 'includes/navbar_cw.html' with variant='admin' %}</code></pre>
            </div>
            <div class="col-md-6">
                <h4>Advanced Configuration</h4>
                <pre><code>{% include 'includes/navbar_cw.html' with 
    variant='hero' 
    show_search=True 
    show_cart=False 
    show_notifications=False %}</code></pre>
            </div>
        </div>
    </section>

    <!-- Available Variants -->
    <section class="mb-5">
        <h2 class="mb-3">Available Variants</h2>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Variant</th>
                        <th>Description</th>
                        <th>Use Case</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>default</code></td>
                        <td>Standard navbar with full navigation</td>
                        <td>General pages, main navigation</td>
                    </tr>
                    <tr>
                        <td><code>minimal</code></td>
                        <td>Clean, simplified design</td>
                        <td>Focused content pages, blogs</td>
                    </tr>
                    <tr>
                        <td><code>hero</code></td>
                        <td>Transparent for landing pages</td>
                        <td>Landing pages, hero sections</td>
                    </tr>
                    <tr>
                        <td><code>admin</code></td>
                        <td>Dark theme for admin interfaces</td>
                        <td>Administrative dashboards</td>
                    </tr>
                    <tr>
                        <td><code>provider</code></td>
                        <td>Service provider specific styling</td>
                        <td>Provider dashboards</td>
                    </tr>
                    <tr>
                        <td><code>customer</code></td>
                        <td>Customer-focused styling</td>
                        <td>Customer interfaces</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </section>
</div>
{% endblock %} 