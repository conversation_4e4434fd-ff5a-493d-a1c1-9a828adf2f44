<!DOCTYPE html>
<html lang="en" data-theme="cozywish">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}CozyWish{% endblock %}</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- DaisyUI 5.0.46 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/daisyui@5.0.46/dist/full.min.css" rel="stylesheet" type="text/css" />
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CozyWish Theme Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'poppins': ['Poppins', 'sans-serif']
                    }
                }
            },
            daisyui: {
                themes: [
                    {
                        cozywish: {
                            "primary": "#43251B",
                            "secondary": "#FAE1D7", 
                            "accent": "#FAE1D7",
                            "neutral": "#262626",
                            "base-100": "#F8F9FA",
                            "base-200": "#FFF9F4",
                            "base-300": "#f5f5f5",
                            "info": "#0284c7",
                            "success": "#059669",
                            "warning": "#d97706",
                            "error": "#dc2626"
                        }
                    }
                ]
            }
        }
    </script>
    
    <!-- Custom CozyWish Styles -->
    <style>
        /* Global Font Configuration */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        /* Custom Button Enhancements */
        .btn-primary {
            background: linear-gradient(135deg, #43251B 0%, #2d1912 100%);
            border: none;
            color: white;
            font-weight: 600;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #2d1912 0%, #1a0f0a 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #FAE1D7 0%, #f5d7c7 100%);
            border: 2px solid #43251B;
            color: #43251B;
            font-weight: 600;
        }
        
        .btn-secondary:hover {
            background: linear-gradient(135deg, #43251B 0%, #2d1912 100%);
            color: white;
            transform: translateY(-1px);
        }
        
        /* Custom Card Enhancements */
        .card {
            transition: all 0.2s ease;
        }
        
        .card:hover {
            transform: translateY(-1px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        /* Footer Styling */
        .footer {
            background-color: #43251B;
            color: white;
            padding: 2rem 0;
        }
        
        .footer h5 {
            color: #FAE1D7;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .footer a {
            color: #FAE1D7;
            text-decoration: none;
        }
        
        .footer a:hover {
            color: white;
        }
        
        .social-links a {
            display: inline-block;
            margin-right: 1rem;
            font-size: 1.25rem;
        }
        
        .social-links a:hover {
            transform: translateY(-2px);
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="{% block body_class %}{% endblock %}">
    <!-- Professional Navigation -->
    {% include 'includes/navbar_cw.html' with variant='default' %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer mt-5">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h5 class="text-xl font-semibold mb-4">CozyWish</h5>
                    <p class="mb-4">Your premier destination for wellness and beauty services.</p>
                    <div class="social-links">
                        <a href="#" class="hover:text-accent transition-colors"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="hover:text-accent transition-colors"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="hover:text-accent transition-colors"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>
                <div>
                    <h5 class="text-xl font-semibold mb-4">Quick Links</h5>
                    <ul class="space-y-2">
                        <li><a href="/" class="hover:text-white transition-colors">Home</a></li>
                        <li><a href="/services" class="hover:text-white transition-colors">Services</a></li>
                        <li><a href="/about" class="hover:text-white transition-colors">About Us</a></li>
                        <li><a href="/contact" class="hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h5 class="text-xl font-semibold mb-4">Contact Info</h5>
                    <p class="mb-2"><i class="fas fa-phone mr-2"></i> (*************</p>
                    <p class="mb-2"><i class="fas fa-envelope mr-2"></i> <EMAIL></p>
                    <p class="mb-2"><i class="fas fa-map-marker-alt mr-2"></i> 123 Wellness St, City</p>
                </div>
            </div>
            <div class="divider my-8 opacity-30"></div>
            <div class="text-center">
                <p>&copy; 2024 CozyWish. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- DaisyUI JavaScript Components -->
    <script src="https://cdn.jsdelivr.net/npm/daisyui@5.0.46/dist/full.min.js"></script>
    
    <!-- CozyWish Navbar Enhancements -->
    {% load static %}
    <script src="{% static 'js/navbar-enhancements.js' %}"></script>
    
    <!-- Custom JavaScript for DaisyUI Component Support -->
    <script>
        // Helper functions for DaisyUI components
        window.CozyWish = {
            // Modal helpers
            showModal: function(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.showModal();
                }
            },
            
            hideModal: function(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.close();
                }
            },
            
            // Toast helpers
            showToast: function(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `toast toast-top toast-end`;
                toast.innerHTML = `
                    <div class="alert alert-${type}">
                        <span>${message}</span>
                    </div>
                `;
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    toast.remove();
                }, 3000);
            },
            
            // Tooltip initialization (compatible with existing code)
            initTooltips: function() {
                // DaisyUI tooltips work with CSS classes, no JS initialization needed
                console.log('DaisyUI tooltips initialized');
            }
        };
        
        // Backward compatibility for existing Bootstrap JavaScript
        window.bootstrap = {
            Modal: function(element) {
                return {
                    show: function() {
                        if (element.showModal) {
                            element.showModal();
                        }
                    },
                    hide: function() {
                        if (element.close) {
                            element.close();
                        }
                    }
                };
            },
            
            Toast: function(element, options) {
                return {
                    show: function() {
                        CozyWish.showToast(element.textContent || 'Notification', 'info');
                    }
                };
            },
            
            Tooltip: function(element) {
                return {
                    // DaisyUI handles tooltips via CSS classes
                };
            },
            
            Popover: function(element) {
                return {
                    // DaisyUI handles popovers via CSS classes  
                };
            },
            
            Alert: function(element) {
                return {
                    close: function() {
                        element.remove();
                    }
                };
            }
        };
        
        // Initialize tooltips on page load
        document.addEventListener('DOMContentLoaded', function() {
            CozyWish.initTooltips();
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
