{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Venue Not Found" %} - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Venue Not Found */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
    }

    /* Error Section */
    .error-section {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem 0;
    }

    .error-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* Error Card */
    .error-card {
        background: white;
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        padding: 3rem 2rem;
        text-align: center;
        box-shadow: var(--cw-shadow-lg);
    }

    .error-icon {
        width: 100px;
        height: 100px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 3rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
    }

    .error-title {
        font-family: var(--cw-font-display);
        font-weight: 700;
        color: var(--cw-brand-primary);
        font-size: 2.75rem;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .error-message {
        color: var(--cw-neutral-600);
        font-size: 1.125rem;
        margin-bottom: 2rem;
        line-height: 1.6;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Custom Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
        text-decoration: none;
        display: inline-block;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-heading);
        text-decoration: none;
        display: inline-block;
        margin-left: 1rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .error-section {
            padding: 1rem;
        }

        .error-card {
            padding: 2rem 1.5rem;
        }

        .error-title {
            font-size: 2.25rem;
        }

        .error-icon {
            width: 80px;
            height: 80px;
            font-size: 2.5rem;
        }

        .btn-cw-secondary {
            margin-left: 0;
            margin-top: 1rem;
        }
    }

    @media (max-width: 576px) {
        .error-card {
            padding: 1.5rem 1rem;
        }

        .error-title {
            font-size: 1.875rem;
        }

        .error-message {
            font-size: 1rem;
        }

        .error-icon {
            width: 70px;
            height: 70px;
            font-size: 2rem;
            margin-bottom: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="error-section">
    <div class="error-container">
        <div class="error-card">
            <div class="error-icon">
                <i class="fas fa-search"></i>
            </div>
            <h1 class="error-title">{% trans "Venue Not Found" %}</h1>
            <p class="error-message">
                {% trans "The venue you're looking for is not available or may have been removed. This might happen if the venue is no longer active or the link is outdated." %}
            </p>
            <div class="d-flex flex-column flex-sm-row justify-content-center align-items-center">
                <a href="{% url 'venues_app:venue_search' %}" class="btn btn-cw-primary">
                    <i class="fas fa-search me-2"></i>{% trans "Browse Venues" %}
                </a>
                <a href="{% url 'home_app:home' %}" class="btn btn-cw-secondary">
                    <i class="fas fa-home me-2"></i>{% trans "Go Home" %}
                </a>
            </div>
        </div>
    </div>
</section>
{% endblock %}
