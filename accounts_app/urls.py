from django.urls import path
from . import views
from .views import ajax, privacy


app_name = 'accounts_app'

urlpatterns = [
    # Business Information Page
    path('for-business/', views.business_landing_view, name='for_business'),

    # Customer Authentication URLs (custom views for role-based authentication)
    path('customer/signup/', views.CustomerSignupView.as_view(), name='customer_signup'),
    path('customer/login/', views.CustomerLoginView.as_view(), name='customer_login'),
    path('customer/logout/', views.CustomerLogoutView.as_view(), name='customer_logout'),
    path('logout/', views.UnifiedLogoutView.as_view(), name='unified_logout'),
    
    # Service Provider Authentication URLs
    path('provider/login/', views.ServiceProviderLoginView.as_view(), name='service_provider_login'),

    # Customer Profile URLs (keeping custom views for profile management)
    path('customer/profile/', views.CustomerProfileView.as_view(), name='customer_profile'),
    path('customer/profile/edit/', views.CustomerProfileEditView.as_view(), name='customer_profile_edit'),
    path('customer/change-password/', views.CustomerPasswordChangeView.as_view(), name='customer_change_password'),
    path('customer/deactivate/', views.CustomerDeactivateAccountView.as_view(), name='customer_deactivate'),
    
    # Customer Password Reset URLs
    path('customer/password/reset/', views.CustomerPasswordResetView.as_view(), name='customer_password_reset'),
    path('customer/password/reset/done/', views.CustomerPasswordResetDoneView.as_view(), name='customer_password_reset_done'),
    path('customer/password/reset/<uidb64>/<token>/', views.CustomerPasswordResetConfirmView.as_view(), name='customer_password_reset_confirm'),
    path('customer/password/reset/complete/', views.CustomerPasswordResetCompleteView.as_view(), name='customer_password_reset_complete'),
    
    # Service Provider Profile URLs (keeping custom views for profile management)
    path('provider/profile/', views.ServiceProviderProfileView.as_view(), name='service_provider_profile'),
    path('provider/profile/edit/', views.ServiceProviderProfileEditView.as_view(), name='service_provider_profile_edit'),
    path('provider/change-password/', views.service_provider_change_password_view, name='service_provider_change_password'),
    path('provider/deactivate/', views.service_provider_deactivate_account_view, name='service_provider_deactivate'),

    # Service Provider Signup URLs
    path('provider/signup/', views.ServiceProviderSignupView.as_view(), name='service_provider_signup'),
    path('provider/signup/done/', views.provider_signup_done_view, name='provider_signup_done'),
    path('provider/email/verify/<str:uidb64>/<str:token>/', views.provider_email_verify_view, name='service_provider_email_verify'),
    
    # Service Provider Password Reset URLs
    path('provider/password/reset/', views.ServiceProviderPasswordResetView.as_view(), name='service_provider_password_reset'),
    path('provider/password/reset/done/', views.ServiceProviderPasswordResetDoneView.as_view(), name='service_provider_password_reset_done'),
    path('provider/password/reset/<uidb64>/<token>/', views.ServiceProviderPasswordResetConfirmView.as_view(), name='service_provider_password_reset_confirm'),
    path('provider/password/reset/complete/', views.ServiceProviderPasswordResetCompleteView.as_view(), name='service_provider_password_reset_complete'),

    # Team Management URLs (keeping custom views for business logic)
    # path('provider/team/', views.team_member_list_view, name='team_member_list'),
    # path('provider/team/add/', views.team_member_add_view, name='team_member_add'),
    # path('provider/team/<int:member_id>/edit/', views.team_member_edit_view, name='team_member_edit'),
    # path('provider/team/<int:member_id>/delete/', views.team_member_delete_view, name='team_member_delete'),
    # path('provider/team/<int:member_id>/toggle/', views.team_member_toggle_status_view, name='team_member_toggle_status'),

    # Premium Features (keeping custom views for business logic)
    # path('premium/upgrade/', views.premium_upgrade, name='premium_upgrade'),
    
    # AJAX Endpoints
    path('ajax/customer/profile/update/', ajax.CustomerProfileAjaxView.as_view(), name='customer_profile_ajax'),
    path('ajax/provider/profile/update/', ajax.ServiceProviderProfileAjaxView.as_view(), name='provider_profile_ajax'),
    path('ajax/validate-field/', ajax.validate_field_ajax, name='validate_field_ajax'),
    
    # Email Verification URLs
    path('verify-email/<str:token>/', views.email_verification_view, name='email_verification'),
    path('resend-verification/', views.resend_verification_email_view, name='resend_verification'),
    path('email-verification-success/', views.email_verification_success_view, name='email_verification_success'),
    path('email-verification-sent/', views.email_verification_sent_view, name='email_verification_sent'),
    
    # Privacy Settings URLs
    path('privacy/settings/', privacy.PrivacySettingsView.as_view(), name='privacy_settings'),
    path('privacy/settings/update/', privacy.PrivacySettingsUpdateView.as_view(), name='privacy_settings_update'),
    path('privacy/quick-toggle/', privacy.privacy_settings_quick_toggle, name='privacy_quick_toggle'),
    
    # GDPR & Data Export URLs
    path('data/export/', privacy.DataExportView.as_view(), name='data_export'),
    path('data/export/download/<str:filename>/', privacy.DataExportDownloadView.as_view(), name='data_export_download'),
    path('data/deletion/request/', privacy.DataDeletionRequestView.as_view(), name='data_deletion_request'),
    
    # Profile Verification URLs
    path('verification/status/', privacy.VerificationBadgeView.as_view(), name='verification_status'),
    path('verification/request/', privacy.RequestVerificationView.as_view(), name='request_verification'),
    path('verification/status/ajax/', privacy.VerificationStatusView.as_view(), name='verification_status_ajax'),
    path('verification/summary/', privacy.user_verification_summary, name='verification_summary'),
    
    # Note: Authentication URLs (signup, login, logout, password reset) are now handled by allauth
    # These are available via the allauth.urls include in project_root/urls.py
    # Common allauth URLs will be available at:
    # - /accounts/login/
    # - /accounts/logout/
    # - /accounts/signup/
    # - /accounts/password/reset/
    # - /accounts/password/reset/done/
    # - /accounts/password/reset/key/<key>/
    # - /accounts/password/reset/key/done/
    # - /accounts/confirm-email/
    # - /accounts/confirm-email/<key>/
]
