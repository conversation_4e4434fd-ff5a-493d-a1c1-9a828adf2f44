{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Your Cart - CozyWish{% endblock %}

{% block booking_extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Cart Page */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Override base styles for cart page */
    .booking-wrapper {
        background: var(--cw-accent-light);
        font-family: var(--cw-font-primary);
    }

    /* Checkout Progress Indicator */
    .checkout-progress {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 1.5rem 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
    }

    .progress-header {
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .progress-header h3 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0 0 0.5rem 0;
        font-size: 1.25rem;
    }

    .progress-header .subtitle {
        color: var(--cw-neutral-600);
        font-size: 0.95rem;
        margin: 0;
    }

    .progress-steps {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        margin-bottom: 1rem;
    }

    .progress-steps::before {
        content: '';
        position: absolute;
        top: 20px;
        left: 20px;
        right: 20px;
        height: 2px;
        background: var(--cw-brand-accent);
        z-index: 1;
    }

    .progress-steps::after {
        content: '';
        position: absolute;
        top: 20px;
        left: 20px;
        width: 33.33%;
        height: 2px;
        background: var(--cw-brand-primary);
        z-index: 2;
        transition: width 0.3s ease;
    }

    .progress-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        position: relative;
        z-index: 3;
        flex: 1;
        max-width: 120px;
    }

    .step-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: white;
        border: 2px solid var(--cw-brand-accent);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        color: var(--cw-neutral-600);
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
    }

    .step-circle.completed {
        background: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
        color: white;
    }

    .step-circle.active {
        background: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
        color: white;
        transform: scale(1.1);
        box-shadow: 0 0 0 4px rgba(47, 22, 15, 0.1);
    }

    .step-title {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--cw-neutral-600);
        line-height: 1.3;
    }

    .step-title.active {
        color: var(--cw-brand-primary);
        font-weight: 600;
    }

    .step-title.completed {
        color: var(--cw-brand-primary);
    }

    /* Page Header */
    .cart-header {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid var(--cw-brand-accent);
    }

    .cart-header h1 {
        font-family: var(--cw-font-heading);
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-size: 2.5rem;
    }

    .cart-header .subtitle {
        font-family: var(--cw-font-primary);
        color: var(--cw-neutral-600);
        font-size: 1.1rem;
        margin: 0;
    }

    /* Cart Cards */
    .cart-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        margin-bottom: 1.5rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .cart-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .cart-card-header {
        background: var(--cw-gradient-card-subtle);
        padding: 1.5rem;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .cart-card-header h4 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0;
        font-size: 1.5rem;
    }

    .cart-card-body {
        padding: 1.5rem;
    }

    /* Cart Items */
    .cart-item {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .cart-item:hover {
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-sm);
    }

    .cart-item h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }

    .cart-item .service-description {
        color: var(--cw-neutral-600);
        font-size: 0.95rem;
        margin-bottom: 1rem;
        line-height: 1.5;
    }

    .cart-item .service-details {
        margin-bottom: 1rem;
    }

    .cart-item .service-details p {
        margin-bottom: 0.5rem;
        color: var(--cw-neutral-700);
        font-weight: 500;
    }

    .cart-item .service-details strong {
        color: var(--cw-brand-primary);
        font-weight: 600;
    }

    .cart-item .price-section {
        text-align: right;
    }

    .cart-item .price-section p {
        margin-bottom: 0.5rem;
        color: var(--cw-neutral-700);
        font-weight: 500;
    }

    .cart-item .total-price {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
    }

    /* Service Images */
    .cart-service-image {
        width: 100%;
        height: 120px;
        object-fit: cover;
        border-radius: 0.5rem;
        border: 1px solid var(--cw-brand-accent);
    }

    .cart-service-placeholder {
        width: 100%;
        height: 120px;
        background: var(--cw-brand-accent);
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--cw-brand-primary);
        font-size: 2rem;
    }

    /* Pricing Breakdown */
    .pricing-breakdown {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .pricing-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.25rem 0;
        font-size: 0.9rem;
    }

    .pricing-row.subtotal {
        border-top: 1px solid var(--cw-brand-accent);
        margin-top: 0.5rem;
        padding-top: 0.5rem;
        font-weight: 600;
        font-size: 1rem;
    }

    .pricing-row.savings {
        color: #28a745;
        font-weight: 500;
    }

    /* Quick View Button */
    .btn-quick-view {
        background: white;
        border: 1px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-primary);
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        font-size: 0.875rem;
        cursor: pointer;
    }

    .btn-quick-view:hover {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        transform: translateY(-1px);
    }

    /* Cart Item Actions */
    .cart-item-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
        margin-top: 1rem;
    }

    .btn-edit {
        background: white;
        border: 1px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        padding: 0.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 2.5rem;
        height: 2.5rem;
    }

    .btn-edit:hover {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        text-decoration: none;
        transform: translateY(-1px);
    }

    /* Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        color: white;
        font-family: var(--cw-font-primary);
        font-weight: 600;
        padding: 0.75rem 2rem;
        border-radius: 0.75rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        font-size: 1rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-primary);
        font-weight: 600;
        padding: 0.75rem 2rem;
        border-radius: 0.75rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        font-size: 1rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        text-decoration: none;
        transform: translateY(-1px);
    }

    .btn-remove {
        background: white;
        border: 2px solid #dc3545;
        color: #dc3545;
        font-family: var(--cw-font-primary);
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        font-size: 0.875rem;
    }

    .btn-remove:hover {
        background: #dc3545;
        color: white;
        transform: translateY(-1px);
    }

    /* Cart Summary */
    .cart-summary {
        background: var(--cw-gradient-card);
        border: 1px solid var(--cw-brand-primary);
        border-radius: 1rem;
        padding: 1.5rem;
        box-shadow: var(--cw-shadow-md);
        margin-bottom: 1.5rem;
    }

    .cart-summary h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }

    .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
        color: var(--cw-neutral-700);
        font-weight: 500;
    }

    .summary-total {
        border-top: 2px solid var(--cw-brand-primary);
        padding-top: 1rem;
        margin-top: 1rem;
    }

    .summary-total .summary-row {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0;
    }

    /* Cart Expiry */
    .cart-expiry {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1rem;
        text-align: center;
    }

    .cart-expiry .expiry-text {
        color: var(--cw-neutral-600);
        font-size: 0.9rem;
        margin: 0;
        font-weight: 500;
    }

    .cart-expiry .fas {
        color: var(--cw-brand-primary);
        margin-right: 0.5rem;
    }

    /* Empty Cart */
    .empty-cart {
        text-align: center;
        padding: 4rem 2rem;
        background: var(--cw-gradient-card-subtle);
        border-radius: 1rem;
        border: 1px solid var(--cw-brand-accent);
    }

    .empty-cart .cart-icon {
        font-size: 4rem;
        color: var(--cw-brand-accent);
        margin-bottom: 1.5rem;
    }

    .empty-cart h5 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .empty-cart p {
        color: var(--cw-neutral-600);
        font-size: 1.1rem;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    /* Checkout Section */
    .checkout-section {
        text-align: right;
        padding-top: 1.5rem;
        border-top: 2px solid var(--cw-brand-accent);
        margin-top: 1.5rem;
    }

    .checkout-total {
        font-family: var(--cw-font-heading);
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .cart-header h1 {
            font-size: 2rem;
        }

        .cart-item .price-section {
            text-align: left;
            margin-top: 1rem;
        }

        .checkout-section {
            text-align: center;
        }

        .btn-cw-primary,
        .btn-cw-secondary {
            width: 100%;
            margin-bottom: 0.5rem;
        }
    }

    .service-placeholder-large {
        width: 100%;
        height: 200px;
        background: var(--cw-brand-accent);
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--cw-brand-primary);
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .service-details-grid {
        display: grid;
        gap: 0.75rem;
    }

    .detail-item {
        padding: 0.5rem 0;
        border-bottom: 1px solid #eee;
    }

    .detail-item:last-child {
        border-bottom: none;
    }
</style>
{% endblock %}

{% block booking_content %}
<div class="container">
    <!-- Checkout Progress Indicator -->
    <div class="checkout-progress">
        <div class="progress-header">
            <h3>Checkout Progress</h3>
            <p class="subtitle">Complete these steps to secure your booking</p>
        </div>
        <div class="progress-steps">
            <div class="progress-step">
                <div class="step-circle active">1</div>
                <span class="step-title active">Review Cart</span>
            </div>
            <div class="progress-step">
                <div class="step-circle">2</div>
                <span class="step-title">Confirm Details</span>
            </div>
            <div class="progress-step">
                <div class="step-circle">3</div>
                <span class="step-title">Payment</span>
            </div>
        </div>
    </div>

    <!-- Page Header -->
    <div class="cart-header">
        <h1>Your Cart</h1>
        {% if cart.items.count > 0 %}
        <p class="subtitle">{{ cart.items.count }} item{{ cart.items.count|pluralize }} ready for booking</p>
        {% else %}
        <p class="subtitle">Start building your perfect spa experience</p>
        {% endif %}
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="cart-card">
                <div class="cart-card-header">
                    <h4>
                        <i class="fas fa-shopping-cart me-2"></i>
                        Cart Items
                    </h4>
                </div>
                <div class="cart-card-body">
                    {% if cart.items.count > 0 %}
                        {% for item in cart.items.all %}
                        <div class="cart-item">
                            <div class="row">
                                <div class="col-md-2">
                                    <!-- Service Image -->
                                    {% if item.service.service_image %}
                                        <img src="{{ item.service.service_image.url }}" 
                                             alt="{{ item.service.service_title }}"
                                             class="cart-service-image"
                                             loading="lazy">
                                    {% else %}
                                        <div class="cart-service-placeholder">
                                            <i class="fas fa-spa"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <h6>{{ item.service.service_title }}</h6>
                                    <p class="service-description">{{ item.service.short_description }}</p>
                                    <div class="service-details">
                                        <p><strong>Venue:</strong> {{ item.service.venue.venue_name }}</p>
                                        <p><strong>Date:</strong> {{ item.selected_date|date:"F d, Y" }}</p>
                                        <p><strong>Time:</strong> {{ item.selected_time_slot|time:"g:i A" }}
                                            <span class="text-muted">
                                                ({{ item.service.duration_minutes }} min session)
                                            </span>
                                        </p>
                                        <p><strong>Category:</strong> {{ item.service.service_category.name }}</p>
                                    </div>
                                    <!-- Quick View Button -->
                                    <button type="button" class="btn-quick-view" 
                                            onclick="showQuickView({{ item.service.id }})">
                                        <i class="fas fa-eye me-1"></i>
                                        Quick View Details
                                    </button>
                                </div>
                                <div class="col-md-4 price-section">
                                    <!-- Enhanced Pricing Breakdown -->
                                    <div class="pricing-breakdown">
                                        <div class="pricing-row">
                                            <span>Quantity:</span>
                                            <span>{{ item.quantity }}</span>
                                        </div>
                                        <div class="pricing-row">
                                            <span>Price per session:</span>
                                            <span>${{ item.price_per_item }}</span>
                                        </div>
                                        <div class="pricing-row subtotal">
                                            <span>Subtotal:</span>
                                            <span>${{ item.total_price }}</span>
                                        </div>
                                    </div>
                                    
                                    <!-- Action Buttons -->
                                    <div class="cart-item-actions">
                                        <a href="{% url 'booking_cart_app:update_cart_item' item.id %}" 
                                           class="btn-edit" title="Edit quantity">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="post" action="{% url 'booking_cart_app:remove_from_cart' item.id %}" class="d-inline">
                                            {% csrf_token %}
                                            <button type="submit" class="btn-remove"
                                                    onclick="return showRemoveConfirmation('{{ item.service.service_title }}')">
                                                <i class="fas fa-trash"></i>
                                                Remove
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}

                        <div class="checkout-section">
                            <div class="checkout-total">Total: ${{ cart.total_price }}</div>
                            <a href="{% url 'booking_cart_app:checkout' %}" class="btn-cw-primary">
                                <i class="fas fa-credit-card me-2"></i>
                                Proceed to Checkout
                            </a>
                        </div>
                    {% else %}
                        <div class="empty-cart">
                            <div class="cart-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <h5>Your cart is empty</h5>
                            <p>Discover amazing spa and wellness services to create your perfect relaxation experience.</p>
                            <a href="{% url 'home_app:home' %}" class="btn-cw-primary">
                                <i class="fas fa-search me-2"></i>
                                Browse Services
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            {% if cart.items.count > 0 %}
            <div class="cart-summary">
                <h6>
                    <i class="fas fa-receipt me-2"></i>
                    Cart Summary
                </h6>
                <div class="summary-row">
                    <span>Items:</span>
                    <span>{{ cart.items.count }}</span>
                </div>
                <div class="summary-row">
                    <span>Subtotal:</span>
                    <span>${{ cart.total_price }}</span>
                </div>
                <div class="summary-total">
                    <div class="summary-row">
                        <span>Total:</span>
                        <span>${{ cart.total_price }}</span>
                    </div>
                </div>
            </div>

            <div class="cart-expiry">
                <p class="expiry-text">
                    <i class="fas fa-clock"></i>
                    Cart expires in {{ cart.expires_at|timeuntil }}
                </p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

<!-- Quick View Modal -->
<div class="modal fade" id="quickViewModal" tabindex="-1" aria-labelledby="quickViewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickViewModalLabel">Service Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="quickViewContent">
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Confirmation Modal -->
<div class="modal fade" id="removeConfirmationModal" tabindex="-1" aria-labelledby="removeConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="removeConfirmationModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Remove Item from Cart
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to remove <strong id="removeItemName"></strong> from your cart?</p>
                <div class="alert alert-info">
                    <small><i class="fas fa-info-circle me-1"></i> This action cannot be undone. You'll need to add the service again if you change your mind.</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmRemoveBtn">
                    <i class="fas fa-trash me-1"></i>
                    Remove Item
                </button>
            </div>
        </div>
    </div>
</div>

{% block booking_extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    let removeFormToSubmit = null;
    let slotsLocked = false;
    
    initializeCartFeatures();
    
    function initializeCartFeatures() {
        setupQuickViewModal();
        setupRemoveConfirmation();
        setupCheckoutFlow();
        setupRealTimeAvailability();
        addCartLoadingStates();
    }
    
    // Quick View Modal functionality
    function setupQuickViewModal() {
        window.showQuickView = function(serviceId) {
            const modal = new bootstrap.Modal(document.getElementById('quickViewModal'));
            const content = document.getElementById('quickViewContent');
            
            // Show loading spinner
            content.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading service details...</p>
                </div>
            `;
            
            modal.show();
            
            // Fetch service details
            fetch(`/bookings/api/services/${serviceId}/details/`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to load service details');
                    }
                    return response.json();
                })
                .then(data => {
                    content.innerHTML = `
                        <div class="row">
                            <div class="col-md-4">
                                ${data.image ? 
                                    `<img src="${data.image}" alt="${data.title}" class="img-fluid rounded mb-3">` : 
                                    `<div class="service-placeholder-large">
                                        <i class="fas fa-spa"></i>
                                    </div>`
                                }
                            </div>
                            <div class="col-md-8">
                                <h4>${data.title}</h4>
                                <p class="text-muted mb-3">${data.description}</p>
                                
                                <div class="service-details-grid">
                                    <div class="detail-item">
                                        <strong>Venue:</strong> ${data.venue_name}
                                    </div>
                                    <div class="detail-item">
                                        <strong>Category:</strong> ${data.category}
                                    </div>
                                    <div class="detail-item">
                                        <strong>Duration:</strong> ${data.duration} minutes
                                    </div>
                                    <div class="detail-item">
                                        <strong>Price:</strong> $${data.price_min}${data.price_max && data.price_max !== data.price_min ? ` - $${data.price_max}` : ''}
                                    </div>
                                    ${data.amenities ? `
                                    <div class="detail-item">
                                        <strong>Amenities:</strong> ${data.amenities.join(', ')}
                                    </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    `;
                })
                .catch(error => {
                    console.error('Error loading service details:', error);
                    content.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Failed to load service details. Please try again.
                        </div>
                    `;
                });
        };
    }
    
    // Enhanced confirmation modal
    function setupRemoveConfirmation() {
        window.showRemoveConfirmation = function(serviceName) {
            const modal = new bootstrap.Modal(document.getElementById('removeConfirmationModal'));
            document.getElementById('removeItemName').textContent = serviceName;
            
            // Store the form that triggered this confirmation
            removeFormToSubmit = event.target.closest('form');
            
            modal.show();
            return false; // Prevent default form submission
        };
        
        // Handle confirmation
        const confirmBtn = document.getElementById('confirmRemoveBtn');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', function() {
                if (removeFormToSubmit) {
                    removeFormToSubmit.submit();
                }
                bootstrap.Modal.getInstance(document.getElementById('removeConfirmationModal')).hide();
            });
        }
    }
    
    // Enhanced checkout flow with slot locking
    function setupCheckoutFlow() {
        const checkoutBtn = document.querySelector('.btn-cw-primary[href*="checkout"]');
        if (checkoutBtn) {
            checkoutBtn.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Show loading state
                const originalText = this.innerHTML;
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Preparing checkout...';
                
                // Lock slots before proceeding to checkout
                lockCartSlots()
                    .then(() => {
                        // Validate availability one more time
                        return validateCartAvailability();
                    })
                    .then(isAvailable => {
                        if (isAvailable) {
                            // Proceed to checkout
                            window.location.href = this.getAttribute('href');
                        } else {
                            throw new Error('Some items are no longer available');
                        }
                    })
                    .catch(error => {
                        // Reset button and show error
                        this.disabled = false;
                        this.innerHTML = originalText;
                        showAvailabilityAlert(error.message, 'danger');
                        
                        // Refresh page to show updated availability
                        setTimeout(() => {
                            window.location.reload();
                        }, 3000);
                    });
            });
        }
    }
    
    // Real-time availability checking
    function setupRealTimeAvailability() {
        // Check availability every 30 seconds
        setInterval(validateCartAvailability, 30000);
        
        // Check when page becomes visible again
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                validateCartAvailability();
            }
        });
    }
    
    function validateCartAvailability() {
        return fetch('/bookings/api/validate-cart-availability/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({
                action: 'validate'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (!data.available && data.unavailable_items && data.unavailable_items.length > 0) {
                // Show warning about unavailable items
                showUnavailableItemsAlert(data.unavailable_items);
                return false;
            }
            return true;
        })
        .catch(error => {
            console.error('Error validating availability:', error);
            return true; // Don't block checkout on validation errors
        });
    }
    
    function lockCartSlots() {
        if (slotsLocked) {
            return Promise.resolve();
        }
        
        return fetch('/bookings/api/lock-cart-slots/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({
                action: 'lock'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                slotsLocked = true;
                showAvailabilityAlert(`Slots locked for ${Math.round(data.lock_expires_in / 60)} minutes`, 'success');
                
                // Set up automatic lock release when leaving page
                window.addEventListener('beforeunload', releaseCartSlots);
                
                return true;
            } else {
                throw new Error(data.error || 'Failed to lock slots');
            }
        });
    }
    
    function releaseCartSlots() {
        if (!slotsLocked) {
            return;
        }
        
        // Use sendBeacon for reliable delivery when leaving page
        const data = JSON.stringify({ action: 'release' });
        navigator.sendBeacon('/bookings/api/lock-cart-slots/', data);
    }
    
    function showUnavailableItemsAlert(unavailableItems) {
        const itemsList = unavailableItems
            .map(item => `• ${item.service_title} on ${item.date} at ${item.time}`)
            .join('<br>');
        
        const alert = createAlert(
            'warning',
            'Some Items Are No Longer Available',
            `The following services are no longer available and should be removed from your cart:<br><br>${itemsList}`,
            [
                {
                    text: 'Refresh Page',
                    class: 'btn-warning',
                    action: () => window.location.reload()
                }
            ]
        );
        
        document.body.appendChild(alert);
        
        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 10000);
    }
    
    function showAvailabilityAlert(message, type = 'info') {
        const alert = createAlert(type, type === 'success' ? 'Success' : 'Notice', message);
        document.body.appendChild(alert);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
    
    function createAlert(type, title, message, actions = []) {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        
        let actionsHtml = '';
        if (actions.length > 0) {
            actionsHtml = '<div class="mt-2">' + 
                actions.map(action => 
                    `<button type="button" class="btn btn-sm ${action.class}" onclick="(${action.action.toString()})()">${action.text}</button>`
                ).join(' ') + 
                '</div>';
        }
        
        alert.innerHTML = `
            <h6><i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>${title}</h6>
            <div>${message}</div>
            ${actionsHtml}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        return alert;
    }
    
    // Add loading states to cart actions
    function addCartLoadingStates() {
        document.querySelectorAll('.cart-item-actions form').forEach(form => {
            form.addEventListener('submit', function() {
                const button = this.querySelector('button');
                if (button) {
                    button.disabled = true;
                    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Removing...';
                }
            });
        });
    }
});
</script>
{% endblock %}
