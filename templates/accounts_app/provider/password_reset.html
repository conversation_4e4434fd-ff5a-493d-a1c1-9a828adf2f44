{% extends 'base.html' %}

{% block title %}Reset Password - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="{% static 'css/accounts_app/provider_password_reset.css' %}">
{% endblock %}

{% block content %}
<section class="password-reset-section">
    <div class="password-reset-container">
        <div class="password-reset-card">
            <div class="password-reset-header">
                <div class="content">
                    <div class="password-reset-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <h1 class="password-reset-title">Reset Business Password</h1>
                    <p class="password-reset-subtitle">We'll send password reset instructions to your business email address</p>
                </div>
            </div>

            <div class="password-reset-body">
                <!-- Show helpful message if email is pre-populated -->
                {% if request.GET.email %}
                <div class="alert-cw alert-cw-info mb-4" role="alert">
                  <i class="fas fa-info-circle me-2"></i>
                  <strong>Email pre-filled:</strong> We've filled in your email address from the login form. You can change it if needed.
                </div>
                {% endif %}

                <!-- Display messages -->
                {% if messages %}
                  {% for message in messages %}
                    <div class="alert-cw alert-cw-{{ message.tags }} mb-4" role="alert">
                      <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
                      {{ message }}
                    </div>
                  {% endfor %}
                {% endif %}

                <form method="post" novalidate>
                  {% csrf_token %}

                  {% if form.non_field_errors %}
                  <div class="alert-cw alert-cw-error mb-4">
                    {% for error in form.non_field_errors %}
                    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}

                  <!-- Email field -->
                  <div class="mb-4">
                    <label for="{{ form.email.id_for_label }}" class="form-label">
                      <i class="fas fa-envelope"></i>Business Email Address
                    </label>
                    {% if form.email.errors %}
                      {{ form.email|add_class:"form-control-cw is-invalid"|attr:"placeholder:<EMAIL>" }}
                    {% else %}
                      {{ form.email|add_class:"form-control-cw"|attr:"placeholder:<EMAIL>" }}
                    {% endif %}
                    {% if form.email.errors %}
                    <div class="invalid-feedback" role="alert" aria-live="polite">
                      {% for error in form.email.errors %}
                      <i class="fas fa-exclamation-circle"></i>{{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>

                  <!-- Submit button -->
                  <div class="d-grid mb-4">
                    <button type="submit" class="btn-cw-primary">
                      <i class="fas fa-paper-plane"></i>Send Reset Link
                    </button>
                  </div>
                </form>

                <!-- Back to login link -->
                <div class="text-center mb-4">
                  <p class="mb-0">
                    Remember your password?
                    <a href="{% url 'accounts_app:service_provider_login' %}">Sign in here</a>
                  </p>
                </div>

                <!-- Customer reset link -->
                <div class="text-center">
                  <p class="mb-0 small-note">
                    Looking for customer password reset?
                    <a href="{% url 'accounts_app:customer_password_reset' %}">Click here</a>
                  </p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
