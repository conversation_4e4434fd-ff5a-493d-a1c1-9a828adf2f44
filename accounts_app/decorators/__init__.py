"""
Decorators package for accounts_app.

This package provides decorators for authentication and permission control
across the accounts_app.
"""

# Import all decorators from auth module for easy access
from .auth import (
    customer_required,
    service_provider_required,
    admin_required,
    role_required,
    anonymous_required,
    require_active_user,
    get_user_home_url,
    get_user_login_url,
)

# Create aliases for backward compatibility with tests
login_required_customer = customer_required
login_required_provider = service_provider_required
login_required_admin = admin_required
account_active_required = require_active_user

# Import all mixins from auth module for easy access
from .auth import (
    CustomerRequiredMixin,
    ServiceProviderRequiredMixin,
    AdminRequiredMixin,
    RoleRequiredMixin,
    AnonymousRequiredMixin,
    ActiveUserRequiredMixin,
)

# Additional decorators that tests expect
from functools import wraps
from django.http import HttpResponse, JsonResponse
from django.core.exceptions import PermissionDenied
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_control as django_cache_control
from django.views.decorators.http import require_http_methods
from django.core.cache import cache
from ..models import CustomerProfile
import json
import time

def email_verified_required(view_func):
    """Decorator to require that the user's email is verified."""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return HttpResponse('Unauthorized', status=401, content_type='text/plain')
        
        if not request.user.email_verified:
            return HttpResponse('Email verification required', status=403, content_type='text/plain')
        
        return view_func(request, *args, **kwargs)
    return wrapper

def profile_complete_required(view_func):
    """Decorator to require that the user's profile is complete."""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return HttpResponse('Unauthorized', status=401, content_type='text/plain')
        
        # Check if profile exists and is complete
        try:
            profile = request.user.customer_profile
            if not profile or not profile.is_complete:
                return HttpResponse('Profile completion required', status=403, content_type='text/plain')
        except CustomerProfile.DoesNotExist:
            return HttpResponse('Profile completion required', status=403, content_type='text/plain')
        
        return view_func(request, *args, **kwargs)
    return wrapper

def rate_limit(limit=100, period=3600, by_ip=False):
    """Decorator to rate limit requests."""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Create a unique key for rate limiting
            if by_ip:
                key = f"rate_limit:{request.META.get('REMOTE_ADDR', 'unknown')}"
            else:
                key = f"rate_limit:{request.user.id if request.user.is_authenticated else 'anonymous'}"
            
            # Check current count
            current_count = cache.get(key, 0)
            
            if current_count >= limit:
                return HttpResponse('Rate limit exceeded', status=429, content_type='text/plain')
            
            # Increment count
            cache.set(key, current_count + 1, period)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator

def ajax_required(view_func):
    """Decorator to require AJAX requests."""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return HttpResponse('AJAX request required', status=400, content_type='text/plain')
        
        return view_func(request, *args, **kwargs)
    return wrapper

def json_response(view_func):
    """Decorator to automatically convert response to JSON."""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        try:
            response = view_func(request, *args, **kwargs)
            
            if isinstance(response, (dict, list)):
                return JsonResponse(response)
            elif isinstance(response, HttpResponse):
                return response
            else:
                return JsonResponse({'result': response})
                
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    
    return wrapper

def cache_control(**kwargs):
    """Decorator to set cache control headers."""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            response = view_func(request, *args, **kwargs)
            return django_cache_control(**kwargs)(lambda req: response)(request)
        return wrapper
    return decorator

def permission_required_custom(permission_func):
    """Decorator to require custom permission check."""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not permission_func(request.user):
                raise PermissionDenied
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator

def two_factor_required(view_func):
    """Decorator to require two-factor authentication."""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return HttpResponse('Unauthorized', status=401, content_type='text/plain')
        
        # For MVP, we'll allow all authenticated users to pass through
        # In a real implementation, you'd check 2FA verification status
        return view_func(request, *args, **kwargs)
    return wrapper

__all__ = [
    # Function-based decorators
    'customer_required',
    'service_provider_required',
    'admin_required',
    'role_required',
    'anonymous_required',
    'require_active_user',
    'email_verified_required',
    'profile_complete_required',
    'rate_limit',
    'ajax_required',
    'json_response',
    'cache_control',
    'permission_required_custom',
    'two_factor_required',
    
    # Class-based mixins
    'CustomerRequiredMixin',
    'ServiceProviderRequiredMixin',
    'AdminRequiredMixin',
    'RoleRequiredMixin',
    'AnonymousRequiredMixin',
    'ActiveUserRequiredMixin',
    
    # Utility functions
    'get_user_home_url',
    'get_user_login_url',
] 