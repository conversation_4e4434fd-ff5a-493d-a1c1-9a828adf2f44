"""
MVP Integration Tests for accounts_app

These tests focus on the core MVP functionality that is actually implemented
and available in the current codebase.
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from accounts_app.models import CustomerProfile, ServiceProviderProfile
from accounts_app.constants import UserRoles, UserStatus

User = get_user_model()


class MVPIntegrationTest(TestCase):
    """Test core MVP functionality for accounts_app."""
    
    def setUp(self):
        """Set up test data."""
        self.client.logout()
        
    def create_customer_user(self, **kwargs):
        """Create a test customer user."""
        defaults = {
            'email': '<EMAIL>',
            'password': 'TestPass123!',
            'role': UserRoles.CUSTOMER,
            'status': UserStatus.ACTIVE,
            'email_verified': True,
        }
        defaults.update(kwargs)
        
        user = User.objects.create_user(**defaults)
        CustomerProfile.objects.create(user=user)
        return user
    
    def create_provider_user(self, **kwargs):
        """Create a test service provider user."""
        defaults = {
            'email': '<EMAIL>',
            'password': 'TestPass123!',
            'role': UserRoles.SERVICE_PROVIDER,
            'status': UserStatus.ACTIVE,
            'email_verified': True,
        }
        defaults.update(kwargs)
        
        user = User.objects.create_user(**defaults)
        ServiceProviderProfile.objects.create(
            user=user,
            legal_name='Test Business',
            contact_name='Test Contact',
            address='123 Test St',
            city='Test City',
            state='CA',
            zip_code='90210',
            phone='+***********'
        )
        return user
    
    def test_customer_login_flow(self):
        """Test customer login functionality."""
        # Create a customer user
        user = self.create_customer_user()
        
        # Test login
        login_url = reverse('accounts_app:customer_login')
        response = self.client.post(login_url, {
            'email': '<EMAIL>',
            'password': 'TestPass123!'
        })
        
        # Should redirect to customer profile
        self.assertRedirects(response, reverse('accounts_app:customer_profile'))
        
        # User should be logged in
        self.assertTrue(self.client.session.get('_auth_user_id'))
    
    def test_provider_login_flow(self):
        """Test service provider login functionality."""
        # Create a provider user
        user = self.create_provider_user()
        
        # Test login (using customer login for now since provider login might not be implemented)
        login_url = reverse('accounts_app:customer_login')
        response = self.client.post(login_url, {
            'email': '<EMAIL>',
            'password': 'TestPass123!'
        })
        
        # Should redirect to provider profile
        self.assertRedirects(response, reverse('accounts_app:service_provider_profile'))
        
        # User should be logged in
        self.assertTrue(self.client.session.get('_auth_user_id'))
    
    def test_customer_profile_access(self):
        """Test customer profile page access."""
        user = self.create_customer_user()
        self.client.login(email='<EMAIL>', password='TestPass123!')
        
        # Test accessing customer profile
        profile_url = reverse('accounts_app:customer_profile')
        response = self.client.get(profile_url)
        
        self.assertEqual(response.status_code, 200)
    
    def test_provider_profile_access(self):
        """Test service provider profile page access."""
        user = self.create_provider_user()
        self.client.login(email='<EMAIL>', password='TestPass123!')
        
        # Test accessing provider profile
        profile_url = reverse('accounts_app:service_provider_profile')
        response = self.client.get(profile_url)
        
        self.assertEqual(response.status_code, 200)
    
    def test_customer_logout(self):
        """Test customer logout functionality."""
        user = self.create_customer_user()
        self.client.login(email='<EMAIL>', password='TestPass123!')
        
        # Test logout
        logout_url = reverse('accounts_app:customer_logout')
        response = self.client.get(logout_url)
        
        # Should redirect to login page
        self.assertRedirects(response, reverse('accounts_app:customer_login'))
        
        # User should be logged out
        self.assertNotIn('_auth_user_id', self.client.session)
    
    def test_unified_logout(self):
        """Test unified logout functionality."""
        user = self.create_customer_user()
        self.client.login(email='<EMAIL>', password='TestPass123!')
        
        # Test unified logout
        logout_url = reverse('accounts_app:unified_logout')
        response = self.client.get(logout_url)
        
        # Should redirect to home page
        self.assertRedirects(response, '/')
        
        # User should be logged out
        self.assertNotIn('_auth_user_id', self.client.session)
    
    def test_authentication_required_pages(self):
        """Test that protected pages require authentication."""
        # Test customer profile without login
        profile_url = reverse('accounts_app:customer_profile')
        response = self.client.get(profile_url)
        
        # Should redirect to login
        self.assertRedirects(response, reverse('accounts_app:customer_login'))
    
    def test_email_verification_page(self):
        """Test email verification page access."""
        # Test email verification page (should be accessible)
        verification_url = reverse('accounts_app:email_verification_sent')
        response = self.client.get(verification_url)
        
        self.assertEqual(response.status_code, 200)
    
    def test_business_landing_page(self):
        """Test business landing page access."""
        # Test business landing page
        business_url = reverse('accounts_app:for_business')
        response = self.client.get(business_url)
        
        self.assertEqual(response.status_code, 200)
    
    def test_customer_profile_edit_access(self):
        """Test customer profile edit page access."""
        user = self.create_customer_user()
        self.client.login(email='<EMAIL>', password='TestPass123!')
        
        # Test accessing customer profile edit
        edit_url = reverse('accounts_app:customer_profile_edit')
        response = self.client.get(edit_url)
        
        self.assertEqual(response.status_code, 200)
    
    def test_provider_profile_edit_access(self):
        """Test service provider profile edit page access."""
        user = self.create_provider_user()
        self.client.login(email='<EMAIL>', password='TestPass123!')
        
        # Test accessing provider profile edit
        edit_url = reverse('accounts_app:service_provider_profile_edit')
        response = self.client.get(edit_url)
        
        self.assertEqual(response.status_code, 200)
    
    def test_customer_password_change_access(self):
        """Test customer password change page access."""
        user = self.create_customer_user()
        self.client.login(email='<EMAIL>', password='TestPass123!')
        
        # Test accessing customer password change
        change_url = reverse('accounts_app:customer_change_password')
        response = self.client.get(change_url)
        
        self.assertEqual(response.status_code, 200)
    
    def test_provider_password_change_access(self):
        """Test service provider password change page access."""
        user = self.create_provider_user()
        self.client.login(email='<EMAIL>', password='TestPass123!')
        
        # Test accessing provider password change
        change_url = reverse('accounts_app:service_provider_change_password')
        response = self.client.get(change_url)
        
        self.assertEqual(response.status_code, 200) 