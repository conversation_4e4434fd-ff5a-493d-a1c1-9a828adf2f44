# --- Standard Library Imports ---
from datetime import timed<PERSON><PERSON>
from typing import Optional

# --- Django Imports ---
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from .auth import CustomUser
from ..constants import (
    SecuritySettings, DefaultValues, ModelConstants
)


class UserSecurity(models.Model):
    """
    Stores user security-related information.
    """
    user = models.OneToOneField(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='user_security',
        verbose_name=_('user account')
    )
    two_factor_method = models.CharField(
        _('two-factor method'),
        max_length=20,
        choices=SecuritySettings.TWO_FACTOR_CHOICES,
        default=DefaultValues.DEFAULT_TWO_FACTOR
    )
    backup_codes = models.JSONField(
        _('backup codes'),
        default=list,
        blank=True
    )
    last_password_change = models.DateTimeField(
        _('last password change'),
        default=timezone.now
    )
    failed_login_attempts = models.PositiveIntegerField(
        _('failed login attempts'),
        default=DefaultValues.DEFAULT_FAILED_ATTEMPTS
    )
    account_locked_until = models.DateTimeField(
        _('account locked until'),
        null=True,
        blank=True
    )
    last_activity = models.DateTimeField(
        _('last activity'),
        null=True,
        blank=True
    )
    session_key = models.CharField(
        _('session key'),
        max_length=40,
        blank=True
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('User Security')
        verbose_name_plural = _('User Security')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['last_password_change']),
            models.Index(fields=['failed_login_attempts']),
            models.Index(fields=['account_locked_until']),
            models.Index(fields=['last_activity']),
        ]

    def __str__(self):
        return f"{self.user.email} - Security"

    @property
    def is_password_expired(self) -> bool:
        """Check if password has expired"""
        expiry_date = self.last_password_change + timezone.timedelta(
            days=ModelConstants.PASSWORD_EXPIRY_DAYS
        )
        return timezone.now() > expiry_date

    @property
    def is_account_locked(self) -> bool:
        """Check if account is currently locked"""
        if not self.account_locked_until:
            return False
        return timezone.now() < self.account_locked_until

    @property
    def is_two_factor_enabled(self) -> bool:
        """Check if two-factor authentication is enabled"""
        return self.two_factor_method != SecuritySettings.TWO_FACTOR_DISABLED

    def lock_account(self, duration_minutes: int = None):
        """Lock the account for a specified duration"""
        if duration_minutes is None:
            duration_minutes = ModelConstants.LOCKOUT_DURATION_MINUTES
        
        self.account_locked_until = timezone.now() + timezone.timedelta(minutes=duration_minutes)
        self.save(update_fields=['account_locked_until'])

    def unlock_account(self):
        """Unlock the account"""
        self.account_locked_until = None
        self.failed_login_attempts = 0
        self.save(update_fields=['account_locked_until', 'failed_login_attempts'])

    def record_failed_login(self):
        """Record a failed login attempt"""
        self.failed_login_attempts += 1
        
        # Lock account if too many failed attempts
        if self.failed_login_attempts >= ModelConstants.MAX_LOGIN_ATTEMPTS:
            self.lock_account()
        
        self.save(update_fields=['failed_login_attempts'])

    def reset_failed_attempts(self):
        """Reset failed login attempts counter"""
        self.failed_login_attempts = 0
        self.save(update_fields=['failed_login_attempts'])

    def update_last_activity(self):
        """Update last activity timestamp"""
        self.last_activity = timezone.now()
        self.save(update_fields=['last_activity'])


class LoginHistory(models.Model):
    """Audit trail for authentication attempts with threat detection"""
    user = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='login_history'
    )
    timestamp = models.DateTimeField(_('timestamp'), auto_now_add=True)
    ip_address = models.GenericIPAddressField(_('IP address'))
    user_agent = models.TextField(_('user agent'), blank=True)
    is_successful = models.BooleanField(_('successful'), default=False)

    class Meta:
        verbose_name = _('Login History')
        verbose_name_plural = _('Login History Records')
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['timestamp'], name='login_timestamp_idx'),
            models.Index(fields=['ip_address'], name='login_ip_idx'),
            models.Index(fields=['is_successful'], name='login_success_idx'),
        ]

    def __str__(self):
        return f"{self.user.email} @ {self.timestamp}"

    @classmethod
    def purge_old_records(cls, days=90):
        """Remove records older than specified days"""
        cutoff = timezone.now() - timedelta(days=days)
        cls.objects.filter(timestamp__lt=cutoff).delete()

    @classmethod
    def detect_suspicious_activity(cls, ip_address, user=None):
        """Identify and alert on suspicious login patterns"""
        time_window = timezone.now() - timedelta(hours=1)
        failed_attempts = cls.objects.filter(
            ip_address=ip_address,
            is_successful=False,
            timestamp__gte=time_window
        ).count()
        
        # Threshold configuration
        thresholds = {
            5: LoginAlert.HIGH,
            3: LoginAlert.MEDIUM
        }
        
        for count, severity in thresholds.items():
            if failed_attempts >= count:
                LoginAlert.objects.get_or_create(
                    ip_address=ip_address,
                    user=user,
                    alert_type=LoginAlert.MULTIPLE_FAILURES,
                    defaults={
                        'severity': severity,
                        'description': f'{failed_attempts} failed attempts from {ip_address}',
                        'attempt_count': failed_attempts
                    }
                )
                return True
        return False


class LoginAlert(models.Model):
    """Security alert system for authentication anomalies"""
    # Alert Types
    MULTIPLE_FAILURES = 'multiple_failures'
    SUSPICIOUS_IP = 'suspicious_ip'
    UNUSUAL_LOCATION = 'unusual_location'
    BRUTE_FORCE = 'brute_force'
    
    ALERT_TYPES = (
        (MULTIPLE_FAILURES, _('Multiple Failed Attempts')),
        (SUSPICIOUS_IP, _('Suspicious IP Address')),
        (UNUSUAL_LOCATION, _('Unusual Login Location')),
        (BRUTE_FORCE, _('Brute Force Attack')),
    )
    
    # Severity Levels
    LOW = 'low'
    MEDIUM = 'medium'
    HIGH = 'high'
    CRITICAL = 'critical'
    
    SEVERITY_LEVELS = (
        (LOW, _('Low')),
        (MEDIUM, _('Medium')),
        (HIGH, _('High')),
        (CRITICAL, _('Critical')),
    )

    user = models.ForeignKey(
        CustomUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='alerts',
        verbose_name=_('associated user')
    )
    ip_address = models.GenericIPAddressField(_('source IP'))
    alert_type = models.CharField(_('type'), max_length=20, choices=ALERT_TYPES)
    severity = models.CharField(_('severity'), max_length=10, choices=SEVERITY_LEVELS, default=MEDIUM)
    description = models.TextField(_('details'))
    attempt_count = models.PositiveIntegerField(_('attempts'), default=1)
    is_resolved = models.BooleanField(_('resolved'), default=False)
    resolved_by = models.ForeignKey(
        CustomUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resolved_alerts',
        verbose_name=_('resolved by')
    )
    resolved_at = models.DateTimeField(_('resolution time'), null=True, blank=True)
    resolution_notes = models.TextField(_('resolution notes'), blank=True)
    created = models.DateTimeField(_('created at'), auto_now_add=True)
    updated = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Security Alert')
        verbose_name_plural = _('Security Alerts')
        ordering = ['-created']
        unique_together = ['ip_address', 'alert_type', 'is_resolved']

    def __str__(self):
        return f"{self.get_alert_type_display()} ({self.get_severity_display()})"

    def resolve(self, resolver, notes=''):
        """Mark alert as resolved with audit trail"""
        self.is_resolved = True
        self.resolved_by = resolver
        self.resolved_at = timezone.now()
        self.resolution_notes = notes
        self.save()

    @property
    def is_critical(self):
        """Check if alert requires immediate attention"""
        return self.severity in [self.HIGH, self.CRITICAL]

    @classmethod
    def unresolved_count(cls):
        """Count of pending alerts"""
        return cls.objects.filter(is_resolved=False).count()

    @classmethod
    def critical_count(cls):
        """Count of high-priority alerts"""
        return cls.objects.filter(
            is_resolved=False,
            severity__in=[cls.HIGH, cls.CRITICAL]
        ).count()


class PasswordHistory(models.Model):
    """
    Tracks password history for users to prevent password reuse.
    Stores hashed passwords to maintain security.
    """
    user = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='password_history',
        verbose_name=_('user')
    )
    password_hash = models.CharField(
        _('password hash'),
        max_length=128,
        help_text=_('Hashed password for comparison')
    )
    created_at = models.DateTimeField(
        _('created at'),
        auto_now_add=True
    )
    
    class Meta:
        verbose_name = _('Password History')
        verbose_name_plural = _('Password History Records')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at'], name='password_history_user_date_idx'),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"
    
    @classmethod
    def cleanup_old_passwords(cls, user, keep_count=5):
        """
        Remove old password history records, keeping only the most recent ones.
        
        :param user: User instance
        :param keep_count: Number of recent passwords to keep
        """
        old_passwords = cls.objects.filter(user=user).order_by('-created_at')[keep_count:]
        cls.objects.filter(id__in=[p.id for p in old_passwords]).delete()
    
    @classmethod
    def add_password(cls, user, password_hash):
        """
        Add a new password to history and clean up old ones.
        
        :param user: User instance
        :param password_hash: Hashed password
        """
        # Create new password history record
        cls.objects.create(user=user, password_hash=password_hash)
        
        # Clean up old passwords, keeping only the last 5
        cls.cleanup_old_passwords(user, keep_count=5)


class AccountLockout(models.Model):
    """
    Tracks failed login attempts and manages account lockouts.
    """
    user = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='lockout_records',
        verbose_name=_('user'),
        null=True,
        blank=True
    )
    ip_address = models.GenericIPAddressField(
        _('IP address'),
        help_text=_('IP address of the failed login attempt')
    )
    failed_attempts = models.PositiveIntegerField(
        _('failed attempts'),
        default=1
    )
    locked_until = models.DateTimeField(
        _('locked until'),
        null=True,
        blank=True,
        help_text=_('Account is locked until this time')
    )
    created_at = models.DateTimeField(
        _('created at'),
        auto_now_add=True
    )
    updated_at = models.DateTimeField(
        _('updated at'),
        auto_now=True
    )
    
    class Meta:
        verbose_name = _('Account Lockout')
        verbose_name_plural = _('Account Lockouts')
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['ip_address'], name='lockout_ip_idx'),
            models.Index(fields=['user'], name='lockout_user_idx'),
            models.Index(fields=['locked_until'], name='lockout_until_idx'),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['ip_address'],
                name='unique_ip_lockout',
                condition=models.Q(locked_until__isnull=False)
            )
        ]
    
    def __str__(self):
        user_info = self.user.email if self.user else 'Anonymous'
        return f"{user_info} @ {self.ip_address} - {self.failed_attempts} attempts"
    
    @property
    def is_locked(self):
        """Check if the account is currently locked"""
        if self.locked_until is None:
            return False
        return timezone.now() < self.locked_until
    
    def unlock(self):
        """Unlock the account and reset failed attempts"""
        self.locked_until = None
        self.failed_attempts = 0
        self.save()
    
    @classmethod
    def get_lockout_duration(cls, attempt_count):
        """
        Calculate lockout duration based on failed attempts.
        Progressive lockout: 5 min, 15 min, 30 min, 1 hour, 2 hours, etc.
        """
        if attempt_count <= 5:
            return timedelta(minutes=5)
        elif attempt_count <= 10:
            return timedelta(minutes=15)
        elif attempt_count <= 15:
            return timedelta(minutes=30)
        elif attempt_count <= 20:
            return timedelta(hours=1)
        else:
            return timedelta(hours=2)
    
    @classmethod
    def record_failed_attempt(cls, ip_address, user=None):
        """
        Record a failed login attempt and potentially lock the account.
        
        :param ip_address: IP address of the failed attempt
        :param user: User instance (optional)
        :return: AccountLockout instance
        """
        lockout_record, created = cls.objects.get_or_create(
            ip_address=ip_address,
            defaults={'user': user, 'failed_attempts': 1}
        )
        
        if not created:
            lockout_record.failed_attempts += 1
            lockout_record.user = user or lockout_record.user
            
            # Lock account after 5 failed attempts
            if lockout_record.failed_attempts >= 5:
                lockout_duration = cls.get_lockout_duration(lockout_record.failed_attempts)
                lockout_record.locked_until = timezone.now() + lockout_duration
            
            lockout_record.save()
        
        return lockout_record
    
    @classmethod
    def reset_failed_attempts(cls, ip_address):
        """Reset failed attempts for successful login"""
        cls.objects.filter(ip_address=ip_address).update(
            failed_attempts=0,
            locked_until=None
        )
    
    @classmethod
    def is_ip_locked(cls, ip_address):
        """Check if an IP address is currently locked"""
        try:
            lockout = cls.objects.get(ip_address=ip_address)
            return lockout.is_locked
        except cls.DoesNotExist:
            return False
    
    @classmethod
    def cleanup_old_records(cls, days=30):
        """Remove old lockout records"""
        cutoff = timezone.now() - timedelta(days=days)
        cls.objects.filter(
            created_at__lt=cutoff,
            locked_until__isnull=True
        ).delete() 