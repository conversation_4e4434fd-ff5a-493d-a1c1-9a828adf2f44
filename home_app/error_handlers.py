"""
Enhanced custom error handlers for CozyWish project.

This module provides comprehensive error handling views for all HTTP errors
with intelligent logging, user-friendly messages, and analytics tracking.
"""

import time
from django.shortcuts import render, redirect
from django.http import (
    HttpResponseNotFound, HttpResponseServerError, 
    HttpResponseForbidden, HttpResponseBadRequest,
    JsonResponse
)
from django.template import TemplateDoesNotExist
from django.conf import settings
from django.core.cache import cache
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.core.exceptions import ValidationError
from django.db import connection
from utils.logging_utils import log_error, log_security_event, log_performance
import logging
import traceback
import json
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)
User = get_user_model()


class ErrorContext:
    """Provides context-aware error handling based on user and request state."""
    
    def __init__(self, request):
        self.request = request
        self.user = getattr(request, 'user', None)
        self.is_authenticated = self.user and self.user.is_authenticated
        self.user_type = self._get_user_type()
        self.is_ajax = self._is_ajax_request()
        self.is_mobile = self._is_mobile_request()
        
    def _get_user_type(self) -> str:
        """Determine user type for context-aware messaging."""
        if not self.is_authenticated or not self.user:
            return 'anonymous'
        
        if hasattr(self.user, 'role') and self.user.role:
            return self.user.role
        
        if self.user.is_superuser:
            return 'superuser'
        elif self.user.is_staff:
            return 'staff'
        else:
            return 'customer'
    
    def _is_ajax_request(self) -> bool:
        """Check if request is AJAX."""
        return (
            self.request.META.get('HTTP_X_REQUESTED_WITH') == 'XMLHttpRequest' or
            self.request.content_type == 'application/json'
        )
    
    def _is_mobile_request(self) -> bool:
        """Check if request is from mobile device."""
        user_agent = self.request.META.get('HTTP_USER_AGENT', '').lower()
        mobile_agents = ['mobile', 'android', 'iphone', 'ipad', 'windows phone']
        return any(agent in user_agent for agent in mobile_agents)
    
    def get_contextual_message(self, error_type: str) -> Dict[str, str]:
        """Get context-aware error messages based on user type and request."""
        messages = {
            'anonymous': {
                '404': "The page you're looking for doesn't exist. Try browsing our venues or creating an account.",
                '403': "You need to log in to access this page. Sign up for free to explore CozyWish!",
                '400': "There was an issue with your request. Please try again or contact support.",
                '500': "Something went wrong on our end. Our team has been notified and will fix this soon."
            },
            'customer': {
                '404': "The page you're looking for doesn't exist. Check your bookings or browse venues.",
                '403': "This area is restricted. You might need provider privileges to access this page.",
                '400': "Please check your input and try again. All required fields must be filled correctly.",
                '500': "We're experiencing technical difficulties. Your data is safe and we're working on a fix."
            },
            'provider': {
                '404': "The page you're looking for doesn't exist. Check your provider dashboard or venue management.",
                '403': "You don't have permission to access this page. Contact admin if you need additional privileges.",
                '400': "Please verify your input data. All venue and booking information must be complete and valid.",
                '500': "System maintenance in progress. Your venues and bookings are secure while we resolve this."
            },
            'staff': {
                '404': "Admin page not found. Check the URL or navigate through the admin dashboard.",
                '403': "Insufficient admin privileges. Contact a superuser if you need access to this area.",
                '400': "Invalid admin request. Please verify all parameters and try again.",
                '500': "Admin system error. Check logs for details and contact technical support if needed."
            }
        }
        
        user_messages = messages.get(self.user_type, messages['anonymous'])
        return {
            'title': f"Error {error_type}",
            'message': user_messages.get(error_type, user_messages['500']),
            'user_type': self.user_type
        }


class ErrorRateLimiter:
    """Rate limiting for error pages to prevent spam and abuse."""
    
    @staticmethod
    def is_rate_limited(request, error_type: str, limit: int = 10, window: int = 60) -> bool:
        """Check if user has exceeded error rate limit."""
        if not request:
            return False
            
        # Get client identifier
        client_id = ErrorRateLimiter._get_client_id(request)
        cache_key = f"error_rate_limit:{error_type}:{client_id}"
        
        # Get current count
        current_count = cache.get(cache_key, 0)
        
        if current_count >= limit:
            return True
        
        # Increment count
        cache.set(cache_key, current_count + 1, window)
        return False
    
    @staticmethod
    def _get_client_id(request) -> str:
        """Get unique client identifier for rate limiting."""
        if hasattr(request, 'user') and request.user.is_authenticated:
            return f"user:{request.user.id}"
        
        # Get IP address
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', 'unknown')
        
        return f"ip:{ip}"


class ErrorAnalytics:
    """Track error patterns and analytics."""
    
    @staticmethod
    def track_error(request, error_type: str, context: ErrorContext):
        """Track error occurrence for analytics."""
        analytics_data = {
            'error_type': error_type,
            'timestamp': timezone.now().isoformat(),
            'user_type': context.user_type,
            'is_authenticated': context.is_authenticated,
            'is_ajax': context.is_ajax,
            'is_mobile': context.is_mobile,
            'path': request.path,
            'method': request.method,
            'referer': request.META.get('HTTP_REFERER', ''),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
        }
        
        # Store in cache for immediate analytics
        cache_key = f"error_analytics:{error_type}:{timezone.now().strftime('%Y-%m-%d-%H')}"
        current_count = cache.get(cache_key, 0)
        cache.set(cache_key, current_count + 1, 3600)  # 1 hour
        
        # Log for long-term storage
        log_error(
            app_name='home_app',
            error_type=f'http_{error_type}',
            error_message=f'HTTP {error_type} error tracked',
            user=context.user if context.is_authenticated else None,
            request=request,
            details=analytics_data
        )


def _handle_error_response(request, error_code: str, exception=None, 
                          template_name: Optional[str] = None, response_class=None):
    """Centralized error handling with enhanced features."""
    start_time = time.time()
    
    try:
        # Create error context
        context = ErrorContext(request)
        
        # Check rate limiting
        if ErrorRateLimiter.is_rate_limited(request, error_code):
            if context.is_ajax:
                return JsonResponse({
                    'error': 'Too many requests',
                    'message': 'Please wait before trying again.'
                }, status=429)
            else:
                return HttpResponseBadRequest(
                    b'<h1>Too Many Requests</h1>'
                    b'<p>Please wait before trying again.</p>'
                )
        
        # Track error analytics
        ErrorAnalytics.track_error(request, error_code, context)
        
        # Get contextual message
        error_info = context.get_contextual_message(error_code)
        
        # Handle AJAX requests
        if context.is_ajax:
            return JsonResponse({
                'error': error_code,
                'message': error_info['message'],
                'user_type': context.user_type,
                'support_email': getattr(settings, 'SUPPORT_EMAIL', '<EMAIL>')
            }, status=int(error_code))
        
        # Enhanced template context
        template_context = {
            'request_path': request.path,
            'support_email': getattr(settings, 'SUPPORT_EMAIL', '<EMAIL>'),
            'error_code': error_code,
            'error_title': error_info['title'],
            'error_message': error_info['message'],
            'user_type': context.user_type,
            'is_authenticated': context.is_authenticated,
            'is_mobile': context.is_mobile,
            'timestamp': timezone.now(),
            'debug': settings.DEBUG,
        }
        
        # Add helpful links based on user type
        if context.user_type == 'anonymous':
            template_context['helpful_links'] = [
                {'url': 'account_login', 'text': 'Login', 'icon': 'fas fa-sign-in-alt'},
                {'url': 'account_signup', 'text': 'Sign Up', 'icon': 'fas fa-user-plus'},
                {'url': 'venues_app:venue_search', 'text': 'Browse Venues', 'icon': 'fas fa-search'},
            ]
        elif context.user_type == 'customer':
            template_context['helpful_links'] = [
                {'url': 'dashboard_app:customer_dashboard', 'text': 'Dashboard', 'icon': 'fas fa-tachometer-alt'},
                {'url': 'booking_cart_app:booking_list', 'text': 'My Bookings', 'icon': 'fas fa-calendar'},
                {'url': 'venues_app:venue_search', 'text': 'Browse Venues', 'icon': 'fas fa-search'},
            ]
        elif context.user_type == 'provider':
            template_context['helpful_links'] = [
                {'url': 'dashboard_app:provider_dashboard', 'text': 'Provider Dashboard', 'icon': 'fas fa-tachometer-alt'},
                {'url': 'venues_app:venue_management', 'text': 'Manage Venues', 'icon': 'fas fa-building'},
                {'url': 'booking_cart_app:provider_bookings', 'text': 'Bookings', 'icon': 'fas fa-calendar-check'},
            ]
        
        # Log the error with performance metrics
        log_performance(
            app_name='home_app',
            operation=f'error_handler_{error_code}',
            duration=time.time() - start_time,
            user=context.user if context.is_authenticated else None,
            request=request,
            details={'error_code': error_code, 'template': template_name}
        )
        
        # Try to render template
        if template_name:
            try:
                response = render(request, template_name, template_context)
                response.status_code = int(error_code)
                return response
            except TemplateDoesNotExist:
                pass
        
        # Fallback response
        fallback_html = f"""
        <html>
        <head><title>Error {error_code} - CozyWish</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 2rem;">
            <h1>Error {error_code}</h1>
            <p>{error_info['message']}</p>
            <p><a href="/">Return to Home</a></p>
            <p>Contact us at <a href="mailto:{template_context['support_email']}">
               {template_context['support_email']}</a></p>
        </body>
        </html>
        """
        
        if response_class:
            return response_class(fallback_html.encode('utf-8'))
        else:
            return HttpResponseServerError(fallback_html.encode('utf-8'))
    
    except Exception as e:
        # Last resort error handling
        logger.error(f"Error in error handler: {str(e)}", exc_info=True)
        return HttpResponseServerError(
            b'<h1>System Error</h1>'
            b'<p>Multiple errors occurred. Please contact support.</p>'
        )


def custom_404_view(request, exception=None):
    """Enhanced 404 error handler with intelligent routing suggestions."""
    return _handle_error_response(
        request=request,
        error_code='404',
        exception=exception,
        template_name='home_app/404.html',
        response_class=HttpResponseNotFound
    )


def custom_500_view(request):
    """Enhanced 500 error handler with system status and recovery suggestions."""
    return _handle_error_response(
        request=request,
        error_code='500',
        template_name='home_app/500.html',
        response_class=HttpResponseServerError
    )


def custom_403_view(request, exception=None):
    """Enhanced 403 error handler with permission escalation guidance."""
    # Log security event for permission denied
    if hasattr(request, 'user') and request.user.is_authenticated:
        log_security_event(
            app_name='home_app',
            event_type='permission_denied',
            user_email=request.user.email,
            user_id=request.user.id,
            request=request,
            details={'attempted_path': request.path},
            severity='WARNING'
        )
    
    return _handle_error_response(
        request=request,
        error_code='403',
        exception=exception,
        template_name='home_app/403.html',
        response_class=HttpResponseForbidden
    )


def custom_400_view(request, exception=None):
    """Enhanced 400 error handler with input validation guidance."""
    return _handle_error_response(
        request=request,
        error_code='400',
        exception=exception,
        template_name='home_app/400.html',
        response_class=HttpResponseBadRequest
    )


@csrf_exempt
def custom_csrf_failure_view(request, reason=""):
    """Handle CSRF token failures with user-friendly guidance."""
    log_security_event(
        app_name='home_app',
        event_type='csrf_failure',
        user_email=request.user.email if hasattr(request, 'user') and request.user.is_authenticated else None,
        request=request,
        details={'reason': reason},
        severity='WARNING'
    )
    
    context = ErrorContext(request)
    
    if context.is_ajax:
        return JsonResponse({
            'error': 'csrf_failure',
            'message': 'Security token expired. Please refresh the page and try again.',
            'action': 'refresh_page'
        }, status=403)
    
    return render(request, 'home_app/403.html', {
        'error_title': 'Security Token Expired',
        'error_message': 'Your security token has expired. Please refresh the page and try again.',
        'csrf_error': True,
        'support_email': getattr(settings, 'SUPPORT_EMAIL', '<EMAIL>'),
    })


def handle_database_error(request, exception):
    """Handle database connection errors with appropriate messaging."""
    log_error(
        app_name='home_app',
        error_type='database_error',
        error_message=f'Database error: {str(exception)}',
        request=request,
        exception=exception,
        details={'db_vendor': connection.vendor if connection else 'unknown'}
    )
    
    context = ErrorContext(request)
    
    if context.is_ajax:
        return JsonResponse({
            'error': 'database_error',
            'message': 'Database temporarily unavailable. Please try again in a moment.',
            'retry_after': 30
        }, status=503)
    
    return render(request, 'home_app/500.html', {
        'error_title': 'Service Temporarily Unavailable',
        'error_message': 'Our database is temporarily unavailable. Please try again in a few moments.',
        'database_error': True,
        'support_email': getattr(settings, 'SUPPORT_EMAIL', '<EMAIL>'),
    })


def get_error_statistics():
    """Get error statistics for monitoring dashboard."""
    stats = {}
    current_hour = timezone.now().strftime('%Y-%m-%d-%H')
    
    error_types = ['404', '500', '403', '400']
    for error_type in error_types:
        cache_key = f"error_analytics:{error_type}:{current_hour}"
        stats[error_type] = cache.get(cache_key, 0)
    
    return stats 